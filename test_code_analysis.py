#!/usr/bin/env python3
"""
Code Analysis Test for Priority 1 Optimizations
Validates that the optimizations are properly implemented in the code
"""

import sys
import os
import re
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import_worker_optimization():
    """Test that ImportWorker artificial delays have been removed."""
    print("🔬 Testing ImportWorker Artificial Delay Removal")
    print("=" * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find ImportWorker class
        import_worker_start = content.find('class ImportWorker(QThread):')
        if import_worker_start == -1:
            print("❌ ImportWorker class not found")
            return False
        
        # Extract ImportWorker section (approximate)
        import_worker_end = content.find('\n        class ', import_worker_start + 1)
        if import_worker_end == -1:
            import_worker_end = content.find('\n    def ', import_worker_start + 1)
        if import_worker_end == -1:
            import_worker_end = len(content)
        
        import_worker_code = content[import_worker_start:import_worker_end]
        
        # Check for artificial delays
        delay_patterns = [
            'QThread.msleep(',
            'time.sleep(',
            'sleep(',
            'msleep(',
            'delay'
        ]
        
        delays_found = []
        for pattern in delay_patterns:
            if pattern in import_worker_code:
                delays_found.append(pattern)
        
        print(f"📊 ImportWorker code length: {len(import_worker_code)} characters")
        print(f"🔍 Artificial delay patterns checked: {len(delay_patterns)}")
        print(f"❌ Delays found: {len(delays_found)}")
        
        if delays_found:
            print("⚠️  Artificial delays detected:")
            for delay in delays_found:
                print(f"  - {delay}")
            return False
        else:
            print("✅ No artificial delays found - optimization confirmed!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_fluids_widget_optimization():
    """Test fluids widget N+1 query optimization."""
    print("\n🔬 Testing Fluids Widget N+1 Query Optimization")
    print("=" * 50)
    
    try:
        with open('ui/fluids_widget.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for optimized method
        has_optimized_method = 'load_equipment_data_optimized' in content
        
        # Check for optimization comments
        has_optimization_comments = 'optimized' in content.lower()
        
        # Count Equipment.get_active() calls
        equipment_get_active_count = content.count('Equipment.get_active()')
        
        # Check for legacy method redirection
        has_legacy_redirect = 'load_equipment_data_optimized()' in content
        
        print(f"📊 File size: {len(content)} characters")
        print(f"✅ Optimized method present: {'YES' if has_optimized_method else 'NO'}")
        print(f"📝 Optimization comments: {'YES' if has_optimization_comments else 'NO'}")
        print(f"🔍 Equipment.get_active() calls: {equipment_get_active_count}")
        print(f"🔄 Legacy method redirect: {'YES' if has_legacy_redirect else 'NO'}")
        
        # Optimization successful if:
        # 1. Optimized method exists
        # 2. Limited Equipment.get_active() calls (≤1 for backward compatibility)
        # 3. Has optimization comments
        optimization_successful = (
            has_optimized_method and
            equipment_get_active_count <= 1 and
            has_optimization_comments
        )
        
        if optimization_successful:
            print("✅ Fluids widget optimization confirmed!")
            return True
        else:
            print("❌ Fluids widget optimization incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_repairs_widget_optimization():
    """Test repairs widget async loading and query optimization."""
    print("\n🔬 Testing Repairs Widget Async Loading & Query Optimization")
    print("=" * 60)
    
    try:
        with open('ui/repairs_widget.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for async loading implementation
        has_async_loading = 'load_other_tabs_async' in content
        
        # Check for AsyncDataLoader usage
        has_async_data_loader = 'AsyncDataLoader' in content
        
        # Check for equipment data extraction optimization
        has_equipment_extraction = 'equipment_ids_seen' in content
        
        # Check for background loading methods
        has_background_methods = '_load_widgets_data' in content and '_on_background_tabs_loaded' in content
        
        # Count Equipment.get_active() calls
        equipment_get_active_count = content.count('Equipment.get_active()')
        
        print(f"📊 File size: {len(content)} characters")
        print(f"🔄 Async loading method: {'YES' if has_async_loading else 'NO'}")
        print(f"📦 AsyncDataLoader import: {'YES' if has_async_data_loader else 'NO'}")
        print(f"⚡ Equipment extraction optimized: {'YES' if has_equipment_extraction else 'NO'}")
        print(f"🔧 Background loading methods: {'YES' if has_background_methods else 'NO'}")
        print(f"🔍 Equipment.get_active() calls: {equipment_get_active_count}")
        
        # Optimization successful if:
        # 1. Async loading implemented
        # 2. AsyncDataLoader used
        # 3. Equipment extraction optimized
        # 4. Background methods present
        optimization_successful = (
            has_async_loading and
            has_async_data_loader and
            has_equipment_extraction and
            has_background_methods
        )
        
        if optimization_successful:
            print("✅ Repairs widget optimization confirmed!")
            return True
        else:
            print("❌ Repairs widget optimization incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_performance_optimizations_file():
    """Test that AsyncDataLoader exists in performance_optimizations.py."""
    print("\n🔬 Testing AsyncDataLoader Implementation")
    print("=" * 45)
    
    try:
        with open('performance_optimizations.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for AsyncDataLoader class
        has_async_data_loader = 'class AsyncDataLoader(QThread):' in content
        
        # Check for required methods
        has_run_method = 'def run(self):' in content
        has_signals = 'data_loaded = pyqtSignal' in content and 'error_occurred = pyqtSignal' in content
        
        print(f"📊 File size: {len(content)} characters")
        print(f"📦 AsyncDataLoader class: {'YES' if has_async_data_loader else 'NO'}")
        print(f"🔧 Run method: {'YES' if has_run_method else 'NO'}")
        print(f"📡 PyQt signals: {'YES' if has_signals else 'NO'}")
        
        optimization_available = has_async_data_loader and has_run_method and has_signals
        
        if optimization_available:
            print("✅ AsyncDataLoader implementation confirmed!")
            return True
        else:
            print("❌ AsyncDataLoader implementation incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_backward_compatibility():
    """Test that backward compatibility is maintained."""
    print("\n🔬 Testing Backward Compatibility")
    print("=" * 35)
    
    compatibility_issues = []
    
    # Test fluids_widget.py
    try:
        with open('ui/fluids_widget.py', 'r', encoding='utf-8') as f:
            fluids_content = f.read()
        
        # Check that legacy method still exists
        if 'def load_equipment_data(self):' not in fluids_content:
            compatibility_issues.append("fluids_widget.py: load_equipment_data method missing")
        
        # Check for redirect to optimized version
        if 'load_equipment_data_optimized()' not in fluids_content:
            compatibility_issues.append("fluids_widget.py: legacy method doesn't redirect to optimized version")
            
    except Exception as e:
        compatibility_issues.append(f"fluids_widget.py: {e}")
    
    # Test repairs_widget.py
    try:
        with open('ui/repairs_widget.py', 'r', encoding='utf-8') as f:
            repairs_content = f.read()
        
        # Check that load_data method still exists
        if 'def load_data(self):' not in repairs_content:
            compatibility_issues.append("repairs_widget.py: load_data method missing")
            
    except Exception as e:
        compatibility_issues.append(f"repairs_widget.py: {e}")
    
    print(f"🔍 Compatibility checks performed: 4")
    print(f"❌ Issues found: {len(compatibility_issues)}")
    
    if compatibility_issues:
        print("⚠️  Compatibility issues detected:")
        for issue in compatibility_issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ All backward compatibility checks passed!")
        return True

def main():
    """Run comprehensive code analysis tests."""
    print("🚀 Priority 1 Performance Optimization - Code Analysis Test Suite")
    print("=" * 75)
    print(f"Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    tests = [
        ("ImportWorker Optimization", test_import_worker_optimization),
        ("Fluids Widget Optimization", test_fluids_widget_optimization),
        ("Repairs Widget Optimization", test_repairs_widget_optimization),
        ("AsyncDataLoader Implementation", test_performance_optimizations_file),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*75)
    print("📋 TEST SUMMARY")
    print("="*75)
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if passed:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"\nOverall Result: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🏆 EXCELLENT: All Priority 1 optimizations successfully implemented!")
        print("   ✅ Artificial delays removed from ImportWorker")
        print("   ✅ N+1 query patterns eliminated in fluids widget")
        print("   ✅ Async loading implemented in repairs widget")
        print("   ✅ AsyncDataLoader framework available")
        print("   ✅ Backward compatibility maintained")
    elif success_rate >= 80:
        print("\n✅ GOOD: Most optimizations implemented successfully")
        print("   ⚠️  Minor issues detected - review failed tests")
    else:
        print("\n⚠️  WARNING: Significant optimization issues detected")
        print("   ❌ Review implementation and address failed tests")
    
    print(f"\nTest completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*75)
    
    return success_rate == 100

if __name__ == "__main__":
    main()
