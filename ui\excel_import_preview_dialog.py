"""
Excel Import Preview Dialog for PROJECT-ALPHA

Provides comprehensive preview and conflict resolution for Excel imports:
1. Data preview with statistics and validation
2. BA number conflict detection and resolution
3. Side-by-side comparison dialogs
4. Integration with existing import pipeline

Follows established UI patterns from crud_dialogs.py and responsive_crud_framework.py
"""

import pandas as pd
import sqlite3
import logging
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QGroupBox, QTextEdit, QSplitter, QHeaderView,
    QMessageBox, QProgressBar, QFrame, QScrollArea, QWidget, QApplication,
    QTabWidget, QFormLayout, QCheckBox, QComboBox, QSpinBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import Q<PERSON>ont, QPalette, QColor

# Import existing UI utilities and styles
from ui.window_utils import DPIScaler, FormManager, LayoutManager, DialogManager
from ui.common_styles import (
    BUTTON_STYLE, PRIMARY_BUTTON_STYLE, DANGER_BUTTON_STYLE, SUCCESS_BUTTON_STYLE,
    TABLE_STYLE, FORM_GROUP_STYLE, INPUT_FIELD_STYLE, LAYOUT_MARGINS, LAYOUT_SPACING
)
import config

logger = logging.getLogger('excel_import_preview')

class ExcelDataAnalyzer:
    """Analyzes Excel data using the same logic as robust_excel_importer_working.py"""
    
    def __init__(self):
        self.session_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def analyze_excel_file(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze Excel file and return preview data with conflict detection.
        Uses exact same logic as robust_excel_importer_working.py for consistency.
        """
        logger.info(f"Starting Excel file analysis: {file_path}")
        
        try:
            # Import robust importer to use its exact logic
            from robust_excel_importer_working import RobustExcelImporter
            
            # Create temporary importer instance for analysis
            analyzer = RobustExcelImporter()
            
            # Use pandas to read file structure
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            analysis_result = {
                'file_path': file_path,
                'session_id': self.session_id,
                'sheets': sheet_names,
                'preview_data': [],
                'statistics': {
                    'total_sheets': len(sheet_names),
                    'total_equipment_rows': 0,
                    'total_fluid_rows': 0,
                    'total_maintenance_rows': 0,
                    'valid_ba_numbers': 0,
                    'invalid_rows': 0,
                    'potential_conflicts': 0
                },
                'conflicts': [],
                'validation_errors': [],
                'success': True
            }
            
            # Analyze each sheet using robust importer logic
            for sheet_name in sheet_names:
                sheet_analysis = self._analyze_sheet_with_robust_logic(
                    analyzer, excel_file, sheet_name
                )
                if sheet_analysis:
                    analysis_result['preview_data'].append(sheet_analysis)
                    
                    # Update statistics
                    stats = sheet_analysis.get('statistics', {})
                    analysis_result['statistics']['total_equipment_rows'] += stats.get('equipment_rows', 0)
                    analysis_result['statistics']['total_fluid_rows'] += stats.get('fluid_rows', 0)
                    analysis_result['statistics']['total_maintenance_rows'] += stats.get('maintenance_rows', 0)
                    analysis_result['statistics']['valid_ba_numbers'] += stats.get('valid_ba_numbers', 0)
                    analysis_result['statistics']['invalid_rows'] += stats.get('invalid_rows', 0)
            
            # Detect BA number conflicts with existing database
            conflicts = self._detect_ba_conflicts(analysis_result['preview_data'])
            analysis_result['conflicts'] = conflicts
            analysis_result['statistics']['potential_conflicts'] = len(conflicts)
            
            logger.info(f"Excel analysis completed: {analysis_result['statistics']}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing Excel file: {e}")
            return {
                'file_path': file_path,
                'session_id': self.session_id,
                'success': False,
                'error': str(e),
                'preview_data': [],
                'statistics': {},
                'conflicts': [],
                'validation_errors': [str(e)]
            }
    
    def _analyze_sheet_with_robust_logic(self, analyzer, excel_file: pd.ExcelFile, 
                                       sheet_name: str) -> Optional[Dict[str, Any]]:
        """Analyze a single sheet using robust importer's exact logic."""
        try:
            # Use robust importer's header detection
            df = analyzer._read_sheet_with_headers(excel_file, sheet_name)
            
            if df is None or len(df) == 0:
                logger.warning(f"Could not read or empty sheet: {sheet_name}")
                return None
            
            # Use robust importer's column mapping
            col_map = analyzer._map_equipment_columns(df.columns)
            
            # Extract preview data (limit to first 20 rows for performance)
            preview_rows = []
            equipment_rows = 0
            valid_ba_numbers = 0
            invalid_rows = 0
            
            max_preview_rows = min(20, len(df))
            
            for index, row in df.head(max_preview_rows).iterrows():
                try:
                    # Use robust importer's data extraction
                    equipment_data = analyzer._extract_equipment_data(row, col_map, sheet_name)
                    
                    if equipment_data and equipment_data.get('make_and_type'):
                        # Use robust importer's validation
                        if analyzer._is_valid_equipment_record(equipment_data):
                            preview_rows.append({
                                'row_index': index,
                                'ba_number': equipment_data.get('ba_number', ''),
                                'make_and_type': equipment_data.get('make_and_type', ''),
                                'serial_number': equipment_data.get('serial_number', ''),
                                'meterage_kms': equipment_data.get('meterage_kms', 0),
                                'hours_run_total': equipment_data.get('hours_run_total', 0),
                                'vintage_years': equipment_data.get('vintage_years', 0),
                                'units_held': equipment_data.get('units_held', 1),
                                'is_valid': True,
                                'raw_data': equipment_data
                            })
                            equipment_rows += 1
                            if equipment_data.get('ba_number'):
                                valid_ba_numbers += 1
                        else:
                            invalid_rows += 1
                    else:
                        invalid_rows += 1
                        
                except Exception as e:
                    logger.warning(f"Error processing row {index} in {sheet_name}: {e}")
                    invalid_rows += 1
            
            return {
                'sheet_name': sheet_name,
                'total_rows': len(df),
                'preview_rows': preview_rows,
                'columns': df.columns.tolist(),
                'column_mapping': col_map,
                'statistics': {
                    'equipment_rows': equipment_rows,
                    'fluid_rows': 0,  # Will be calculated separately if needed
                    'maintenance_rows': 0,  # Will be calculated separately if needed
                    'valid_ba_numbers': valid_ba_numbers,
                    'invalid_rows': invalid_rows
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sheet {sheet_name}: {e}")
            return None
    
    def _detect_ba_conflicts(self, preview_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect BA number conflicts with existing database records."""
        conflicts = []
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Get all existing BA numbers and their associated data
            cursor.execute('''
                SELECT equipment_id, ba_number, make_and_type, serial_number, 
                       meterage_kms, hours_run_total, vintage_years, units_held,
                       date_of_commission, is_active, remarks
                FROM equipment 
                WHERE ba_number IS NOT NULL AND ba_number != ''
            ''')
            
            existing_equipment = {}
            for row in cursor.fetchall():
                existing_equipment[row[1]] = {
                    'equipment_id': row[0],
                    'ba_number': row[1],
                    'make_and_type': row[2],
                    'serial_number': row[3],
                    'meterage_kms': row[4],
                    'hours_run_total': row[5],
                    'vintage_years': row[6],
                    'units_held': row[7],
                    'date_of_commission': row[8],
                    'is_active': row[9],
                    'remarks': row[10]
                }
            
            conn.close()
            
            # Check each preview row for conflicts
            for sheet_data in preview_data:
                for row_data in sheet_data.get('preview_rows', []):
                    ba_number = row_data.get('ba_number', '').strip()
                    
                    if ba_number and ba_number in existing_equipment:
                        existing_data = existing_equipment[ba_number]
                        new_data = row_data.get('raw_data', {})
                        
                        # Create conflict record
                        conflict = {
                            'ba_number': ba_number,
                            'sheet_name': sheet_data['sheet_name'],
                            'row_index': row_data['row_index'],
                            'existing_data': existing_data,
                            'new_data': new_data,
                            'differences': self._find_data_differences(existing_data, new_data)
                        }
                        
                        conflicts.append(conflict)
                        logger.info(f"Detected BA number conflict: {ba_number}")
            
            logger.info(f"Found {len(conflicts)} BA number conflicts")
            return conflicts
            
        except Exception as e:
            logger.error(f"Error detecting BA conflicts: {e}")
            return []
    
    def _find_data_differences(self, existing: Dict[str, Any], new: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find differences between existing and new equipment data."""
        differences = []
        
        # Compare key fields
        compare_fields = [
            ('make_and_type', 'Equipment Type'),
            ('serial_number', 'Serial Number'),
            ('meterage_kms', 'Meterage (KM)'),
            ('hours_run_total', 'Hours Run Total'),
            ('vintage_years', 'Vintage Years'),
            ('units_held', 'Units Held'),
            ('date_of_commission', 'Commission Date')
        ]
        
        for field, display_name in compare_fields:
            existing_val = existing.get(field, '')
            new_val = new.get(field, '')
            
            # Convert to strings for comparison
            existing_str = str(existing_val) if existing_val is not None else ''
            new_str = str(new_val) if new_val is not None else ''
            
            if existing_str != new_str:
                differences.append({
                    'field': field,
                    'display_name': display_name,
                    'existing_value': existing_str,
                    'new_value': new_str
                })
        
        return differences


class ImportPreviewDialog(QDialog):
    """
    Main import preview dialog showing data preview, statistics, and conflict summary.
    Follows ResponsiveFormDialog pattern from crud_dialogs.py
    """

    # Signals
    import_confirmed = pyqtSignal(dict)  # analysis_data
    import_cancelled = pyqtSignal()

    def __init__(self, analysis_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.analysis_data = analysis_data
        self.conflicts_resolved = {}  # Track resolved conflicts

        self.setup_dialog()
        self.setup_ui()
        self.populate_data()

    def setup_dialog(self):
        """Setup dialog with responsive sizing following established patterns."""
        self.setWindowTitle("Excel Import Preview")
        self.setModal(True)

        # Use DialogManager for responsive sizing (from window_utils.py)
        DialogManager.setup_responsive_dialog(
            self,
            width_percent=0.8,
            height_percent=0.8,
            min_width=800,
            min_height=600
        )

        # Main layout with standard margins
        self.main_layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(self.main_layout, margins=LAYOUT_MARGINS, spacing=LAYOUT_SPACING)

    def setup_ui(self):
        """Setup the UI components."""
        # Header section
        self.create_header_section()

        # Main content area with tabs
        self.create_content_tabs()

        # Button section
        self.create_button_section()

        # Apply consistent styling
        self.apply_styling()

        # Initialize button states
        self.update_import_button_state()
        self.update_accept_all_button_state()
        self.update_skip_all_button_state()

    def create_header_section(self):
        """Create header with file info and summary statistics."""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QVBoxLayout(header_frame)
        LayoutManager.setup_responsive_layout(header_layout)

        # File info
        file_path = self.analysis_data.get('file_path', 'Unknown')
        file_label = QLabel(f"<b>File:</b> {file_path}")
        file_label.setFont(DPIScaler.create_scaled_font(12))
        header_layout.addWidget(file_label)

        # Statistics summary
        stats = self.analysis_data.get('statistics', {})
        stats_layout = QHBoxLayout()

        # Create statistics labels
        stats_info = [
            ("Sheets", stats.get('total_sheets', 0)),
            ("Equipment Rows", stats.get('total_equipment_rows', 0)),
            ("Valid BA Numbers", stats.get('valid_ba_numbers', 0)),
            ("Potential Conflicts", stats.get('potential_conflicts', 0)),
            ("Invalid Rows", stats.get('invalid_rows', 0))
        ]

        for label_text, value in stats_info:
            stat_label = QLabel(f"<b>{label_text}:</b> {value}")
            stat_label.setFont(DPIScaler.create_scaled_font(10))
            stats_layout.addWidget(stat_label)

        stats_layout.addStretch()
        header_layout.addLayout(stats_layout)

        # Conflict warning if any
        if stats.get('potential_conflicts', 0) > 0:
            warning_label = QLabel(f"⚠️ {stats['potential_conflicts']} BA number conflicts detected - review required")
            warning_label.setStyleSheet("color: #dc3545; font-weight: bold; padding: 5px;")
            header_layout.addWidget(warning_label)

        self.main_layout.addWidget(header_frame)

    def create_content_tabs(self):
        """Create tabbed content area for preview and conflicts."""
        self.tab_widget = QTabWidget()

        # Data Preview Tab
        self.create_preview_tab()

        # Conflicts Tab (if any conflicts exist)
        if self.analysis_data.get('conflicts'):
            self.create_conflicts_tab()

        # Validation Tab (if any validation errors)
        if self.analysis_data.get('validation_errors'):
            self.create_validation_tab()

        self.main_layout.addWidget(self.tab_widget)

    def create_preview_tab(self):
        """Create data preview tab showing sample data from each sheet."""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        LayoutManager.setup_responsive_layout(preview_layout)

        # Create scroll area for sheet previews
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # Add preview for each sheet
        for sheet_data in self.analysis_data.get('preview_data', []):
            sheet_group = self.create_sheet_preview_group(sheet_data)
            scroll_layout.addWidget(sheet_group)

        scroll_area.setWidget(scroll_content)
        preview_layout.addWidget(scroll_area)

        self.tab_widget.addTab(preview_widget, "Data Preview")

    def create_sheet_preview_group(self, sheet_data: Dict[str, Any]) -> QGroupBox:
        """Create preview group for a single sheet."""
        sheet_name = sheet_data.get('sheet_name', 'Unknown Sheet')
        group = QGroupBox(f"Sheet: {sheet_name}")
        group.setStyleSheet(FORM_GROUP_STYLE)

        layout = QVBoxLayout(group)
        LayoutManager.setup_responsive_layout(layout)

        # Sheet statistics
        stats = sheet_data.get('statistics', {})
        stats_text = (f"Total Rows: {sheet_data.get('total_rows', 0)} | "
                     f"Equipment: {stats.get('equipment_rows', 0)} | "
                     f"Valid BA Numbers: {stats.get('valid_ba_numbers', 0)} | "
                     f"Invalid: {stats.get('invalid_rows', 0)}")

        stats_label = QLabel(stats_text)
        stats_label.setFont(DPIScaler.create_scaled_font(9))
        layout.addWidget(stats_label)

        # Preview table
        preview_table = QTableWidget()
        preview_table.setStyleSheet(TABLE_STYLE)

        # Setup table with preview data
        preview_rows = sheet_data.get('preview_rows', [])
        if preview_rows:
            # Define columns to show
            columns = ['Row', 'BA Number', 'Equipment Type', 'Serial Number', 'Meterage (KM)', 'Hours Run']
            preview_table.setColumnCount(len(columns))
            preview_table.setHorizontalHeaderLabels(columns)
            preview_table.setRowCount(len(preview_rows))

            # Populate table
            for row_idx, row_data in enumerate(preview_rows):
                preview_table.setItem(row_idx, 0, QTableWidgetItem(str(row_data.get('row_index', ''))))
                preview_table.setItem(row_idx, 1, QTableWidgetItem(str(row_data.get('ba_number', ''))))
                preview_table.setItem(row_idx, 2, QTableWidgetItem(str(row_data.get('make_and_type', ''))))
                preview_table.setItem(row_idx, 3, QTableWidgetItem(str(row_data.get('serial_number', ''))))
                preview_table.setItem(row_idx, 4, QTableWidgetItem(str(row_data.get('meterage_kms', ''))))
                preview_table.setItem(row_idx, 5, QTableWidgetItem(str(row_data.get('hours_run_total', ''))))

            # Configure table appearance
            preview_table.horizontalHeader().setStretchLastSection(True)
            preview_table.setAlternatingRowColors(True)
            preview_table.setSelectionBehavior(QTableWidget.SelectRows)
            preview_table.setEditTriggers(QTableWidget.NoEditTriggers)
            preview_table.setMaximumHeight(200)  # Limit height

        else:
            preview_table.setRowCount(1)
            preview_table.setColumnCount(1)
            preview_table.setItem(0, 0, QTableWidgetItem("No valid data found in this sheet"))
            preview_table.setMaximumHeight(60)

        layout.addWidget(preview_table)

        return group

    def create_conflicts_tab(self):
        """Create conflicts tab for BA number conflicts."""
        conflicts_widget = QWidget()
        conflicts_layout = QVBoxLayout(conflicts_widget)
        LayoutManager.setup_responsive_layout(conflicts_layout)

        # Conflicts summary
        conflicts = self.analysis_data.get('conflicts', [])
        summary_label = QLabel(f"Found {len(conflicts)} BA number conflicts that require resolution:")
        summary_label.setFont(DPIScaler.create_scaled_font(11, bold=True))
        summary_label.setStyleSheet("color: #dc3545; padding: 10px;")
        conflicts_layout.addWidget(summary_label)

        # Conflicts table
        self.conflicts_table = QTableWidget()
        self.conflicts_table.setStyleSheet(TABLE_STYLE)

        if conflicts:
            columns = ['BA Number', 'Sheet', 'Row', 'Equipment Type (New)', 'Equipment Type (Existing)', 'Action']
            self.conflicts_table.setColumnCount(len(columns))
            self.conflicts_table.setHorizontalHeaderLabels(columns)
            self.conflicts_table.setRowCount(len(conflicts))

            for row_idx, conflict in enumerate(conflicts):
                ba_number = conflict.get('ba_number', '')
                sheet_name = conflict.get('sheet_name', '')
                row_index = str(conflict.get('row_index', ''))
                new_type = conflict.get('new_data', {}).get('make_and_type', '')
                existing_type = conflict.get('existing_data', {}).get('make_and_type', '')

                self.conflicts_table.setItem(row_idx, 0, QTableWidgetItem(ba_number))
                self.conflicts_table.setItem(row_idx, 1, QTableWidgetItem(sheet_name))
                self.conflicts_table.setItem(row_idx, 2, QTableWidgetItem(row_index))
                self.conflicts_table.setItem(row_idx, 3, QTableWidgetItem(new_type))
                self.conflicts_table.setItem(row_idx, 4, QTableWidgetItem(existing_type))

                # Action button
                resolve_btn = QPushButton("Resolve")
                resolve_btn.setStyleSheet(PRIMARY_BUTTON_STYLE)
                resolve_btn.clicked.connect(lambda checked, c=conflict: self.resolve_conflict(c))
                self.conflicts_table.setCellWidget(row_idx, 5, resolve_btn)

            # Configure table
            self.conflicts_table.horizontalHeader().setStretchLastSection(True)
            self.conflicts_table.setAlternatingRowColors(True)
            self.conflicts_table.setSelectionBehavior(QTableWidget.SelectRows)
            self.conflicts_table.setEditTriggers(QTableWidget.NoEditTriggers)

        else:
            self.conflicts_table.setRowCount(1)
            self.conflicts_table.setColumnCount(1)
            self.conflicts_table.setItem(0, 0, QTableWidgetItem("No conflicts detected"))

        conflicts_layout.addWidget(self.conflicts_table)

        # Resolution status
        self.resolution_status_label = QLabel("Conflicts must be resolved before import can proceed.")
        self.resolution_status_label.setStyleSheet("color: #dc3545; font-weight: bold; padding: 10px;")
        conflicts_layout.addWidget(self.resolution_status_label)

        self.tab_widget.addTab(conflicts_widget, f"Conflicts ({len(conflicts)})")

    def create_validation_tab(self):
        """Create validation tab for validation errors."""
        validation_widget = QWidget()
        validation_layout = QVBoxLayout(validation_widget)
        LayoutManager.setup_responsive_layout(validation_layout)

        # Validation errors
        errors = self.analysis_data.get('validation_errors', [])

        errors_text = QTextEdit()
        errors_text.setReadOnly(True)
        errors_text.setStyleSheet(INPUT_FIELD_STYLE)

        if errors:
            error_content = "Validation Errors Found:\n\n"
            for i, error in enumerate(errors, 1):
                error_content += f"{i}. {error}\n"
        else:
            error_content = "No validation errors found."

        errors_text.setPlainText(error_content)
        validation_layout.addWidget(errors_text)

        self.tab_widget.addTab(validation_widget, f"Validation ({len(errors)})")

    def create_button_section(self):
        """Create button section with import/cancel options."""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        LayoutManager.setup_responsive_layout(button_layout)

        # Help text
        help_label = QLabel("Review the data preview and resolve any conflicts before proceeding with import.")
        help_label.setFont(DPIScaler.create_scaled_font(9))
        help_label.setStyleSheet("color: #6c757d;")
        button_layout.addWidget(help_label)

        button_layout.addStretch()

        # Cancel button
        self.cancel_btn = QPushButton("Cancel Import")
        self.cancel_btn.setStyleSheet(BUTTON_STYLE)
        self.cancel_btn.clicked.connect(self.cancel_import)
        button_layout.addWidget(self.cancel_btn)

        # Accept All button for bulk resolution
        self.accept_all_btn = QPushButton("Accept All Conflicts")
        self.accept_all_btn.setStyleSheet(SUCCESS_BUTTON_STYLE)
        self.accept_all_btn.clicked.connect(self.accept_all_conflicts)
        self.accept_all_btn.setToolTip("Accept all conflicts - update database with new Excel data for all conflicting records")
        button_layout.addWidget(self.accept_all_btn)

        # Skip All button for bulk resolution
        self.skip_all_btn = QPushButton("Skip All Conflicts")
        self.skip_all_btn.setStyleSheet(DANGER_BUTTON_STYLE)
        self.skip_all_btn.clicked.connect(self.skip_all_conflicts)
        self.skip_all_btn.setToolTip("Skip all conflicts - keep existing database data for all conflicting records")
        button_layout.addWidget(self.skip_all_btn)

        # Import button
        self.import_btn = QPushButton("Proceed with Import")
        self.import_btn.setStyleSheet(PRIMARY_BUTTON_STYLE)
        self.import_btn.clicked.connect(self.confirm_import)
        button_layout.addWidget(self.import_btn)

        # Initially disable import if there are unresolved conflicts
        if self.analysis_data.get('conflicts'):
            self.import_btn.setEnabled(False)
            self.import_btn.setText("Resolve Conflicts First")

        self.main_layout.addWidget(button_frame)

    def apply_styling(self):
        """Apply consistent styling to the dialog."""
        # Set dialog background
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: #ffffff;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: 1px solid #ffffff;
            }
        """)

    def populate_data(self):
        """Populate dialog with analysis data."""
        # Data is populated during UI creation
        pass

    def resolve_conflict(self, conflict: Dict[str, Any]):
        """Open conflict resolution dialog for a specific conflict."""
        resolution_dialog = ConflictResolutionDialog(conflict, self)

        if resolution_dialog.exec_() == QDialog.Accepted:
            resolution = resolution_dialog.get_resolution()
            ba_number = conflict.get('ba_number')

            # Store resolution in analysis_data
            if 'conflict_resolutions' not in self.analysis_data:
                self.analysis_data['conflict_resolutions'] = {}

            self.analysis_data['conflict_resolutions'][ba_number] = resolution

            # Update UI to reflect resolution
            self.update_conflicts_table()
            self.update_import_button_state()
            self.update_accept_all_button_state()
            self.update_skip_all_button_state()

    def update_conflict_resolution_status(self):
        """Update the conflict resolution status and enable/disable import button."""
        total_conflicts = len(self.analysis_data.get('conflicts', []))
        resolved_conflicts = len(self.conflicts_resolved)

        if resolved_conflicts == total_conflicts and total_conflicts > 0:
            self.resolution_status_label.setText(f"All {total_conflicts} conflicts resolved. Ready to import.")
            self.resolution_status_label.setStyleSheet("color: #28a745; font-weight: bold; padding: 10px;")
            self.import_btn.setEnabled(True)
            self.import_btn.setText("Proceed with Import")
        elif resolved_conflicts > 0:
            remaining = total_conflicts - resolved_conflicts
            self.resolution_status_label.setText(f"{resolved_conflicts}/{total_conflicts} conflicts resolved. {remaining} remaining.")
            self.resolution_status_label.setStyleSheet("color: #ffc107; font-weight: bold; padding: 10px;")

    def accept_all_conflicts(self):
        """Accept all conflicts - update database with new Excel data for all conflicting records."""
        conflicts = self.analysis_data.get('conflicts', [])

        if not conflicts:
            return

        # Show confirmation dialog
        from PyQt5.QtWidgets import QMessageBox

        reply = QMessageBox.question(
            self,
            "Accept All Conflicts",
            f"Are you sure you want to accept all {len(conflicts)} conflicts?\n\n"
            "This will update the database with new Excel data for all conflicting BA numbers.\n"
            "Existing data will be overwritten.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Set all conflicts to "accept"
            if 'conflict_resolutions' not in self.analysis_data:
                self.analysis_data['conflict_resolutions'] = {}

            for conflict in conflicts:
                ba_number = conflict['ba_number']
                self.analysis_data['conflict_resolutions'][ba_number] = 'accept'

            # Update the conflicts table to show all as resolved
            self.update_conflicts_table()

            # Update button states
            self.update_import_button_state()
            self.update_accept_all_button_state()
            self.update_skip_all_button_state()

            # Show success message
            QMessageBox.information(
                self,
                "Conflicts Resolved",
                f"All {len(conflicts)} conflicts have been set to 'Accept'.\n"
                "You can now proceed with the import.",
                QMessageBox.StandardButton.Ok
            )

    def skip_all_conflicts(self):
        """Skip all conflicts - keep existing database data for all conflicting records."""
        conflicts = self.analysis_data.get('conflicts', [])

        if not conflicts:
            return

        # Show confirmation dialog
        from PyQt5.QtWidgets import QMessageBox

        reply = QMessageBox.question(
            self,
            "Skip All Conflicts",
            f"Are you sure you want to skip all {len(conflicts)} conflicts?\n\n"
            "This will keep the existing database data for all conflicting BA numbers.\n"
            "New Excel data will be ignored for these records.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Set all conflicts to "skip"
            if 'conflict_resolutions' not in self.analysis_data:
                self.analysis_data['conflict_resolutions'] = {}

            for conflict in conflicts:
                ba_number = conflict['ba_number']
                self.analysis_data['conflict_resolutions'][ba_number] = 'skip'

            # Update the conflicts table to show all as resolved
            self.update_conflicts_table()

            # Update button states
            self.update_import_button_state()
            self.update_accept_all_button_state()
            self.update_skip_all_button_state()

            # Show success message
            QMessageBox.information(
                self,
                "Conflicts Resolved",
                f"All {len(conflicts)} conflicts have been set to 'Skip'.\n"
                "You can now proceed with the import.",
                QMessageBox.StandardButton.Ok
            )

    def confirm_import(self):
        """Confirm and proceed with import."""
        # Final confirmation dialog
        conflicts_count = len(self.analysis_data.get('conflicts', []))
        equipment_count = self.analysis_data.get('statistics', {}).get('total_equipment_rows', 0)

        if conflicts_count > 0:
            message = (f"Proceed with import?\n\n"
                      f"• {equipment_count} equipment records will be imported\n"
                      f"• {conflicts_count} conflicts have been resolved\n"
                      f"• Existing data will be updated according to your conflict resolutions")
        else:
            message = (f"Proceed with import?\n\n"
                      f"• {equipment_count} equipment records will be imported\n"
                      f"• No conflicts detected")

        reply = QMessageBox.question(
            self,
            "Confirm Import",
            message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Add conflict resolutions to analysis data
            self.analysis_data['conflict_resolutions'] = self.conflicts_resolved
            self.import_confirmed.emit(self.analysis_data)
            self.accept()

    def cancel_import(self):
        """Cancel the import process."""
        self.import_cancelled.emit()
        self.reject()

    def update_import_button_state(self):
        """Update the import button state based on conflict resolution status."""
        conflicts = self.analysis_data.get('conflicts', [])
        conflict_resolutions = self.analysis_data.get('conflict_resolutions', {})

        if conflicts:
            unresolved_conflicts = [c for c in conflicts if c['ba_number'] not in conflict_resolutions]
            if unresolved_conflicts:
                self.import_btn.setEnabled(False)
                self.import_btn.setText(f"Resolve {len(unresolved_conflicts)} Conflicts First")
            else:
                self.import_btn.setEnabled(True)
                self.import_btn.setText("Proceed with Import")
        else:
            self.import_btn.setEnabled(True)
            self.import_btn.setText("Proceed with Import")

    def update_accept_all_button_state(self):
        """Update the Accept All button state based on conflict resolution status."""
        conflicts = self.analysis_data.get('conflicts', [])
        conflict_resolutions = self.analysis_data.get('conflict_resolutions', {})

        if conflicts:
            unresolved_conflicts = [c for c in conflicts if c['ba_number'] not in conflict_resolutions]
            if unresolved_conflicts:
                self.accept_all_btn.setEnabled(True)
                self.accept_all_btn.setText(f"Accept All {len(unresolved_conflicts)} Conflicts")
            else:
                self.accept_all_btn.setEnabled(False)
                self.accept_all_btn.setText("All Conflicts Resolved")
        else:
            self.accept_all_btn.setVisible(False)

    def update_skip_all_button_state(self):
        """Update the Skip All button state based on conflict resolution status."""
        conflicts = self.analysis_data.get('conflicts', [])
        conflict_resolutions = self.analysis_data.get('conflict_resolutions', {})

        if conflicts:
            unresolved_conflicts = [c for c in conflicts if c['ba_number'] not in conflict_resolutions]
            if unresolved_conflicts:
                self.skip_all_btn.setEnabled(True)
                self.skip_all_btn.setText(f"Skip All {len(unresolved_conflicts)} Conflicts")
            else:
                self.skip_all_btn.setEnabled(False)
                self.skip_all_btn.setText("All Conflicts Resolved")
        else:
            self.skip_all_btn.setVisible(False)

    def update_conflicts_table(self):
        """Update the conflicts table to reflect current resolution status."""
        conflicts = self.analysis_data.get('conflicts', [])
        conflict_resolutions = self.analysis_data.get('conflict_resolutions', {})

        if not conflicts:
            return

        # Update action buttons/status for each conflict
        for row_idx, conflict in enumerate(conflicts):
            ba_number = conflict.get('ba_number', '')

            if ba_number in conflict_resolutions:
                resolution = conflict_resolutions[ba_number]

                # Replace the resolve button with status text
                if resolution == 'accept':
                    status_label = QLabel("✓ Accept")
                    status_label.setStyleSheet("color: #28a745; font-weight: bold; padding: 5px;")
                elif resolution == 'skip':
                    status_label = QLabel("✗ Skip")
                    status_label.setStyleSheet("color: #dc3545; font-weight: bold; padding: 5px;")
                else:
                    status_label = QLabel(f"? {resolution}")
                    status_label.setStyleSheet("color: #6c757d; font-weight: bold; padding: 5px;")

                self.conflicts_table.setCellWidget(row_idx, 5, status_label)
            else:
                # Keep the resolve button
                resolve_btn = QPushButton("Resolve")
                resolve_btn.setStyleSheet(PRIMARY_BUTTON_STYLE)
                resolve_btn.clicked.connect(lambda checked, c=conflict: self.resolve_conflict(c))
                self.conflicts_table.setCellWidget(row_idx, 5, resolve_btn)

        # Update resolution status label
        unresolved_conflicts = [c for c in conflicts if c['ba_number'] not in conflict_resolutions]
        if unresolved_conflicts:
            self.resolution_status_label.setText(f"{len(unresolved_conflicts)} conflicts remaining. Resolve all conflicts before proceeding.")
            self.resolution_status_label.setStyleSheet("color: #dc3545; font-weight: bold; padding: 10px;")
        else:
            self.resolution_status_label.setText("All conflicts resolved! You can now proceed with the import.")
            self.resolution_status_label.setStyleSheet("color: #28a745; font-weight: bold; padding: 10px;")


class ConflictResolutionDialog(QDialog):
    """
    Side-by-side conflict resolution dialog for BA number conflicts.
    Follows ResponsiveFormDialog pattern with Accept/Skip options.
    """

    def __init__(self, conflict_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.conflict_data = conflict_data
        self.resolution = None

        self.setup_dialog()
        self.setup_ui()
        self.populate_conflict_data()

    def setup_dialog(self):
        """Setup dialog with responsive sizing."""
        ba_number = self.conflict_data.get('ba_number', 'Unknown')
        self.setWindowTitle(f"Resolve Conflict - BA Number: {ba_number}")
        self.setModal(True)

        # Responsive sizing
        DialogManager.setup_responsive_dialog(
            self,
            width_percent=0.7,
            height_percent=0.6,
            min_width=700,
            min_height=500
        )

        # Main layout
        self.main_layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(self.main_layout, margins=LAYOUT_MARGINS, spacing=LAYOUT_SPACING)

    def setup_ui(self):
        """Setup the UI components."""
        # Header
        self.create_header()

        # Side-by-side comparison
        self.create_comparison_section()

        # Differences highlight
        self.create_differences_section()

        # Resolution buttons
        self.create_resolution_buttons()

        # Apply styling
        self.apply_styling()

    def create_header(self):
        """Create header with conflict information."""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QVBoxLayout(header_frame)
        LayoutManager.setup_responsive_layout(header_layout)

        # Title
        ba_number = self.conflict_data.get('ba_number', 'Unknown')
        title_label = QLabel(f"<h3>BA Number Conflict: {ba_number}</h3>")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        # Conflict description
        sheet_name = self.conflict_data.get('sheet_name', 'Unknown')
        row_index = self.conflict_data.get('row_index', 'Unknown')
        desc_label = QLabel(f"This BA number already exists in the database. "
                           f"Conflict found in sheet '{sheet_name}', row {row_index}.")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #6c757d; padding: 5px;")
        header_layout.addWidget(desc_label)

        self.main_layout.addWidget(header_frame)

    def create_comparison_section(self):
        """Create side-by-side comparison of existing vs new data."""
        comparison_frame = QFrame()
        comparison_frame.setFrameStyle(QFrame.StyledPanel)
        comparison_layout = QHBoxLayout(comparison_frame)
        LayoutManager.setup_responsive_layout(comparison_layout)

        # Existing data section
        existing_group = QGroupBox("Existing Data (Database)")
        existing_group.setStyleSheet(FORM_GROUP_STYLE + "QGroupBox { color: #dc3545; font-weight: bold; }")
        existing_layout = QFormLayout(existing_group)

        existing_data = self.conflict_data.get('existing_data', {})
        self.populate_data_form(existing_layout, existing_data)

        # New data section
        new_group = QGroupBox("New Data (Excel Import)")
        new_group.setStyleSheet(FORM_GROUP_STYLE + "QGroupBox { color: #28a745; font-weight: bold; }")
        new_layout = QFormLayout(new_group)

        new_data = self.conflict_data.get('new_data', {})
        self.populate_data_form(new_layout, new_data)

        # Add to comparison layout
        comparison_layout.addWidget(existing_group)
        comparison_layout.addWidget(new_group)

        self.main_layout.addWidget(comparison_frame)

    def populate_data_form(self, layout: QFormLayout, data: Dict[str, Any]):
        """Populate a form layout with equipment data."""
        # Define fields to display
        display_fields = [
            ('make_and_type', 'Equipment Type'),
            ('serial_number', 'Serial Number'),
            ('meterage_kms', 'Meterage (KM)'),
            ('hours_run_total', 'Hours Run Total'),
            ('vintage_years', 'Vintage Years'),
            ('units_held', 'Units Held'),
            ('date_of_commission', 'Commission Date'),
            ('is_active', 'Active Status'),
            ('remarks', 'Remarks')
        ]

        for field_name, display_name in display_fields:
            value = data.get(field_name, '')

            # Format value for display
            if value is None:
                display_value = 'Not Set'
            elif isinstance(value, bool):
                display_value = 'Yes' if value else 'No'
            elif isinstance(value, (int, float)) and value == 0:
                display_value = '0'
            else:
                display_value = str(value)

            # Create label with value
            value_label = QLabel(display_value)
            value_label.setStyleSheet(INPUT_FIELD_STYLE + "QLabel { background-color: #f8f9fa; padding: 5px; }")
            value_label.setWordWrap(True)

            layout.addRow(f"{display_name}:", value_label)

    def create_differences_section(self):
        """Create section highlighting the differences between records."""
        differences = self.conflict_data.get('differences', [])

        if differences:
            diff_group = QGroupBox("Differences Detected")
            diff_group.setStyleSheet(FORM_GROUP_STYLE)
            diff_layout = QVBoxLayout(diff_group)
            LayoutManager.setup_responsive_layout(diff_layout)

            diff_text = QTextEdit()
            diff_text.setReadOnly(True)
            diff_text.setMaximumHeight(120)
            diff_text.setStyleSheet(INPUT_FIELD_STYLE)

            diff_content = "The following fields have different values:\n\n"
            for diff in differences:
                field_name = diff.get('display_name', diff.get('field', ''))
                existing_val = diff.get('existing_value', '')
                new_val = diff.get('new_value', '')

                diff_content += f"• {field_name}:\n"
                diff_content += f"  Database: {existing_val}\n"
                diff_content += f"  Excel:    {new_val}\n\n"

            diff_text.setPlainText(diff_content)
            diff_layout.addWidget(diff_text)

            self.main_layout.addWidget(diff_group)
        else:
            # No differences detected (shouldn't happen, but handle gracefully)
            no_diff_label = QLabel("No differences detected in key fields.")
            no_diff_label.setStyleSheet("color: #28a745; font-style: italic; padding: 10px;")
            no_diff_label.setAlignment(Qt.AlignCenter)
            self.main_layout.addWidget(no_diff_label)

    def create_resolution_buttons(self):
        """Create resolution buttons with clear options."""
        button_frame = QFrame()
        button_layout = QVBoxLayout(button_frame)
        LayoutManager.setup_responsive_layout(button_layout)

        # Instructions
        instruction_label = QLabel("Choose how to resolve this conflict:")
        instruction_label.setFont(DPIScaler.create_scaled_font(11, bold=True))
        instruction_label.setAlignment(Qt.AlignCenter)
        button_layout.addWidget(instruction_label)

        # Button layout
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        # Skip button (keep existing data)
        self.skip_btn = QPushButton("Skip - Keep Existing Data")
        self.skip_btn.setStyleSheet(BUTTON_STYLE)
        self.skip_btn.setToolTip("Skip importing this record and keep the existing database entry unchanged")
        self.skip_btn.clicked.connect(self.skip_record)
        buttons_layout.addWidget(self.skip_btn)

        # Accept button (overwrite with new data)
        self.accept_btn = QPushButton("Accept - Update with New Data")
        self.accept_btn.setStyleSheet(PRIMARY_BUTTON_STYLE)
        self.accept_btn.setToolTip("Update the existing database entry with the new data from Excel")
        self.accept_btn.clicked.connect(self.accept_record)
        buttons_layout.addWidget(self.accept_btn)

        buttons_layout.addStretch()
        button_layout.addLayout(buttons_layout)

        self.main_layout.addWidget(button_frame)

    def apply_styling(self):
        """Apply consistent styling to the dialog."""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

    def populate_conflict_data(self):
        """Populate dialog with conflict data."""
        # Data is populated during UI creation
        pass

    def skip_record(self):
        """Skip this record (keep existing data)."""
        self.resolution = {
            'action': 'skip',
            'ba_number': self.conflict_data.get('ba_number'),
            'description': 'Keep existing database record unchanged'
        }
        self.accept()

    def accept_record(self):
        """Accept new record (overwrite existing data)."""
        self.resolution = {
            'action': 'accept',
            'ba_number': self.conflict_data.get('ba_number'),
            'new_data': self.conflict_data.get('new_data'),
            'description': 'Update database record with new Excel data'
        }
        self.accept()

    def get_resolution(self) -> Optional[Dict[str, Any]]:
        """Get the conflict resolution decision."""
        return self.resolution


class SimpleConfirmationDialog(QDialog):
    """
    Simple confirmation dialog for non-conflicting imports.
    Shows summary and asks for final confirmation.
    """

    def __init__(self, analysis_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.analysis_data = analysis_data

        self.setup_dialog()
        self.setup_ui()

    def setup_dialog(self):
        """Setup dialog with responsive sizing."""
        self.setWindowTitle("Confirm Excel Import")
        self.setModal(True)

        # Responsive sizing
        DialogManager.setup_responsive_dialog(
            self,
            width_percent=0.5,
            height_percent=0.4,
            min_width=500,
            min_height=300
        )

        # Main layout
        self.main_layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(self.main_layout, margins=LAYOUT_MARGINS, spacing=LAYOUT_SPACING)

    def setup_ui(self):
        """Setup the UI components."""
        # Header
        header_label = QLabel("<h3>Confirm Excel Import</h3>")
        header_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(header_label)

        # File info
        file_path = self.analysis_data.get('file_path', 'Unknown')
        file_label = QLabel(f"<b>File:</b> {file_path}")
        file_label.setWordWrap(True)
        self.main_layout.addWidget(file_label)

        # Summary
        stats = self.analysis_data.get('statistics', {})
        summary_text = f"""
<b>Import Summary:</b>
• {stats.get('total_equipment_rows', 0)} equipment records will be imported
• {stats.get('valid_ba_numbers', 0)} records have valid BA numbers
• No conflicts detected - all records are new or will be safely imported

<b>Ready to proceed?</b>
        """.strip()

        summary_label = QLabel(summary_text)
        summary_label.setWordWrap(True)
        summary_label.setStyleSheet("padding: 20px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;")
        self.main_layout.addWidget(summary_label)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("Cancel")
        cancel_btn.setStyleSheet(BUTTON_STYLE)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        confirm_btn = QPushButton("Confirm Import")
        confirm_btn.setStyleSheet(SUCCESS_BUTTON_STYLE)
        confirm_btn.clicked.connect(self.accept)
        button_layout.addWidget(confirm_btn)

        self.main_layout.addLayout(button_layout)

        # Apply styling
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
        """)


class SimpleConfirmationDialog(QDialog):
    """
    Simple confirmation dialog for non-conflicting imports.
    Shows summary and asks for final confirmation.
    """

    def __init__(self, analysis_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.analysis_data = analysis_data

        self.setup_dialog()
        self.setup_ui()

    def setup_dialog(self):
        """Setup dialog with responsive sizing."""
        self.setWindowTitle("Confirm Excel Import")
        self.setModal(True)

        # Responsive sizing
        DialogManager.setup_responsive_dialog(
            self,
            width_percent=0.5,
            height_percent=0.4,
            min_width=500,
            min_height=300
        )

        # Main layout
        self.main_layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(self.main_layout, margins=LAYOUT_MARGINS, spacing=LAYOUT_SPACING)

    def setup_ui(self):
        """Setup the UI components."""
        # Header
        header_label = QLabel("<h3>Confirm Excel Import</h3>")
        header_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(header_label)

        # File info
        file_path = self.analysis_data.get('file_path', 'Unknown')
        file_label = QLabel(f"<b>File:</b> {file_path}")
        file_label.setWordWrap(True)
        self.main_layout.addWidget(file_label)

        # Summary
        stats = self.analysis_data.get('statistics', {})
        summary_text = f"""
<b>Import Summary:</b>
• {stats.get('total_equipment_rows', 0)} equipment records will be imported
• {stats.get('valid_ba_numbers', 0)} records have valid BA numbers
• No conflicts detected - all records are new or will be safely imported

<b>Ready to proceed?</b>
        """.strip()

        summary_label = QLabel(summary_text)
        summary_label.setWordWrap(True)
        summary_label.setStyleSheet("padding: 20px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;")
        self.main_layout.addWidget(summary_label)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("Cancel")
        cancel_btn.setStyleSheet(BUTTON_STYLE)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        confirm_btn = QPushButton("Confirm Import")
        confirm_btn.setStyleSheet(SUCCESS_BUTTON_STYLE)
        confirm_btn.clicked.connect(self.accept)
        button_layout.addWidget(confirm_btn)

        self.main_layout.addLayout(button_layout)

        # Apply styling
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
        """)
