"""
Permission Decorators for PROJECT-ALPHA
Provides method-level access control through decorators.
"""

import logging
from functools import wraps
from typing import Callable, Any, Optional
from PyQt5.QtWidgets import QMessageBox, QWidget

logger = logging.getLogger(__name__)


def requires_permission(permission: str, show_message: bool = True):
    """
    Decorator that requires specific permission to execute method.
    
    Args:
        permission: Required permission string (e.g., 'write', 'admin', 'delete')
        show_message: Whether to show access denied message to user
        
    Usage:
        @requires_permission('write')
        def save_record(self):
            # Method implementation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get session manager from widget
            session_manager = getattr(self, 'session_manager', None)
            
            if not session_manager:
                logger.error(f"No session_manager found in {self.__class__.__name__} for permission check")
                if show_message and isinstance(self, QWidget):
                    QMessageBox.critical(
                        self,
                        "Authentication Error",
                        "Authentication system not properly initialized."
                    )
                return None
            
            # Check permission
            if not session_manager.has_permission(permission):
                current_user = session_manager.get_current_user()
                username = current_user.username if current_user else "Unknown"
                
                logger.warning(f"Access denied: User '{username}' lacks '{permission}' permission for {func.__name__}")
                
                if show_message and isinstance(self, QWidget):
                    QMessageBox.warning(
                        self,
                        "Access Denied",
                        f"You do not have permission to perform this action.\n\n"
                        f"Required permission: {permission.title()}\n"
                        f"Your role: {current_user.role_name if current_user else 'Unknown'}"
                    )
                return None
            
            # Execute method if permission granted
            return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


def requires_write_access(show_message: bool = True):
    """
    Decorator that requires write access to execute method.
    Shorthand for @requires_permission('write').
    
    Args:
        show_message: Whether to show access denied message to user
    """
    return requires_permission('write', show_message)


def requires_admin_access(show_message: bool = True):
    """
    Decorator that requires admin access to execute method.
    Shorthand for @requires_permission('admin').
    
    Args:
        show_message: Whether to show access denied message to user
    """
    return requires_permission('admin', show_message)


def requires_delete_access(show_message: bool = True):
    """
    Decorator that requires delete access to execute method.
    Shorthand for @requires_permission('delete').
    
    Args:
        show_message: Whether to show access denied message to user
    """
    return requires_permission('delete', show_message)


def read_only_disabled(show_message: bool = True):
    """
    Decorator that disables method execution in read-only mode.
    
    Args:
        show_message: Whether to show read-only message to user
        
    Usage:
        @read_only_disabled()
        def edit_record(self):
            # Method implementation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get session manager from widget
            session_manager = getattr(self, 'session_manager', None)
            
            if not session_manager:
                logger.error(f"No session_manager found in {self.__class__.__name__} for read-only check")
                if show_message and isinstance(self, QWidget):
                    QMessageBox.critical(
                        self,
                        "Authentication Error",
                        "Authentication system not properly initialized."
                    )
                return None
            
            # Check if in read-only mode
            if session_manager.is_read_only():
                current_user = session_manager.get_current_user()
                username = current_user.username if current_user else "Unknown"
                
                logger.info(f"Read-only mode: User '{username}' attempted to execute {func.__name__}")
                
                if show_message and isinstance(self, QWidget):
                    QMessageBox.information(
                        self,
                        "Read-Only Mode",
                        f"This action is not available in read-only mode.\n\n"
                        f"Your role: {current_user.role_name if current_user else 'Unknown'}\n"
                        f"Contact an administrator to request write access."
                    )
                return None
            
            # Execute method if not in read-only mode
            return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


def log_user_action(action_type: str, table_name: str = None):
    """
    Decorator that logs user actions to audit trail.
    
    Args:
        action_type: Type of action being performed (e.g., 'CREATE', 'UPDATE', 'DELETE')
        table_name: Database table being affected (optional)
        
    Usage:
        @log_user_action('UPDATE', 'equipment')
        def save_equipment(self, equipment_id):
            # Method implementation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get session manager from widget
            session_manager = getattr(self, 'session_manager', None)
            
            if session_manager:
                current_user = session_manager.get_current_user()
                username = current_user.username if current_user else "Unknown"
                
                # Log action start
                logger.info(f"User '{username}' executing {action_type} on {table_name or 'unknown'} via {func.__name__}")
            
            try:
                # Execute method
                result = func(self, *args, **kwargs)
                
                # Log successful completion
                if session_manager:
                    session_manager._log_activity(
                        action_type,
                        table_name or func.__name__,
                        str(args[0]) if args else 'unknown',
                        {'method': func.__name__, 'success': True}
                    )
                
                return result
                
            except Exception as e:
                # Log error
                if session_manager:
                    session_manager._log_activity(
                        action_type,
                        table_name or func.__name__,
                        str(args[0]) if args else 'unknown',
                        {'method': func.__name__, 'success': False, 'error': str(e)}
                    )
                
                logger.error(f"Error in {func.__name__}: {e}")
                raise
        
        return wrapper
    return decorator


def requires_any_permission(*permissions: str, show_message: bool = True):
    """
    Decorator that requires ANY of the specified permissions to execute method.
    
    Args:
        permissions: List of permission strings (user needs at least one)
        show_message: Whether to show access denied message to user
        
    Usage:
        @requires_any_permission('write', 'admin')
        def save_record(self):
            # Method implementation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get session manager from widget
            session_manager = getattr(self, 'session_manager', None)
            
            if not session_manager:
                logger.error(f"No session_manager found in {self.__class__.__name__} for permission check")
                if show_message and isinstance(self, QWidget):
                    QMessageBox.critical(
                        self,
                        "Authentication Error",
                        "Authentication system not properly initialized."
                    )
                return None
            
            # Check if user has any of the required permissions
            has_permission = any(session_manager.has_permission(perm) for perm in permissions)
            
            if not has_permission:
                current_user = session_manager.get_current_user()
                username = current_user.username if current_user else "Unknown"
                
                logger.warning(f"Access denied: User '{username}' lacks any of {permissions} permissions for {func.__name__}")
                
                if show_message and isinstance(self, QWidget):
                    QMessageBox.warning(
                        self,
                        "Access Denied",
                        f"You do not have permission to perform this action.\n\n"
                        f"Required permissions: {', '.join(permissions)}\n"
                        f"Your role: {current_user.role_name if current_user else 'Unknown'}"
                    )
                return None
            
            # Execute method if any permission granted
            return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


def requires_all_permissions(*permissions: str, show_message: bool = True):
    """
    Decorator that requires ALL of the specified permissions to execute method.
    
    Args:
        permissions: List of permission strings (user needs all of them)
        show_message: Whether to show access denied message to user
        
    Usage:
        @requires_all_permissions('write', 'admin')
        def delete_user(self):
            # Method implementation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get session manager from widget
            session_manager = getattr(self, 'session_manager', None)
            
            if not session_manager:
                logger.error(f"No session_manager found in {self.__class__.__name__} for permission check")
                if show_message and isinstance(self, QWidget):
                    QMessageBox.critical(
                        self,
                        "Authentication Error",
                        "Authentication system not properly initialized."
                    )
                return None
            
            # Check if user has all required permissions
            missing_permissions = [perm for perm in permissions if not session_manager.has_permission(perm)]
            
            if missing_permissions:
                current_user = session_manager.get_current_user()
                username = current_user.username if current_user else "Unknown"
                
                logger.warning(f"Access denied: User '{username}' lacks {missing_permissions} permissions for {func.__name__}")
                
                if show_message and isinstance(self, QWidget):
                    QMessageBox.warning(
                        self,
                        "Access Denied",
                        f"You do not have permission to perform this action.\n\n"
                        f"Missing permissions: {', '.join(missing_permissions)}\n"
                        f"Your role: {current_user.role_name if current_user else 'Unknown'}"
                    )
                return None
            
            # Execute method if all permissions granted
            return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


class PermissionMixin:
    """
    Mixin class that provides permission checking methods for widgets.
    
    Usage:
        class MyWidget(QWidget, PermissionMixin):
            def __init__(self, parent=None, session_manager=None):
                super().__init__(parent)
                self.session_manager = session_manager
    """
    
    def has_permission(self, permission: str) -> bool:
        """Check if current user has specific permission."""
        session_manager = getattr(self, 'session_manager', None)
        return session_manager.has_permission(permission) if session_manager else False
    
    def is_read_only(self) -> bool:
        """Check if current user is in read-only mode."""
        session_manager = getattr(self, 'session_manager', None)
        return session_manager.is_read_only() if session_manager else True
    
    def is_admin(self) -> bool:
        """Check if current user has admin privileges."""
        session_manager = getattr(self, 'session_manager', None)
        return session_manager.is_admin() if session_manager else False
    
    def get_current_user(self):
        """Get current authenticated user."""
        session_manager = getattr(self, 'session_manager', None)
        return session_manager.get_current_user() if session_manager else None
    
    def check_permission_with_message(self, permission: str, action_name: str = "this action") -> bool:
        """
        Check permission and show message if denied.
        
        Args:
            permission: Permission to check
            action_name: Human-readable action name for error message
            
        Returns:
            True if permission granted, False otherwise
        """
        if self.has_permission(permission):
            return True
        
        current_user = self.get_current_user()
        
        if isinstance(self, QWidget):
            QMessageBox.warning(
                self,
                "Access Denied",
                f"You do not have permission to perform {action_name}.\n\n"
                f"Required permission: {permission.title()}\n"
                f"Your role: {current_user.role_name if current_user else 'Unknown'}"
            )
        
        return False
