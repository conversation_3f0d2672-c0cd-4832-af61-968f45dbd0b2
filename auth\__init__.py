"""
Authentication Module for PROJECT-ALPHA
Provides user authentication, session management, and role-based access control.
"""

from .authentication_service import AuthenticationService, User
from .session_manager import SessionManager, Session
from .decorators import (
    requires_permission,
    requires_write_access,
    requires_admin_access,
    requires_delete_access,
    read_only_disabled,
    log_user_action,
    requires_any_permission,
    requires_all_permissions,
    PermissionMixin
)

__all__ = [
    'AuthenticationService',
    'User',
    'SessionManager', 
    'Session',
    'requires_permission',
    'requires_write_access',
    'requires_admin_access',
    'requires_delete_access',
    'read_only_disabled',
    'log_user_action',
    'requires_any_permission',
    'requires_all_permissions',
    'PermissionMixin'
]
