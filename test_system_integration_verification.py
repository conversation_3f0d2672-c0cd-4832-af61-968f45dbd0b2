#!/usr/bin/env python3
"""
PROJECT-ALPHA System Integration Verification Tests
Verifies that authentication, session management, UI consistency, and database operations work seamlessly.
"""

import sys
import os
import logging
import time
from unittest.mock import MagicMock

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_authentication_integration():
    """Test database and authentication system integration."""
    print("🔍 Testing database and authentication integration...")
    
    try:
        import database
        from auth.authentication_service import AuthenticationService
        
        # Initialize database
        success, first_run = database.init_db()
        if not success:
            print("❌ Database initialization failed")
            return False
        
        print("✅ Database initialized successfully")
        
        # Test authentication service database integration
        user_count = AuthenticationService.count_total_users()
        print(f"✅ Authentication service connected to database - {user_count} users")
        
        # Test user authentication
        admin_user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not admin_user:
            print("❌ Admin user authentication failed")
            return False
        
        print(f"✅ User authentication working - {admin_user.username} ({admin_user.role_name})")
        
        return True
        
    except Exception as e:
        print(f"❌ Database authentication integration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_management_integration():
    """Test session management integration with authentication."""
    print("\n🔍 Testing session management integration...")
    
    try:
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Authenticate user
        admin_user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not admin_user:
            print("❌ Cannot test session management without authentication")
            return False
        
        # Create session manager
        session_manager = SessionManager()
        
        # Create session
        session_id = session_manager.create_session(admin_user)
        if not session_id:
            print("❌ Session creation failed")
            return False
        
        print(f"✅ Session created successfully: {session_id[:16]}...")
        
        # Validate session
        current_user = session_manager.get_current_user()
        if not current_user or current_user.user_id != admin_user.user_id:
            print("❌ Session validation failed")
            return False
        
        print(f"✅ Session validation working - Current user: {current_user.username}")
        
        # Test session timeout functionality
        if not hasattr(session_manager, 'is_session_valid'):
            print("❌ Session manager missing timeout validation")
            return False
        
        print("✅ Session timeout functionality available")
        
        return True
        
    except Exception as e:
        print(f"❌ Session management integration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_consistency_integration():
    """Test UI consistency framework integration with authentication."""
    print("\n🔍 Testing UI consistency integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Get authenticated user and session
        admin_user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not admin_user:
            print("❌ Cannot test UI consistency without authentication")
            return False
        
        session_manager = SessionManager()
        session_id = session_manager.create_session(admin_user)
        
        # Test LoginDialog integration
        from auth.login_dialog import LoginDialog
        login_dialog = LoginDialog()
        
        if not hasattr(login_dialog, 'check_signup_availability'):
            print("❌ LoginDialog missing signup availability check")
            return False
        
        print("✅ LoginDialog has signup availability integration")
        
        # Test SignUpDialog integration
        from auth.signup_dialog import SignUpDialog
        signup_dialog = SignUpDialog(parent=None, current_user=admin_user, is_admin_mode=True)
        
        if not hasattr(signup_dialog, 'role_combo'):
            print("❌ SignUpDialog missing role selection")
            return False
        
        print("✅ SignUpDialog has role-based UI integration")
        
        # Test UserManagementDialog integration
        from auth.user_management_dialog import UserManagementDialog
        user_mgmt_dialog = UserManagementDialog(parent=None, current_user=admin_user)
        
        if not hasattr(user_mgmt_dialog, 'user_table'):
            print("❌ UserManagementDialog missing user table")
            return False
        
        print("✅ UserManagementDialog has consistent UI components")
        
        return True
        
    except Exception as e:
        print(f"❌ UI consistency integration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_role_based_access_control():
    """Test role-based access control integration."""
    print("\n🔍 Testing role-based access control...")
    
    try:
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Test admin user permissions
        admin_user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not admin_user:
            print("❌ Admin authentication failed")
            return False
        
        if admin_user.role_name != "Administrator":
            print(f"❌ Admin user has wrong role: {admin_user.role_name}")
            return False
        
        print(f"✅ Admin user has correct role: {admin_user.role_name}")
        
        # Test role-based dialog access
        from auth.user_management_dialog import UserManagementDialog
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Admin should be able to access user management
        try:
            admin_dialog = UserManagementDialog(parent=None, current_user=admin_user)
            print("✅ Administrator can access user management")
        except Exception as e:
            print(f"❌ Administrator cannot access user management: {e}")
            return False
        
        # Test non-admin user restriction
        mock_read_only_user = MagicMock()
        mock_read_only_user.role_name = "Read-Only"
        mock_read_only_user.user_id = 999
        
        try:
            UserManagementDialog(parent=None, current_user=mock_read_only_user)
            print("❌ Read-Only user should not access user management")
            return False
        except ValueError:
            print("✅ Read-Only user correctly restricted from user management")
        
        return True
        
    except Exception as e:
        print(f"❌ Role-based access control error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_application_integration():
    """Test main application integration with authentication system."""
    print("\n🔍 Testing main application integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Get authenticated user and session
        admin_user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not admin_user:
            print("❌ Cannot test main application without authentication")
            return False
        
        session_manager = SessionManager()
        session_id = session_manager.create_session(admin_user)
        
        # Test main window creation with session manager
        from main import MainWindow
        main_window = MainWindow(session_manager=session_manager)
        
        # Verify main window has authentication integration
        if not hasattr(main_window, 'session_manager'):
            print("❌ MainWindow missing session_manager")
            return False
        
        print("✅ MainWindow has session manager integration")
        
        # Verify main window has user management functionality
        if not hasattr(main_window, 'show_user_management'):
            print("❌ MainWindow missing user management functionality")
            return False
        
        print("✅ MainWindow has user management functionality")
        
        # Verify main window has authentication-aware UI
        if not hasattr(main_window, 'button_state_manager'):
            print("❌ MainWindow missing button state manager")
            return False
        
        print("✅ MainWindow has authentication-aware UI controls")
        
        return True
        
    except Exception as e:
        print(f"❌ Main application integration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_user_workflow():
    """Test complete user workflow from registration to authentication."""
    print("\n🔍 Testing complete user workflow...")
    
    try:
        from auth.authentication_service import AuthenticationService
        
        # Generate unique username
        unique_suffix = str(int(time.time() * 1000))[-6:]
        test_username = f"workflow{unique_suffix}"
        
        # Test user registration
        success, message, user_id = AuthenticationService.register_user(
            username=test_username,
            password="Workflow123!",
            full_name="Workflow Test User",
            email="<EMAIL>",
            role_name="Read-Only"
        )
        
        if not success:
            print(f"❌ User registration failed: {message}")
            return False
        
        print(f"✅ User registered successfully - ID: {user_id}")
        
        # Test user authentication
        new_user = AuthenticationService.authenticate_user(test_username, "Workflow123!")
        if not new_user:
            print("❌ Newly registered user cannot authenticate")
            return False
        
        print(f"✅ User authentication successful: {new_user.username}")
        
        # Test session creation for new user
        from auth.session_manager import SessionManager
        session_manager = SessionManager()
        session_id = session_manager.create_session(new_user)
        
        if not session_id:
            print("❌ Session creation failed for new user")
            return False
        
        print(f"✅ Session created for new user: {session_id[:16]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Complete user workflow error: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_integration_verification_tests():
    """Run all system integration verification tests."""
    print("🚀 Starting System Integration Verification Tests")
    print("=" * 60)
    
    tests = [
        test_database_authentication_integration,
        test_session_management_integration,
        test_ui_consistency_integration,
        test_role_based_access_control,
        test_main_application_integration,
        test_complete_user_workflow
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED")
            else:
                failed += 1
                print("❌ FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print("-" * 40)
    
    print(f"\n📊 Integration Verification Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All integration verification tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = run_integration_verification_tests()
    sys.exit(0 if success else 1)
