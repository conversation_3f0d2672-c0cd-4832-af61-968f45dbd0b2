# Database Corruption Fixes - Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing the database corruption fixes in PROJECT-ALPHA. The fixes are designed to achieve a 10/10 database reliability score while maintaining compatibility with the existing single-window desktop architecture.

## Prerequisites

1. **Backup Current Database**: Always create a backup before implementing fixes
2. **Test Environment**: Set up a test environment with sample data
3. **Dependencies**: Ensure all required packages are installed

```bash
# Create database backup
cp inventory.db inventory.db.backup.$(date +%Y%m%d_%H%M%S)
```

## Phase 1: Critical Connection Management Fixes

### Step 1: Replace Direct Database Connections

**Target Files**: `robust_excel_importer_working.py`

**Current Problem**:
```python
# PROBLEMATIC CODE - Direct connections bypass pool
import sqlite3
import config
conn = sqlite3.connect(config.DB_PATH)
cursor = conn.cursor()
```

**Solution**:
```python
# FIXED CODE - Use connection pool
from database import get_db_connection

with get_db_connection() as conn:
    cursor = conn.cursor()
    # ... operations ...
    # Connection automatically returned to pool
```

**Implementation Steps**:

1. **Find all direct connections**:
```bash
grep -n "sqlite3.connect" robust_excel_importer_working.py
```

2. **Replace each occurrence** with connection pool usage:

**Lines to modify in `robust_excel_importer_working.py`**:
- Line 447: Equipment insertion
- Line 794: Equipment updates  
- Line 1810: Equipment lookup
- Line 2097: Maintenance operations
- Line 2465: Release date updates
- Line 2635: Overhaul operations
- Line 2820: Conditioning operations
- Line 2987: Battery operations

### Step 2: Implement Atomic Transactions

**Create new method in `robust_excel_importer_working.py`**:

```python
def _save_equipment_atomic(self, equipment_data: Dict[str, Any], 
                          fluids_data: List[Dict[str, Any]] = None,
                          maintenance_data: List[Dict[str, Any]] = None) -> Tuple[bool, Optional[int]]:
    """Save equipment with all related data in a single atomic transaction."""
    from database_corruption_fixes import TransactionManager
    
    return TransactionManager.execute_equipment_with_related_data(
        equipment_data, fluids_data, maintenance_data
    )
```

**Replace equipment processing loop**:

```python
# OLD CODE (Lines 431-708)
for index, row in df.iterrows():
    # Individual operations with separate connections
    
# NEW CODE
for index, row in df.iterrows():
    equipment_data = self._extract_equipment_data(row, col_map, sheet_name)
    if equipment_data and self._is_valid_equipment_record(equipment_data):
        
        # Extract related data
        fluids_data = self._extract_fluids_data(row, col_map)
        maintenance_data = self._extract_maintenance_data(row, col_map)
        
        # Atomic save - all or nothing
        success, equipment_id = self._save_equipment_atomic(
            equipment_data, fluids_data, maintenance_data
        )
        
        if success:
            equipment_count += 1
        else:
            logger.error(f"Failed to save equipment atomically: {equipment_data.get('make_and_type')}")
```

### Step 3: Add Import Synchronization

**Modify `main.py` ImportWorker class**:

```python
from database_corruption_fixes import ImportSynchronizationManager

class ImportWorker(QThread):
    def __init__(self, filename, conflict_resolutions=None):
        super().__init__()
        self.filename = filename
        self.conflict_resolutions = conflict_resolutions or {}
        self.import_id = f"import_{int(time.time())}_{threading.current_thread().ident}"
    
    def run(self):
        """Perform the Excel import with synchronization."""
        sync_manager = ImportSynchronizationManager()
        
        try:
            with sync_manager.synchronized_import(self.import_id):
                self.progress_update.emit(10, "Acquiring import lock...")
                
                # Perform health check before import
                from database_corruption_fixes import DatabaseIntegrityManager
                integrity_manager = DatabaseIntegrityManager(config.DB_PATH)
                health_report = integrity_manager.perform_health_check()
                
                if health_report['overall_status'] == 'CRITICAL':
                    raise Exception(f"Database health check failed: {health_report['issues']}")
                
                # Proceed with import
                self.progress_update.emit(20, "Starting import...")
                stats = import_from_excel(self.filename)
                
                # Verify integrity after import
                post_health_report = integrity_manager.perform_health_check()
                if post_health_report['overall_status'] == 'CRITICAL':
                    logger.warning("Database integrity issues detected after import")
                
                self.import_complete.emit(stats)
                
        except Exception as e:
            self.import_error.emit(str(e))
```

## Phase 2: Enhanced Error Handling

### Step 4: Add Database Health Monitoring

**Create health check service**:

```python
# Add to main_window.py
from database_corruption_fixes import DatabaseIntegrityManager

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.integrity_manager = DatabaseIntegrityManager(config.DB_PATH)
        self.setup_health_monitoring()
    
    def setup_health_monitoring(self):
        """Set up periodic database health checks."""
        from PyQt5.QtCore import QTimer
        
        self.health_timer = QTimer()
        self.health_timer.timeout.connect(self.perform_health_check)
        self.health_timer.start(300000)  # 5 minutes
    
    def perform_health_check(self):
        """Perform periodic health check."""
        health_report = self.integrity_manager.perform_health_check()
        
        if health_report['overall_status'] == 'CRITICAL':
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(
                self, 
                "Database Health Warning",
                f"Critical database issues detected:\n" + 
                "\n".join(health_report['issues'][:3]) +
                "\n\nPlease contact system administrator."
            )
```

### Step 5: Implement Backup Strategy

**Add to Excel import process**:

```python
def import_from_excel_with_backup(file_path):
    """Import Excel with automatic backup."""
    import time
    from database_corruption_fixes import create_database_backup
    
    # Create backup before import
    backup_path = f"{config.DB_PATH}.backup.{int(time.time())}"
    if not create_database_backup(config.DB_PATH, backup_path):
        raise Exception("Failed to create database backup")
    
    try:
        # Perform import
        result = import_from_excel(file_path)
        
        # Validate result
        from database_corruption_fixes import validate_database_constraints
        validation = validate_database_constraints(config.DB_PATH)
        
        if not validation['valid']:
            # Restore from backup
            logger.error("Import caused database corruption, restoring backup")
            import shutil
            shutil.copy2(backup_path, config.DB_PATH)
            raise Exception(f"Import validation failed: {validation['constraint_violations']}")
        
        return result
        
    except Exception as e:
        # Restore from backup on any error
        logger.error(f"Import failed, restoring backup: {e}")
        import shutil
        shutil.copy2(backup_path, config.DB_PATH)
        raise
    
    finally:
        # Clean up backup after successful import
        import os
        try:
            os.remove(backup_path)
        except:
            pass
```

## Phase 3: Testing and Validation

### Step 6: Create Test Suite

**Create `test_database_integrity.py`**:

```python
import unittest
import tempfile
import os
from database_corruption_fixes import DatabaseIntegrityManager, TransactionManager

class TestDatabaseIntegrity(unittest.TestCase):
    
    def setUp(self):
        self.test_db = tempfile.mktemp(suffix='.db')
        # Initialize test database
        
    def tearDown(self):
        if os.path.exists(self.test_db):
            os.remove(self.test_db)
    
    def test_concurrent_imports(self):
        """Test multiple simultaneous imports."""
        # Simulate concurrent import operations
        pass
    
    def test_transaction_rollback(self):
        """Test transaction rollback on error."""
        # Test atomic transaction behavior
        pass
    
    def test_foreign_key_constraints(self):
        """Test foreign key constraint enforcement."""
        # Test constraint validation
        pass

if __name__ == '__main__':
    unittest.main()
```

### Step 7: Performance Testing

**Create load test script**:

```python
def test_concurrent_operations():
    """Test database under concurrent load."""
    import threading
    import time
    
    def simulate_import(import_id):
        # Simulate Excel import operation
        pass
    
    # Start multiple import threads
    threads = []
    for i in range(5):
        thread = threading.Thread(target=simulate_import, args=(f"import_{i}",))
        threads.append(thread)
        thread.start()
    
    # Wait for completion
    for thread in threads:
        thread.join()
    
    # Verify database integrity
    from database_corruption_fixes import DatabaseIntegrityManager
    manager = DatabaseIntegrityManager(config.DB_PATH)
    health_report = manager.perform_health_check()
    
    assert health_report['overall_status'] != 'CRITICAL', f"Database corruption detected: {health_report['issues']}"
```

## Deployment Checklist

### Pre-Deployment
- [ ] Database backup created
- [ ] Test environment validated
- [ ] All unit tests passing
- [ ] Performance tests completed

### Deployment Steps
1. [ ] Stop application
2. [ ] Create production backup
3. [ ] Deploy new code
4. [ ] Run database health check
5. [ ] Start application
6. [ ] Verify import functionality
7. [ ] Monitor for 24 hours

### Post-Deployment Monitoring
- [ ] Database health checks every 5 minutes
- [ ] Import operation monitoring
- [ ] Performance metrics tracking
- [ ] Error rate monitoring

## Rollback Plan

If issues are detected after deployment:

1. **Stop application immediately**
2. **Restore from backup**:
   ```bash
   cp inventory.db.backup.TIMESTAMP inventory.db
   ```
3. **Revert code changes**
4. **Restart application**
5. **Investigate issues in test environment**

## Success Metrics

**Target: 10/10 Database Reliability Score**

- ✅ Zero data loss events
- ✅ Zero constraint violations  
- ✅ Zero lock timeouts
- ✅ 100% transaction consistency
- ✅ Sub-second error recovery

## Support and Troubleshooting

### Common Issues

**Issue**: Import operations hang
**Solution**: Check for database locks, restart application

**Issue**: Foreign key violations
**Solution**: Run integrity repair tool

**Issue**: Performance degradation
**Solution**: Check connection pool configuration

### Monitoring Commands

```bash
# Check database integrity
python -c "from database_corruption_fixes import DatabaseIntegrityManager; print(DatabaseIntegrityManager('inventory.db').perform_health_check())"

# Monitor active imports
python -c "from database_corruption_fixes import ImportSynchronizationManager; print(ImportSynchronizationManager().get_active_imports())"
```

This implementation guide provides a comprehensive roadmap for achieving database reliability while maintaining the existing PROJECT-ALPHA architecture.
