"""Overhaul management widget for the equipment inventory application."""
import logging
from datetime import datetime, date, timedelta

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QComboBox, QPushButton, QFileDialog, QFormLayout, QTextEdit, QDateEdit, QLabel, QFrame, QCompleter, QGroupBox, QSplitter, QTabWidget, QMessageBox
from PyQt5.QtCore import Qt, QDate

import locale
locale.setlocale(locale.LC_ALL, '')

import database
import overhaul_service
import config
import utils
from models import Equipment, Repair, Overhaul, MediumReset
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel
from ui.common_styles import *
from ui.custom_widgets import StatusLabel

# Configure logger
logger = logging.getLogger('overhaul_widget')

class OverhaulWidget(QWidget):
    """Main overhaul management widget with OH-I and OH-II sub-tabs."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Set up the main overhaul widget with sub-tabs."""
        # Create main layout
        main_layout = QVBoxLayout(self)
        apply_standard_layout(main_layout)

        # Create tab widget for overhaul categories
        self.tab_widget = QTabWidget()

        # Create sub-widgets for each overhaul type
        self.oh1_widget = OverhaulSubWidget(overhaul_type='OH-I', parent=self)
        self.oh2_widget = OverhaulSubWidget(overhaul_type='OH-II', parent=self)

        # Add tabs
        self.tab_widget.addTab(self.oh1_widget, "OH-I (First Overhaul)")
        self.tab_widget.addTab(self.oh2_widget, "OH-II (Second Overhaul)")

        # Connect tab changed signal
        self.tab_widget.currentChanged.connect(self.tab_changed)

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

        # Apply standardized stylesheet
        self.setStyleSheet(get_complete_stylesheet())

    def load_data(self):
        """Load data for all overhaul sub-tabs asynchronously to prevent UI freezing."""
        logger.info("Loading data for all overhaul tabs asynchronously")
        try:
            # Load data for currently visible tab first (synchronously for immediate display)
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()

            # Load data for other tabs asynchronously to prevent UI blocking
            self.load_other_tabs_async()

        except Exception as e:
            logger.error(f"Error loading overhaul data: {e}")

    def load_other_tabs_async(self):
        """Load data for non-current tabs asynchronously."""
        try:
            from performance_optimizations import AsyncDataLoader

            current_widget = self.tab_widget.currentWidget()
            other_widgets = []

            # Collect widgets that need loading
            for i in range(self.tab_widget.count()):
                widget = self.tab_widget.widget(i)
                if widget != current_widget and hasattr(widget, 'load_data'):
                    other_widgets.append(widget)

            if other_widgets:
                # Create async loader for background tab loading
                self.async_loader = AsyncDataLoader(self._load_widgets_data, other_widgets)
                self.async_loader.data_loaded.connect(self._on_background_tabs_loaded)
                self.async_loader.error_occurred.connect(self._on_background_load_error)
                self.async_loader.start()

        except Exception as e:
            logger.error(f"Error setting up async tab loading: {e}")

    def _load_widgets_data(self, widgets):
        """Load data for multiple widgets (runs in background thread)."""
        results = []
        for widget in widgets:
            try:
                widget.load_data()
                results.append(f"Loaded {widget.__class__.__name__}")
            except Exception as e:
                logger.error(f"Error loading data for {widget.__class__.__name__}: {e}")
                results.append(f"Error loading {widget.__class__.__name__}: {e}")
        return results

    def _on_background_tabs_loaded(self, results):
        """Handle successful background tab loading."""
        logger.info(f"Background tab loading completed: {results}")

    def _on_background_load_error(self, error):
        """Handle background tab loading error."""
        logger.error(f"Background tab loading failed: {error}")

    def tab_changed(self, index):
        """Handle tab change event."""
        current_widget = self.tab_widget.widget(index)
        if hasattr(current_widget, 'load_data'):
            current_widget.load_data()


class OverhaulSubWidget(QWidget):
    """Individual overhaul widget for each type (OH-I, OH-II)."""

    def __init__(self, overhaul_type, parent=None):
        super().__init__(parent)
        self.overhaul_type = overhaul_type  # 'OH-I' or 'OH-II'
        self.current_overhaul_id = None
        self.setup_ui()

    def setup_ui(self):
        """Set up the overhaul sub-widget UI following Maintenance tab layout."""
        # Create main layout
        main_layout = QHBoxLayout(self)
        apply_standard_layout(main_layout)

        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Create left panel (overhaul list)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Add category label
        category_label = QLabel(f"Equipment {self.overhaul_type} Overhauls")
        category_label.setProperty("class", "section-title")
        left_layout.addWidget(category_label)

        # ---- Toolbar ----
        toolbar = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Filter by BA, serial, equipment...")
        self.search_edit.textChanged.connect(self.apply_filters)

        self.status_combo = QComboBox()
        self.status_combo.addItems(["All", "Reminder", "Warning", "Critical", "Overdue", "Completed"])
        self.status_combo.currentIndexChanged.connect(self.apply_filters)

        clear_btn = QPushButton("Clear")
        clear_btn.clicked.connect(self.clear_filters)
        apply_button_style(clear_btn, "default")

        export_btn = QPushButton("Export CSV")
        export_btn.clicked.connect(self.export_csv)
        apply_button_style(export_btn, "default")

        toolbar.addWidget(self.search_edit)
        toolbar.addWidget(self.status_combo)
        toolbar.addWidget(clear_btn)
        toolbar.addStretch()
        toolbar.addWidget(export_btn)
        left_layout.addLayout(toolbar)

        # ---- Table ----
        self.overhauls_table = ReadOnlyTableWidget()
        self.overhauls_table.row_clicked.connect(self.on_row_selected)
        self.overhauls_table.setSortingEnabled(True)
        left_layout.addWidget(self.overhauls_table)

        # Add left panel to splitter
        splitter.addWidget(left_panel)

        # ---- Right Panel (Details) ----
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Details Panel
        details_group = QGroupBox(f"{self.overhaul_type} Overhaul Details")
        details_layout = QFormLayout()
        apply_form_layout(details_layout)
        details_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        details_layout.setFormAlignment(Qt.AlignmentFlag.AlignLeft)

        self.detail_equipment = QLabel()
        self.detail_ba = QLabel()
        self.detail_ba.hide() # BA Number row removed as per new requirements
        self.detail_meterage = QLabel()
        
        # Change Date of Release from QLabel to QDateEdit to make it editable
        # Set to allow null dates and default to null
        self.release_date_edit = QDateEdit()
        self.release_date_edit.setCalendarPopup(True)
        self.release_date_edit.setSpecialValueText("Not Set")
        self.release_date_edit.setDate(QDate(1900, 1, 1))  # Null date
        self.release_date_edit.dateChanged.connect(self.update_status_preview)

        # Equipment selection dropdown for new overhauls
        self.equipment_combo = QComboBox()
        self.equipment_combo.setPlaceholderText("Select Equipment...")
        self.equipment_combo.currentIndexChanged.connect(self.on_equipment_selected)
        self.equipment_combo.hide()  # Hidden by default

        # Date fields specific to this overhaul type
        # OH Done Date should default to empty/null and be freely editable
        self.overhaul_done = QDateEdit()
        self.overhaul_done.setCalendarPopup(True)
        self.overhaul_done.setSpecialValueText("Not Completed")
        self.overhaul_done.setDate(QDate(1900, 1, 1))  # Default to null date
        self.overhaul_done.dateChanged.connect(self.update_status_preview)
        
        # Due date kept internally for logic but not shown to user
        self.overhaul_due = QDateEdit()
        self.overhaul_due.setCalendarPopup(True)
        self.overhaul_due.hide()
        self.overhaul_status_lbl = StatusLabel()

        details_layout.addRow("Equipment:", self.detail_equipment)
        # Remove "Select Equipment" row as requested
        details_layout.addRow("Meterage KM:", self.detail_meterage)
        details_layout.addRow("Date of Release:", self.release_date_edit)
        details_layout.addRow(f"{self.overhaul_type} Done Date:", self.overhaul_done)
        # Due date row removed – now auto-calculated
        details_layout.addRow(f"{self.overhaul_type} Status:", self.overhaul_status_lbl)

        details_group.setLayout(details_layout)
        right_layout.addWidget(details_group)

        # Action buttons (following Maintenance tab layout)
        actions = QHBoxLayout()
        apply_button_layout(actions)

        self.add_btn = QPushButton("Add New")
        self.edit_btn = QPushButton("Edit")
        self.save_btn = QPushButton("Save")
        self.delete_btn = QPushButton("Delete")

        # Completion button specific to overhaul type
        completion_text = f"Mark {self.overhaul_type} Complete"
        self.complete_btn = QPushButton(completion_text)

        # Apply standardized button styles
        apply_button_style(self.add_btn, "primary")
        apply_button_style(self.edit_btn, "default")
        apply_button_style(self.save_btn, "success")
        apply_button_style(self.delete_btn, "danger")

        # Style completion button based on overhaul type
        if self.overhaul_type == 'OH-I':
            apply_button_style(self.complete_btn, "success")  # Green for OH-I
        else:
            apply_button_style(self.complete_btn, "warning")  # Orange for OH-II

        # Initial button states
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.complete_btn.setEnabled(False)

        actions.addWidget(self.add_btn)
        actions.addWidget(self.edit_btn)
        actions.addWidget(self.save_btn)
        actions.addWidget(self.delete_btn)
        actions.addWidget(self.complete_btn)
        actions.addStretch()

        right_layout.addLayout(actions)
        right_layout.addStretch()

        # Add right panel to splitter
        splitter.addWidget(right_panel)

        # Set splitter proportions (70% left, 30% right)
        splitter.setSizes([700, 300])

        # Add splitter to main layout
        main_layout.addWidget(splitter)

        # Connections
        self.overhauls_table.itemSelectionChanged.connect(self.on_row_selected)
        self.add_btn.clicked.connect(self.on_add)
        self.edit_btn.clicked.connect(self.on_edit)
        self.save_btn.clicked.connect(self.on_save)
        self.delete_btn.clicked.connect(self.on_delete)
        self.complete_btn.clicked.connect(self.on_complete_overhaul)

        # Initially disable form editing
        self.set_form_enabled(False)

        # Load data initially
        self.load_data()

        # One-time status preview refresh (ensures blank form shows 'unknown')
        self.update_status_preview()

    def load_data(self):
        """Load overhauls data filtered by overhaul type with optimized queries."""
        logger.info(f"Loading {self.overhaul_type} overhauls data from Overhaul table")
        try:
            # Get all overhauls from the Overhaul table (already includes equipment data via JOIN)
            overhauls_list = Overhaul.get_all()

            # Build equipment mappings from overhaul data (no separate Equipment.get_active() call needed)
            equipment_map = {}
            equipment_details = {}
            equipment_ba_map = {}
            equipment_ids_seen = set()

            # Extract equipment data from overhaul records (already JOINed)
            for overhaul in overhauls_list:
                equipment_id = overhaul.get('equipment_id')
                if equipment_id and equipment_id not in equipment_ids_seen:
                    # Create equipment record from overhaul data
                    eq = {
                        'equipment_id': equipment_id,
                        'make_and_type': overhaul.get('make_and_type'),
                        'ba_number': overhaul.get('ba_number')
                    }
                    equipment_map[equipment_id] = utils.format_equipment_display(eq)
                    equipment_details[equipment_id] = eq
                    if eq.get('ba_number') not in [None, '', 'None']:
                        equipment_ba_map[eq['ba_number']] = eq
                    equipment_ids_seen.add(equipment_id)

            # Group overhauls by equipment_id
            equipment_overhauls = {}
            for overhaul in overhauls_list:
                equipment_id = overhaul.get('equipment_id')
                if not equipment_id:
                    continue  # Skip records without equipment
                if equipment_id not in equipment_overhauls:
                    equipment_overhauls[equipment_id] = {'OH-I': None, 'OH-II': None}

                overhaul_type = overhaul.get('overhaul_type', '')
                if overhaul_type in ['OH-I', 'OH-II']:
                    equipment_overhauls[equipment_id][overhaul_type] = overhaul

            # Prepare data for table - filter by current overhaul type
            data = []

            for equipment_id, overhauls in equipment_overhauls.items():
                oh1 = overhauls.get('OH-I')
                oh2 = overhauls.get('OH-II')

                # Get the overhaul record for current type
                current_overhaul = oh1 if self.overhaul_type == 'OH-I' else oh2

                # Skip if no overhaul record exists for this type and equipment doesn't need this overhaul yet
                if not current_overhaul:
                    # For OH-I, always show equipment (they all need OH-I eventually)
                    # For OH-II, only show if OH-I is completed
                    if self.overhaul_type == 'OH-II':
                        if not oh1 or not oh1.get('done_date') or oh1.get('done_date') in ['None', '', None]:
                            continue  # Skip equipment that hasn't completed OH-I yet

                # Calculate due dates
                equip_info = equipment_details.get(equipment_id, {})
                # FIX: Use date_of_release instead of date_of_commission for Release Date
                release_date_val = equip_info.get('date_of_release') or equip_info.get('date_of_commission')

                # Calculate OH-I due date (15 years from release)
                oh1_due_calculated = None
                if release_date_val:
                    try:
                        from datetime import datetime, timedelta
                        if isinstance(release_date_val, str):
                            rel_date = datetime.strptime(release_date_val.split(' ')[0], '%Y-%m-%d').date()
                        else:
                            rel_date = release_date_val
                        oh1_due_calculated = rel_date + timedelta(days=365*15)
                    except Exception:
                        pass

                # Calculate OH-II due date (10 years after OH-I completion)
                oh2_due_calculated = None
                if oh1 and oh1.get('done_date') not in [None, '', 'None']:
                    try:
                        from datetime import datetime, timedelta
                        oh1_done_date = datetime.strptime(oh1.get('done_date').split(' ')[0], '%Y-%m-%d').date()
                        oh2_due_calculated = oh1_done_date + timedelta(days=365*10)
                    except (ValueError, TypeError):
                        pass

                # Get dates and status for current overhaul type
                if self.overhaul_type == 'OH-I':
                    due_date = oh1.get('due_date') if oh1 else oh1_due_calculated
                    # FIX: Handle null/empty done_date properly
                    done_date = oh1.get('done_date') if oh1 and oh1.get('done_date') not in [None, '', 'None', '0'] else None
                    overhaul_id = oh1.get('overhaul_id') if oh1 else equipment_id
                else:  # OH-II
                    due_date = oh2.get('due_date') if oh2 and oh2.get('due_date') else oh2_due_calculated
                    # FIX: Handle null/empty done_date properly
                    done_date = oh2.get('done_date') if oh2 and oh2.get('done_date') not in [None, '', 'None', '0'] else None
                    overhaul_id = oh2.get('overhaul_id') if oh2 else equipment_id

                # Format dates
                due_date_str = self.format_date(due_date)
                done_date_str = self.format_date(done_date) if done_date else "-"
                release_date_str = self.format_date(release_date_val) if release_date_val else "-"

                # Get meterage
                km_run_val = None
                if current_overhaul and current_overhaul.get('meter_reading') not in [None, 0, '']:
                    km_run_val = current_overhaul.get('meter_reading')
                if km_run_val in [None, '', 0]:
                    km_run_val = equip_info.get('MeterageKMs') or equip_info.get('meterage_kms') or ''
                meterage_str = self.format_kilometers(km_run_val)

                # Calculate status
                if current_overhaul:
                    raw_status = current_overhaul.get('status')
                    if raw_status not in [None, '', 'None', 'No']:
                        status = raw_status
                    else:
                        status = overhaul_service.get_overhaul_status(
                            self.overhaul_type,
                            due_date,
                            done_date,
                            date_of_commission=release_date_val,
                            oh1_done_date=oh1.get('done_date') if oh1 else None,
                            meterage_km=km_run_val
                        )
                else:
                    status = overhaul_service.get_overhaul_status(
                        self.overhaul_type,
                        due_date,
                        None,
                        date_of_commission=release_date_val,
                        oh1_done_date=oh1.get('done_date') if oh1 else None,
                        meterage_km=km_run_val
                    )

                # Skip equipment with "discard" status - they should only appear in Discard Criteria tab
                if status == "discard":
                    continue

                equipment_name = equipment_map.get(equipment_id, str(equipment_id))

                row_data = {
                    "Equipment": equipment_name,
                    "Release Date": release_date_str,
                    "Meterage KM": meterage_str,
                    "Done Date": done_date_str,
                    "Due Date": due_date_str,
                    "Status": status,
                    "ID": overhaul_id
                }
                data.append(row_data)

            self.all_data = data  # store for filtering/export
            self.refresh_table()

            # Hide the ID column but keep it for backend operations
            id_column_index = len(self.visible_headers()) - 1  # ID is the last column
            self.overhauls_table.setColumnHidden(id_column_index, True)

            logger.info(f"Prepared {len(data)} {self.overhaul_type} overhaul rows")
        except Exception as e:
            logger.error(f"Error loading {self.overhaul_type} overhauls data: {e}")

    def apply_filters(self):
        """Filter self.all_data based on search text and status combo."""
        if not hasattr(self, 'all_data'):
            return
        text = self.search_edit.text().lower().strip()
        status_filter = self.status_combo.currentText()

        filtered = []
        for row in self.all_data:
            if status_filter != "All" and status_filter.lower() != row.get("Status", "").lower():
                continue
            if text and text not in str(row["Equipment"]).lower() and text not in str(row["Release Date"]).lower():
                continue
            filtered.append(row)
        self.overhauls_table.set_data(self.visible_headers(), filtered, id_column=len(self.visible_headers())-1)

    def clear_filters(self):
        self.search_edit.clear()
        self.status_combo.setCurrentIndex(0)

    def refresh_table(self):
        """Initial table set or after data reload."""
        self.apply_filters()

    def visible_headers(self):
        """Return headers specific to the overhaul type."""
        return ["Equipment", "Release Date", "Meterage KM", "Done Date", "Due Date", "Status", "ID"]

    def export_csv(self):
        if not hasattr(self, 'all_data') or not self.all_data:
            return
        filename = f"{self.overhaul_type.lower().replace('-', '_')}_overhauls.csv"
        path, _ = QFileDialog.getSaveFileName(self, f"Export {self.overhaul_type} Overhaul Data", filename, "CSV Files (*.csv)")
        if not path:
            return
        import csv
        try:
            with open(path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=self.visible_headers()[:-1])  # exclude ID
                writer.writeheader()
                for row in self.all_data:
                    writer.writerow({k: row.get(k, '') for k in self.visible_headers()[:-1]})
        except Exception as e:
            logger.error(f"Export failed: {e}")

    def format_date(self, value):
        """Convert date or string value to DD/MM/YYYY string via utils."""
        return utils.format_date_for_display(value)

    def format_kilometers(self, value):
        """Return kilometer value with commas and no decimals, or '-' if not valid."""
        try:
            if value in [None, '', 'None']:
                return '-'
            val = float(value)
            if val <= 0:
                return '-'
            return f"{val:,.0f}"
        except (ValueError, TypeError):
            return str(value)

    # ---------- details panel handlers ----------
    def on_row_selected(self):
        """Handle overhaul selection."""
        # Get selected overhaul ID
        overhaul_id = self.overhauls_table.get_selected_id()

        if overhaul_id:
            self.current_overhaul_id = overhaul_id
            # Show equipment label and hide dropdown for existing records
            self.equipment_combo.hide()
            self.detail_equipment.show()
            # Enable edit, delete, and completion buttons
            self.edit_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            # Load overhaul details (this will set completion button state)
            self.load_overhaul_details(overhaul_id)
            # Disable form editing initially
            self.set_form_enabled(False)
        else:
            self.current_overhaul_id = None
            self.edit_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.complete_btn.setEnabled(False)
            self.set_form_enabled(False)
            self.clear_form()

    def load_overhaul_details(self, overhaul_id):
        """Load overhaul details into the form."""
        # Find data dict from loaded data
        data_dict = next((d for d in self.all_data if d['ID']==overhaul_id), None)
        if data_dict:
            self.populate_details(data_dict)

    def clear_form(self):
        """Clear the overhaul form."""
        self.detail_equipment.setText("")
        self.detail_ba.setText("")
        self.detail_meterage.setText("")
        # Set to null dates instead of current date
        self.release_date_edit.setDate(QDate(1900, 1, 1))
        self.overhaul_done.setDate(QDate(1900, 1, 1))
        self.overhaul_due.setDate(QDate.currentDate())
        self.overhaul_status_lbl.setText("")
        self.overhaul_status_lbl.setStatus("normal")

    def populate_details(self, d):
        """Populate form with overhaul details."""
        try:
            self.detail_equipment.setText(d.get('Equipment',''))
            self.detail_ba.setText(d.get('BA Number',''))

            # Use meterage from table data directly
            meterage_str = d.get('Meterage KM', '')
            self.detail_meterage.setText(meterage_str)
            
            # FIX: Parse and set release date from table data
            release_date_str = d.get('Release Date', '')
            if release_date_str and release_date_str not in ['-', '', 'None', None]:
                try:
                    from datetime import datetime
                    # The table shows dates in DD/MM/YYYY format, parse accordingly
                    if '/' in release_date_str:
                        # Parse DD/MM/YYYY format from table
                        parsed_date = datetime.strptime(release_date_str, '%d/%m/%Y').date()
                        self.release_date_edit.setDate(QDate.fromString(parsed_date.isoformat(), 'yyyy-MM-dd'))
                    else:
                        # Try other formats
                        for fmt in ['%Y-%m-%d', '%d-%m-%Y']:
                            try:
                                parsed_date = datetime.strptime(release_date_str.split(' ')[0], fmt).date()
                                self.release_date_edit.setDate(QDate.fromString(parsed_date.isoformat(), 'yyyy-MM-dd'))
                                break
                            except ValueError:
                                continue
                        else:
                            # If no format worked, set to null
                            self.release_date_edit.setDate(QDate(1900, 1, 1))
                except Exception as e:
                    logger.debug(f"Error parsing release date '{release_date_str}': {e}")
                    self.release_date_edit.setDate(QDate(1900, 1, 1))  # Null date
            else:
                self.release_date_edit.setDate(QDate(1900, 1, 1))  # Null date
                
            # FIX: Parse and set OH done date from table data
            done_date_str = d.get('Done Date', '')
            if done_date_str and done_date_str not in ['-', '', 'None', None]:
                try:
                    from datetime import datetime
                    # The table shows dates in DD/MM/YYYY format, parse accordingly
                    if '/' in done_date_str:
                        # Parse DD/MM/YYYY format from table
                        parsed_date = datetime.strptime(done_date_str, '%d/%m/%Y').date()
                        self.overhaul_done.setDate(QDate.fromString(parsed_date.isoformat(), 'yyyy-MM-dd'))
                    else:
                        # Try other formats
                        for fmt in ['%Y-%m-%d', '%d-%m-%Y']:
                            try:
                                parsed_date = datetime.strptime(done_date_str.split(' ')[0], fmt).date()
                                self.overhaul_done.setDate(QDate.fromString(parsed_date.isoformat(), 'yyyy-MM-dd'))
                                break
                            except ValueError:
                                continue
                        else:
                            # If no format worked, set to null
                            self.overhaul_done.setDate(QDate(1900, 1, 1))
                except Exception as e:
                    logger.debug(f"Error parsing done date '{done_date_str}': {e}")
                    self.overhaul_done.setDate(QDate(1900, 1, 1))  # Null date for not completed
            else:
                self.overhaul_done.setDate(QDate(1900, 1, 1))  # Null date for not completed
                
            # Set due date (internal only)
            self.set_qdate(self.overhaul_due, d.get('Due Date'))

            # Set status with color coding
            status = d.get('Status','')
            self.overhaul_status_lbl.setText(status)
            self.overhaul_status_lbl.setStatus(status.lower())

            # Enable completion button if overhaul is not completed
            is_completed = status.lower() == 'completed' or (done_date_str and done_date_str != '-')
            self.complete_btn.setEnabled(not is_completed)
            
        except Exception as e:
            logger.error(f"Error populating details: {e}")

    def on_add(self):
        """Start adding a new overhaul record."""
        # Clear form for new entry
        self.current_overhaul_id = None
        self.clear_form()

        # Show equipment selection dropdown and hide equipment label
        self.equipment_combo.show()
        self.detail_equipment.hide()

        # Populate equipment dropdown
        self.populate_equipment_dropdown()

        # Enable form editing for new entry
        self.set_form_enabled(True)

    def populate_equipment_dropdown(self):
        """Populate the equipment selection dropdown."""
        self.equipment_combo.clear()
        self.equipment_combo.addItem("Select Equipment...", None)

        # Get all equipment - Equipment.get_all() returns dictionaries
        equipment_list = Equipment.get_all()
        for equipment in equipment_list:
            # Use utils.format_equipment_display if available, otherwise format manually
            try:
                display_text = utils.format_equipment_display(equipment)
            except:
                # Fallback formatting - equipment is a dictionary from get_all()
                ba_number = equipment.get('ba_number', 'No BA') or 'No BA'
                make_type = equipment.get('make_and_type', 'Unknown') or 'Unknown'
                display_text = f"{make_type} - {ba_number}"

            self.equipment_combo.addItem(display_text, equipment['equipment_id'])

    def on_equipment_selected(self):
        """Handle equipment selection from dropdown."""
        equipment_id = self.equipment_combo.currentData()
        if equipment_id is None:
            # "Select Equipment..." option selected
            self.detail_ba.setText("")
            self.detail_meterage.setText("")
            self.release_date_edit.setDate(QDate(1900, 1, 1))  # Null date
            return

        # Get equipment details - Equipment.get_active() returns dictionaries
        equipment_list = Equipment.get_active()
        equipment = next((eq for eq in equipment_list if eq['equipment_id'] == equipment_id), None)

        if equipment:
            # Auto-fill fields - equipment is a dictionary from get_active()
            self.detail_ba.setText(str(equipment.get('ba_number', '') or ''))
            meterage = equipment.get('meterage_kms', 0) or 0
            self.detail_meterage.setText(self.format_kilometers(meterage))

            # FIX: Auto-populate release date from equipment's date_of_release first, then date_of_commission
            release_date_val = equipment.get('date_of_release') or equipment.get('date_of_commission')
            if release_date_val and release_date_val not in ['', 'None', None]:
                try:
                    from datetime import datetime
                    if isinstance(release_date_val, str):
                        # Parse various date formats
                        parsed_date = None
                        for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%Y/%m/%d']:
                            try:
                                parsed_date = datetime.strptime(release_date_val.split(' ')[0], fmt).date()
                                break
                            except ValueError:
                                continue
                        if parsed_date:
                            self.release_date_edit.setDate(QDate.fromString(parsed_date.isoformat(), 'yyyy-MM-dd'))
                        else:
                            self.release_date_edit.setDate(QDate(1900, 1, 1))  # Null date
                    else:
                        # If it's already a date object
                        self.release_date_edit.setDate(QDate.fromString(release_date_val.isoformat(), 'yyyy-MM-dd'))
                except Exception as e:
                    logger.debug(f"Error setting release date: {e}")
                    self.release_date_edit.setDate(QDate(1900, 1, 1))  # Null date
            else:
                self.release_date_edit.setDate(QDate(1900, 1, 1))  # Null date

            # Calculate and set default due dates based on release date
            self.calculate_default_due_dates(equipment)

    def calculate_default_due_dates(self, equipment):
        """Calculate and set default due dates for overhauls."""
        commission_date = equipment.get('date_of_commission')
        if not commission_date:
            return

        try:
            from datetime import datetime, timedelta

            # Parse commission date
            if isinstance(commission_date, str):
                commission_date = datetime.strptime(commission_date.split(' ')[0], '%Y-%m-%d').date()

            if self.overhaul_type == 'OH-I':
                # Calculate OH-I due date (15 years from commission)
                due_date = commission_date + timedelta(days=365*15)
                self.overhaul_due.setDate(QDate.fromString(due_date.isoformat(), 'yyyy-MM-dd'))
            else:  # OH-II
                from models import Overhaul
                overhauls = Overhaul.get_by_equipment(equipment['equipment_id'])
                oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)

                if oh1 and oh1.get('done_date'):
                    try:
                        oh1_done = datetime.strptime(oh1['done_date'].split(' ')[0], '%Y-%m-%d').date()
                        oh2_due = oh1_done + timedelta(days=365*10)
                        self.overhaul_due.setDate(QDate.fromString(oh2_due.isoformat(), 'yyyy-MM-dd'))
                    except Exception:
                        self.overhaul_due.setDate(QDate.currentDate())
                else:
                    # Leave due date unset when OH-I not completed
                    self.overhaul_due.setDate(QDate.currentDate())

            # Refresh preview
            self.update_status_preview()

        except Exception as e:
            logger.error(f"Error calculating default due dates: {e}")
            # Set to current date as fallback and refresh preview
            self.overhaul_due.setDate(QDate.currentDate())
            self.update_status_preview()

    def on_edit(self):
        """Enable editing of the selected overhaul record."""
        if self.current_overhaul_id:
            self.set_form_enabled(True)

    def set_form_enabled(self, enabled):
        """Enable or disable form editing."""
        self.release_date_edit.setEnabled(enabled)
        self.overhaul_done.setEnabled(enabled)
        # Note: overhaul_due is always hidden and handled internally
        # Equipment combo is shown/hidden separately
        self.save_btn.setVisible(enabled)

    def on_complete_overhaul(self):
        """Handle overhaul completion button click."""
        if not self.current_overhaul_id:
            QMessageBox.warning(self, "No Selection", "Please select an overhaul record to complete.")
            return

        # Get current overhaul data
        data_dict = next((d for d in self.all_data if d['ID']==self.current_overhaul_id), None)
        if not data_dict:
            QMessageBox.warning(self, "Error", "Could not find overhaul record.")
            return

        # Check if already completed
        if data_dict.get('Status', '').lower() == 'completed':
            QMessageBox.information(self, "Already Completed", f"This {self.overhaul_type} overhaul is already marked as completed.")
            return

        # Show completion dialog
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QDateEdit, QTextEdit

        dialog = QDialog(self)
        dialog.setWindowTitle(f"Complete {self.overhaul_type} Overhaul")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)

        # Equipment info
        info_label = QLabel(f"Equipment: {data_dict.get('Equipment', '')}\nBA Number: {data_dict.get('BA Number', '')}")
        layout.addWidget(info_label)

        # Completion date
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("Completion Date:"))
        completion_date = QDateEdit(calendarPopup=True)
        completion_date.setDate(QDate.currentDate())
        date_layout.addWidget(completion_date)
        layout.addLayout(date_layout)

        # Notes
        layout.addWidget(QLabel("Completion Notes (optional):"))
        notes_edit = QTextEdit()
        notes_edit.setMaximumHeight(100)
        layout.addWidget(notes_edit)

        # Buttons
        button_layout = QHBoxLayout()
        complete_btn = QPushButton("Complete Overhaul")
        cancel_btn = QPushButton("Cancel")

        complete_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(complete_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        if dialog.exec_() == QDialog.Accepted:
            try:
                # Determine equipment ID directly from overhaul record
                equipment_id = None
                try:
                    oh_rec = Overhaul.get_by_id(self.current_overhaul_id)
                    if oh_rec:
                        equipment_id = oh_rec.get('equipment_id')
                except Exception:
                    pass

                if not equipment_id:
                    QMessageBox.warning(self, "Error", "Could not find equipment record.")
                    return

                # Complete the overhaul using overhaul service
                completion_date_str = completion_date.date().toString('yyyy-MM-dd')
                completion_notes = notes_edit.toPlainText().strip()

                overhaul_service.complete_overhaul(
                    equipment_id=equipment_id,
                    overhaul_type=self.overhaul_type,
                    completion_date=completion_date_str,
                    completion_notes=completion_notes if completion_notes else None
                )

                # Refresh data and UI
                self.load_data()
                QMessageBox.information(self, "Success", f"{self.overhaul_type} overhaul completed successfully!")

            except Exception as e:
                logger.error(f"Error completing overhaul: {e}")
                QMessageBox.critical(self, "Error", f"Failed to complete overhaul:\n{str(e)}")

    def on_save(self):
        """Save overhaul data for the specific overhaul type."""
        try:
            # Handle both new and existing overhaul records
            if self.current_overhaul_id:
                # Editing existing overhaul
                overhaul = Overhaul.get_by_id(self.current_overhaul_id)
                if not overhaul:
                    QMessageBox.warning(self, "Error", "Overhaul record not found.")
                    return
                eq_id = overhaul['equipment_id']
            else:
                # Creating new overhaul
                eq_id = self.equipment_combo.currentData()
                if not eq_id:
                    QMessageBox.warning(self, "Validation Error", "Please select an equipment.")
                    return

            # Get dates from form - handle null dates properly
            release_date_obj = self.release_date_edit.date()
            release_str = None
            if release_date_obj.isValid() and release_date_obj != QDate(1900, 1, 1):
                release_str = self.qdate_to_str(release_date_obj)

            done_date_obj = self.overhaul_done.date()
            done_str = None
            if done_date_obj.isValid() and done_date_obj != QDate(1900, 1, 1):
                done_str = self.qdate_to_str(done_date_obj)

            due_str = self.qdate_to_str(self.overhaul_due.date())

            # Update equipment's date_of_release if changed
            if release_str:
                try:
                    equipment = Equipment.get_by_id(eq_id)
                    if equipment:
                        # Update equipment's date_of_release field
                        query = "UPDATE equipment SET date_of_release = %s WHERE equipment_id = %s"
                        database.execute_query(query, (release_str, eq_id))
                        logger.info(f"Updated equipment {eq_id} date_of_release to {release_str}")
                except Exception as e:
                    logger.warning(f"Could not update equipment release date: {e}")

            # Validate completion logic
            if done_str and due_str:
                from datetime import datetime, timedelta
                done_date = datetime.strptime(done_str, '%Y-%m-%d').date()
                due_date = datetime.strptime(due_str, '%Y-%m-%d').date()
                if done_date > due_date + timedelta(days=365):  # Allow 1 year grace period
                    reply = QMessageBox.question(self, "Date Validation",
                                               f"{self.overhaul_type} was completed significantly after due date.\n"
                                               f"Due: {due_str}\nDone: {done_str}\n"
                                               f"Continue saving?",
                                               QMessageBox.Yes | QMessageBox.No)
                    if reply == QMessageBox.No:
                        return

            # Get equipment details for status calculation
            equipment = Equipment.get_by_id(eq_id)
            if not equipment:
                QMessageBox.warning(self, "Error", "Equipment not found.")
                return

            # Calculate status
            meterage = getattr(equipment, 'meterage_kms', 0) or 0

            # For OH-II, we need OH-I completion date
            oh1_done_date = None
            if self.overhaul_type == 'OH-II':
                existing_overhauls = Overhaul.get_by_equipment(eq_id)
                oh1 = next((oh for oh in existing_overhauls if oh.get('overhaul_type') == 'OH-I'), None)
                oh1_done_date = oh1.get('done_date') if oh1 else None

            status_val = overhaul_service.get_overhaul_status(
                self.overhaul_type,
                due_str,
                done_str,
                date_of_commission=release_str,
                oh1_done_date=oh1_done_date,
                meterage_km=meterage
            )

            # Check if overhaul already exists for this equipment and type
            existing_overhauls = Overhaul.get_by_equipment(eq_id)
            existing_overhaul = next((oh for oh in existing_overhauls if oh.get('overhaul_type') == self.overhaul_type), None)

            # Save or update overhaul
            if existing_overhaul:
                Overhaul.update(existing_overhaul['overhaul_id'],
                              done_date=done_str,
                              due_date=due_str,
                              status=status_val,
                              meter_reading=meterage)
            else:
                overhaul = Overhaul(
                    equipment_id=eq_id,
                    overhaul_type=self.overhaul_type,
                    done_date=done_str,
                    due_date=due_str,
                    status=status_val,
                    meter_reading=meterage
                )
                overhaul.save()

            # Refresh data and UI
            self.load_data()
            self.detail_ba.setText(str(getattr(equipment, 'ba_number', '') or ''))
            self.detail_meterage.setText(self.format_kilometers(meterage))
            
            # Refresh the parent widget's data too to update the table
            parent_widget = self.parent()
            if parent_widget and hasattr(parent_widget, 'load_data'):
                parent_widget.load_data()

            # Reset UI state after save
            self.equipment_combo.hide()
            self.detail_equipment.show()
            self.current_overhaul_id = None

            # Disable form editing after save
            self.set_form_enabled(False)

            QMessageBox.information(self, "Success", f"{self.overhaul_type} overhaul data saved successfully.")

        except Exception as e:
            logger.error(f'Save failed: {e}')
            QMessageBox.critical(self, "Save Error", f"Failed to save {self.overhaul_type} overhaul data:\n{str(e)}")

    def on_delete(self):
        """Delete the selected overhaul record."""
        if not self.current_overhaul_id:
            return

        reply = QMessageBox.question(self, "Confirm Delete",
                                   f"Are you sure you want to delete this {self.overhaul_type} overhaul record?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                Overhaul.delete_by_id(self.current_overhaul_id)
                self.load_data()
                QMessageBox.information(self, "Success", f"{self.overhaul_type} overhaul record deleted successfully.")
            except Exception as e:
                logger.error(f'Delete failed: {e}')
                QMessageBox.critical(self, "Delete Error", f"Failed to delete overhaul record:\n{str(e)}")

    # ----- local date helpers -----
    def qdate_to_date(self, qdate: 'QDate'):
        """Convert QDate -> datetime.date or None."""
        if qdate and qdate.isValid():
            return date(qdate.year(), qdate.month(), qdate.day())
        return None

    def update_status_preview(self):
        """Recalculate status label from current form dates for instant feedback."""
        try:
            # Get dates from form
            due_dt = None
            if self.overhaul_due.date().isValid():
                due_dt = self.qdate_to_date(self.overhaul_due.date())
                
            done_dt = None
            if self.overhaul_done.date().isValid() and self.overhaul_done.date() != QDate(1900, 1, 1):
                done_dt = self.qdate_to_date(self.overhaul_done.date())
                
            release_dt = None
            if self.release_date_edit.date().isValid() and self.release_date_edit.date() != QDate(1900, 1, 1):
                release_dt = self.qdate_to_date(self.release_date_edit.date())
                
            if due_dt is None:
                status = "unknown"
            else:
                status = overhaul_service.get_overhaul_status(
                    self.overhaul_type,
                    due_dt,
                    done_dt,
                    date_of_commission=release_dt
                )
            self.overhaul_status_lbl.setText(status)
            self.overhaul_status_lbl.setStatus(status.lower())
        except Exception as e:
            logger.error(f"Status preview error: {e}")

    def set_qdate(self, widget, date_str):
        """Set QDateEdit widget from a DD/MM/YYYY or YYYY-MM-DD string."""
        if not date_str or date_str in ['-', '', 'None', None]:
            widget.setDate(QDate(1900, 1, 1))  # Set to null date
            return
        try:
            from datetime import datetime
            if isinstance(date_str, str):
                for fmt in ('%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y'):
                    try:
                        dt = datetime.strptime(date_str.split(' ')[0], fmt)
                        widget.setDate(QDate(dt.year, dt.month, dt.day))
                        return
                    except ValueError:
                        continue
        except Exception:
            pass
        widget.setDate(QDate(1900, 1, 1))  # Set to null date if parsing fails

    def qdate_to_str(self, qdate):
        return qdate.toString('yyyy-MM-dd')

    def format_date(self, value):
        """Convert date or string value to DD/MM/YYYY string via utils."""
        return utils.format_date_for_display(value)

    def format_kilometers(self, value):
        """Return kilometer value with commas and no decimals, or '-' if not valid."""
        try:
            if value in [None, '', 'None']:
                return '-'
            val = float(value)
            if val <= 0:
                return '-'
            return f"{val:,.0f}"
        except (ValueError, TypeError):
            return str(value)
