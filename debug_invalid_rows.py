#!/usr/bin/env python3
"""
Debug script to analyze why rows are being marked as invalid in Excel import.
This script will provide detailed logging of the validation process.
"""

import pandas as pd
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from robust_excel_importer_working import RobustExcelImporter
import logging

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('invalid_rows_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class InvalidRowsDebugger:
    def __init__(self):
        self.importer = RobustExcelImporter()
        self.invalid_reasons = []
        
    def analyze_excel_file(self, excel_path):
        """Analyze an Excel file and report detailed invalid row information."""
        logger.info(f"Starting analysis of: {excel_path}")
        
        try:
            # Read Excel file to get sheet names
            excel_file = pd.ExcelFile(excel_path)
            sheet_names = excel_file.sheet_names
            logger.info(f"Found sheets: {sheet_names}")
            
            for sheet_name in sheet_names:
                logger.info(f"\n{'='*50}")
                logger.info(f"ANALYZING SHEET: {sheet_name}")
                logger.info(f"{'='*50}")
                
                self.analyze_sheet(excel_path, sheet_name)
                
        except Exception as e:
            logger.error(f"Error analyzing Excel file: {e}")
            
    def analyze_sheet(self, excel_path, sheet_name):
        """Analyze a specific sheet and report invalid rows."""
        try:
            # Read the sheet
            df = self.importer._read_sheet_with_fallback(excel_path, sheet_name)
            if df is None or df.empty:
                logger.warning(f"Could not read sheet {sheet_name} or it's empty")
                return
                
            logger.info(f"Sheet {sheet_name} has {len(df)} total rows")
            
            # Get column mapping
            col_map = self.importer._map_equipment_columns(df.columns)
            logger.info(f"Column mapping: {col_map}")
            
            valid_count = 0
            invalid_count = 0
            
            # Analyze each row
            for index, row in df.iterrows():
                logger.info(f"\n--- ROW {index + 1} ---")
                
                # Extract equipment data
                equipment_data = self.importer._extract_equipment_data(row, col_map, sheet_name)
                logger.info(f"Extracted data: {equipment_data}")
                
                # Check if data extraction succeeded
                if not equipment_data:
                    logger.warning(f"ROW {index + 1}: INVALID - No equipment data extracted")
                    invalid_count += 1
                    self.invalid_reasons.append(f"Sheet {sheet_name}, Row {index + 1}: No equipment data extracted")
                    continue
                    
                # Check if make_and_type exists
                if not equipment_data.get('make_and_type'):
                    logger.warning(f"ROW {index + 1}: INVALID - No make_and_type found")
                    invalid_count += 1
                    self.invalid_reasons.append(f"Sheet {sheet_name}, Row {index + 1}: No make_and_type found")
                    continue
                    
                # Check validation
                is_valid = self.importer._is_valid_equipment_record(equipment_data)
                if is_valid:
                    logger.info(f"ROW {index + 1}: VALID - {equipment_data.get('make_and_type')}")
                    valid_count += 1
                else:
                    logger.warning(f"ROW {index + 1}: INVALID - Failed validation: {equipment_data.get('make_and_type')}")
                    invalid_count += 1
                    
                    # Determine specific reason for invalidity
                    reason = self.get_invalid_reason(equipment_data)
                    self.invalid_reasons.append(f"Sheet {sheet_name}, Row {index + 1}: {reason}")
                    
            logger.info(f"\nSHEET {sheet_name} SUMMARY:")
            logger.info(f"Total rows: {len(df)}")
            logger.info(f"Valid equipment rows: {valid_count}")
            logger.info(f"Invalid rows: {invalid_count}")
            
        except Exception as e:
            logger.error(f"Error analyzing sheet {sheet_name}: {e}")
            
    def get_invalid_reason(self, equipment_data):
        """Determine the specific reason why a row is invalid."""
        make_and_type = equipment_data.get('make_and_type', '').strip().upper()
        ba_number = equipment_data.get('ba_number', '').strip().upper()
        serial_number = equipment_data.get('serial_number', '').strip().upper()
        
        # Check for invalid indicators
        invalid_indicators = [
            'NOT ASSIGNED', 'ALL DATA ARE CORRECT', 'ALL DATA CORRECT',
            'HEADER', 'TOTAL', 'SUMMARY', 'VALIDATION', 'CHECK', 'VERIFY'
        ]
        
        for indicator in invalid_indicators:
            if indicator in make_and_type:
                return f"Invalid text in make_and_type: '{indicator}'"
            if indicator in ba_number:
                return f"Invalid text in ba_number: '{indicator}'"
            if indicator in serial_number:
                return f"Invalid text in serial_number: '{indicator}'"
                
        # Check for generic fallback names
        if make_and_type.startswith('EQUIPMENT FROM'):
            meaningful_keywords = [
                'BMP', 'TATRA', 'TRUCK', 'TANK', 'VEHICLE', 'GENERATOR', 
                'JCB', 'DOZER', 'CRANE', 'TRAILER', 'ALS', 'MSS', 'CT'
            ]
            has_meaningful_content = any(keyword in make_and_type for keyword in meaningful_keywords)
            if not has_meaningful_content:
                return f"Generic fallback name without meaningful keywords: '{make_and_type}'"
                
        # Check for all-zero validation
        meterage = equipment_data.get('meterage_kms', 0)
        vintage = equipment_data.get('vintage_years', 0)
        if meterage == 0 and vintage == 0 and not ba_number:
            return "All numeric fields zero and no BA number"
            
        return "Unknown validation failure"
        
    def print_summary(self):
        """Print a summary of all invalid reasons."""
        logger.info(f"\n{'='*60}")
        logger.info("INVALID ROWS SUMMARY")
        logger.info(f"{'='*60}")
        
        if not self.invalid_reasons:
            logger.info("No invalid rows found!")
            return
            
        logger.info(f"Total invalid rows: {len(self.invalid_reasons)}")
        logger.info("\nDetailed breakdown:")
        
        for i, reason in enumerate(self.invalid_reasons, 1):
            logger.info(f"{i:2d}. {reason}")

def main():
    """Main function to run the debug analysis."""
    if len(sys.argv) != 2:
        print("Usage: python debug_invalid_rows.py <excel_file_path>")
        sys.exit(1)
        
    excel_path = sys.argv[1]
    if not os.path.exists(excel_path):
        print(f"Error: File {excel_path} does not exist")
        sys.exit(1)
        
    debugger = InvalidRowsDebugger()
    debugger.analyze_excel_file(excel_path)
    debugger.print_summary()
    
    logger.info(f"\nDetailed log saved to: invalid_rows_debug.log")

if __name__ == "__main__":
    main()
