"""
Add User Dialog for PROJECT-ALPHA
Simplified PIN-based user registration interface for kiosk-style authentication.
"""

import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QComboBox, QFrame,
                             QMessageBox, QProgressBar, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon

from auth.authentication_service import AuthenticationService
from ui.window_utils import DPIScaler
from ui.common_styles import PRIMARY_BUTTON_STYLE, BUTTON_STYLE, DANGER_BUTTON_STYLE

logger = logging.getLogger(__name__)


class PINRegistrationWorker(QThread):
    """Worker thread for PIN-based user registration to prevent UI blocking."""
    
    registration_complete = pyqtSignal(bool, str, int)  # success, message, user_id
    registration_error = pyqtSignal(str)
    
    def __init__(self, username, pin, role_name, created_by_user_id):
        super().__init__()
        self.username = username
        self.pin = pin
        self.role_name = role_name
        self.created_by_user_id = created_by_user_id
    
    def run(self):
        """Perform PIN-based user registration in background thread."""
        try:
            success, message, user_id = AuthenticationService.register_user_pin(
                self.username, self.pin, self.role_name, self.created_by_user_id
            )
            self.registration_complete.emit(success, message, user_id)
        except Exception as e:
            logger.error(f"PIN registration error: {e}")
            self.registration_error.emit(str(e))


class AddUserDialog(QDialog):
    """Simplified user registration dialog for PIN-based authentication."""
    
    registration_successful = pyqtSignal(int)  # Emits user_id when successful
    
    def __init__(self, parent=None, current_user=None):
        """Initialize add user dialog."""
        super().__init__(parent)
        self.current_user = current_user
        self.registration_worker = None
        
        self.setup_ui()
        self.load_roles()
        self.apply_styles()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("PROJECT-ALPHA - Add New User")
        self.setModal(True)
        self.setFixedSize(DPIScaler.scale_size(450), DPIScaler.scale_size(400))
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(DPIScaler.scale_size(30), DPIScaler.scale_size(30),
                                      DPIScaler.scale_size(30), DPIScaler.scale_size(30))
        main_layout.setSpacing(DPIScaler.scale_size(20))
        
        # Header
        self.create_header(main_layout)
        
        # Form section
        self.create_form(main_layout)
        
        # Progress section
        self.create_progress(main_layout)
        
        # Footer
        self.create_footer(main_layout)
        
    def create_header(self, layout):
        """Create dialog header."""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(DPIScaler.scale_size(10))
        
        # Title
        title_label = QLabel("Add New User")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Create a new user profile with PIN authentication")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
        
    def create_form(self, layout):
        """Create registration form."""
        form_group = QGroupBox("User Information")
        form_group.setObjectName("formGroup")
        form_layout = QFormLayout(form_group)
        form_layout.setSpacing(DPIScaler.scale_size(15))
        
        # Username field
        self.username_edit = QLineEdit()
        self.username_edit.setObjectName("usernameEdit")
        self.username_edit.setPlaceholderText("Enter username (display name)")
        self.username_edit.textChanged.connect(self.validate_form)
        form_layout.addRow("Username:", self.username_edit)
        
        # PIN field
        self.pin_edit = QLineEdit()
        self.pin_edit.setObjectName("pinEdit")
        self.pin_edit.setPlaceholderText("Enter 4-6 digit PIN")
        self.pin_edit.setMaxLength(6)
        self.pin_edit.setEchoMode(QLineEdit.Password)
        self.pin_edit.textChanged.connect(self.validate_form)
        form_layout.addRow("PIN:", self.pin_edit)
        
        # PIN confirmation field
        self.pin_confirm_edit = QLineEdit()
        self.pin_confirm_edit.setObjectName("pinConfirmEdit")
        self.pin_confirm_edit.setPlaceholderText("Confirm PIN")
        self.pin_confirm_edit.setMaxLength(6)
        self.pin_confirm_edit.setEchoMode(QLineEdit.Password)
        self.pin_confirm_edit.textChanged.connect(self.validate_form)
        form_layout.addRow("Confirm PIN:", self.pin_confirm_edit)
        
        # Role selection
        self.role_combo = QComboBox()
        self.role_combo.setObjectName("roleCombo")
        form_layout.addRow("Role:", self.role_combo)
        
        layout.addWidget(form_group)
        
        # Validation message
        self.validation_label = QLabel("")
        self.validation_label.setObjectName("validationLabel")
        self.validation_label.setWordWrap(True)
        self.validation_label.hide()
        layout.addWidget(self.validation_label)
        
    def create_progress(self, layout):
        """Create progress indicator."""
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
    def create_footer(self, layout):
        """Create dialog footer."""
        footer_layout = QHBoxLayout()
        footer_layout.setSpacing(DPIScaler.scale_size(15))
        
        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setObjectName("cancelButton")
        self.cancel_button.clicked.connect(self.reject)
        footer_layout.addWidget(self.cancel_button)
        
        # Add spacer
        footer_layout.addStretch()
        
        # Create button
        self.create_button = QPushButton("Create User")
        self.create_button.setObjectName("createButton")
        self.create_button.clicked.connect(self.create_user)
        self.create_button.setEnabled(False)
        footer_layout.addWidget(self.create_button)
        
        layout.addLayout(footer_layout)
        
    def load_roles(self):
        """Load available roles into combo box."""
        try:
            roles = AuthenticationService.get_all_roles()
            
            for role in roles:
                self.role_combo.addItem(role['role_name'], role['role_id'])
            
            # Set default to Read-Only
            read_only_index = self.role_combo.findText("Read-Only")
            if read_only_index >= 0:
                self.role_combo.setCurrentIndex(read_only_index)
                
        except Exception as e:
            logger.error(f"Error loading roles: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load roles: {str(e)}")
            
    def validate_form(self):
        """Validate form inputs and enable/disable create button."""
        username = self.username_edit.text().strip()
        pin = self.pin_edit.text().strip()
        pin_confirm = self.pin_confirm_edit.text().strip()
        
        # Clear previous validation message
        self.validation_label.hide()
        
        # Check if all fields are filled
        if not username or not pin or not pin_confirm:
            self.create_button.setEnabled(False)
            return
        
        # Validate username
        if len(username) < 3:
            self.show_validation_error("Username must be at least 3 characters")
            return
        
        # Validate PIN format
        if not pin.isdigit():
            self.show_validation_error("PIN must contain only digits")
            return
            
        if not (4 <= len(pin) <= 6):
            self.show_validation_error("PIN must be 4-6 digits")
            return
        
        # Check PIN confirmation
        if pin != pin_confirm:
            self.show_validation_error("PINs do not match")
            return
        
        # All validation passed
        self.create_button.setEnabled(True)
        
    def show_validation_error(self, message):
        """Show validation error message."""
        self.validation_label.setText(message)
        self.validation_label.show()
        self.create_button.setEnabled(False)
        
    def create_user(self):
        """Create new user with PIN authentication."""
        username = self.username_edit.text().strip()
        pin = self.pin_edit.text().strip()
        role_name = self.role_combo.currentText()
        
        # Get created_by user ID
        created_by_user_id = self.current_user.user_id if self.current_user else None
        
        # Disable form during registration
        self.set_form_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Start registration worker
        self.registration_worker = PINRegistrationWorker(
            username, pin, role_name, created_by_user_id
        )
        self.registration_worker.registration_complete.connect(self.on_registration_complete)
        self.registration_worker.registration_error.connect(self.on_registration_error)
        self.registration_worker.start()
        
    def set_form_enabled(self, enabled):
        """Enable/disable form controls."""
        self.username_edit.setEnabled(enabled)
        self.pin_edit.setEnabled(enabled)
        self.pin_confirm_edit.setEnabled(enabled)
        self.role_combo.setEnabled(enabled)
        self.create_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
        
    @pyqtSlot(bool, str, int)
    def on_registration_complete(self, success, message, user_id):
        """Handle registration completion."""
        self.progress_bar.setVisible(False)
        self.set_form_enabled(True)
        
        if success:
            logger.info(f"User registration successful: {message}")
            QMessageBox.information(self, "Success", message)
            self.registration_successful.emit(user_id)
            self.accept()
        else:
            logger.warning(f"User registration failed: {message}")
            QMessageBox.warning(self, "Registration Failed", message)
            
    @pyqtSlot(str)
    def on_registration_error(self, error_message):
        """Handle registration error."""
        self.progress_bar.setVisible(False)
        self.set_form_enabled(True)
        
        logger.error(f"Registration error: {error_message}")
        QMessageBox.critical(self, "Registration Error", f"An error occurred during registration:\n{error_message}")
        
    def apply_styles(self):
        """Apply styling to the dialog."""
        self.setStyleSheet(f"""
            QDialog {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }}
            
            QFrame#headerFrame {{
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
                padding: {DPIScaler.scale_size(15)}px;
            }}
            
            QLabel#titleLabel {{
                font-size: {DPIScaler.scale_font_size(20)}px;
                font-weight: bold;
                color: #212529;
            }}
            
            QLabel#subtitleLabel {{
                font-size: {DPIScaler.scale_font_size(12)}px;
                color: #6c757d;
            }}
            
            QGroupBox#formGroup {{
                font-size: {DPIScaler.scale_font_size(13)}px;
                font-weight: bold;
                color: #495057;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
                margin-top: {DPIScaler.scale_size(10)}px;
                padding-top: {DPIScaler.scale_size(10)}px;
            }}
            
            QLineEdit {{
                padding: {DPIScaler.scale_size(8)}px {DPIScaler.scale_size(12)}px;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(4)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
                background-color: #ffffff;
            }}
            
            QLineEdit:focus {{
                border-color: #007bff;
                outline: none;
            }}
            
            QComboBox {{
                padding: {DPIScaler.scale_size(8)}px {DPIScaler.scale_size(12)}px;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(4)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
                background-color: #ffffff;
            }}
            
            QLabel#validationLabel {{
                color: #dc3545;
                font-size: {DPIScaler.scale_font_size(11)}px;
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: {DPIScaler.scale_size(4)}px;
                padding: {DPIScaler.scale_size(8)}px;
            }}
            
            QPushButton#createButton {{
                {PRIMARY_BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                min-width: {DPIScaler.scale_size(120)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
            }}
            
            QPushButton#cancelButton {{
                {BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                min-width: {DPIScaler.scale_size(80)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
            }}
            
            QProgressBar {{
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(4)}px;
                text-align: center;
                font-size: {DPIScaler.scale_font_size(11)}px;
            }}
            
            QProgressBar::chunk {{
                background-color: #007bff;
                border-radius: {DPIScaler.scale_size(2)}px;
            }}
        """)
