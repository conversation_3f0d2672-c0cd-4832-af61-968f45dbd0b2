"""
Session Manager for PROJECT-ALPHA
Handles user sessions, permissions, and activity tracking.
"""

import json
import secrets
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Set
from dataclasses import dataclass

from database import get_db_connection
from auth.authentication_service import User

logger = logging.getLogger(__name__)


@dataclass
class Session:
    """Session data class."""
    session_id: str
    user: User
    login_time: datetime
    last_activity: datetime
    ip_address: Optional[str]
    user_agent: Optional[str]
    session_data: Dict[str, Any]


class SessionManager:
    """Manages user sessions and permissions for PROJECT-ALPHA."""
    
    # Session configuration
    DEFAULT_TIMEOUT_HOURS = 8  # Default session timeout
    WARNING_MINUTES = 15  # Warning before session expires
    CLEANUP_INTERVAL_HOURS = 24  # How often to clean up expired sessions
    
    def __init__(self):
        """Initialize session manager."""
        self.current_session: Optional[Session] = None
        self._last_cleanup = datetime.now()
    
    def create_session(self, user: User, ip_address: str = None, user_agent: str = None) -> str:
        """
        Create a new session for authenticated user.
        
        Args:
            user: Authenticated user object
            ip_address: Client IP address
            user_agent: Client user agent string
            
        Returns:
            Session ID string
        """
        try:
            # Generate secure session ID
            session_id = secrets.token_urlsafe(32)
            
            # Create session data
            session_data = {
                'timeout_hours': self.DEFAULT_TIMEOUT_HOURS,
                'created_at': datetime.now().isoformat(),
                'permissions_cache': user.permissions
            }
            
            # Store session in database
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO user_sessions 
                    (session_id, user_id, login_time, last_activity, ip_address, user_agent, session_data)
                    VALUES (?, ?, datetime('now'), datetime('now'), ?, ?, ?)
                """, (
                    session_id,
                    user.user_id,
                    ip_address,
                    user_agent,
                    json.dumps(session_data)
                ))
                conn.commit()
            
            # Create session object
            self.current_session = Session(
                session_id=session_id,
                user=user,
                login_time=datetime.now(),
                last_activity=datetime.now(),
                ip_address=ip_address,
                user_agent=user_agent,
                session_data=session_data
            )
            
            logger.info(f"Session created for user '{user.username}' (ID: {session_id[:8]}...)")
            
            # Log session creation
            self._log_activity('SESSION_CREATED', 'user_sessions', session_id, 
                             {'user_id': user.user_id, 'username': user.username})
            
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session for user '{user.username}': {e}")
            raise RuntimeError("Failed to create session")
    
    def validate_session(self, session_id: str) -> bool:
        """
        Validate session and check if it's still active.
        
        Args:
            session_id: Session ID to validate
            
        Returns:
            True if session is valid and active, False otherwise
        """
        try:
            if not session_id:
                return False
            
            # Get session from database
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT s.*, u.*, r.role_name, r.permissions
                    FROM user_sessions s
                    JOIN users u ON s.user_id = u.user_id
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE s.session_id = ? AND s.is_active = 1 
                    AND u.is_active = 1 AND r.is_active = 1
                """, (session_id,))
                
                session_data = cursor.fetchone()
                
            if not session_data:
                logger.warning(f"Session validation failed: Session {session_id[:8]}... not found or inactive")
                return False
            
            # Check session timeout
            last_activity = datetime.fromisoformat(session_data['last_activity'])
            session_info = json.loads(session_data.get('session_data', '{}'))
            timeout_hours = session_info.get('timeout_hours', self.DEFAULT_TIMEOUT_HOURS)
            
            if datetime.now() - last_activity > timedelta(hours=timeout_hours):
                logger.warning(f"Session {session_id[:8]}... expired (last activity: {last_activity})")
                self.end_session(session_id)
                return False
            
            # Update last activity
            self._update_session_activity(session_id)
            
            # Update current session if it matches
            if self.current_session and self.current_session.session_id == session_id:
                self.current_session.last_activity = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating session {session_id[:8]}...: {e}")
            return False
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """
        Get session object by session ID.
        
        Args:
            session_id: Session ID
            
        Returns:
            Session object or None if not found/invalid
        """
        try:
            if not self.validate_session(session_id):
                return None
            
            # If this is the current session, return it
            if self.current_session and self.current_session.session_id == session_id:
                return self.current_session
            
            # Load session from database
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT s.*, u.*, r.role_name, r.permissions
                    FROM user_sessions s
                    JOIN users u ON s.user_id = u.user_id
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE s.session_id = ? AND s.is_active = 1
                """, (session_id,))
                
                data = cursor.fetchone()
                
            if not data:
                return None
            
            # Parse permissions and session data
            try:
                permissions = json.loads(data['permissions'])
                session_data = json.loads(data.get('session_data', '{}'))
            except (json.JSONDecodeError, TypeError):
                permissions = {}
                session_data = {}
            
            # Create User object
            user = User(
                user_id=data['user_id'],
                username=data['username'],
                full_name=data['full_name'],
                email=data['email'],
                role_id=data['role_id'],
                role_name=data['role_name'],
                permissions=permissions,
                is_active=bool(data['is_active']),
                last_login=data['last_login'],
                failed_login_attempts=data['failed_login_attempts'],
                account_locked_until=data['account_locked_until'],
                password_changed_at=data['password_changed_at'],
                created_at=data['created_at']
            )
            
            # Create Session object
            session = Session(
                session_id=session_id,
                user=user,
                login_time=datetime.fromisoformat(data['login_time']),
                last_activity=datetime.fromisoformat(data['last_activity']),
                ip_address=data['ip_address'],
                user_agent=data['user_agent'],
                session_data=session_data
            )
            
            return session
            
        except Exception as e:
            logger.error(f"Error getting session {session_id[:8]}...: {e}")
            return None
    
    def end_session(self, session_id: str) -> bool:
        """
        End a session and mark it as inactive.
        
        Args:
            session_id: Session ID to end
            
        Returns:
            True if session ended successfully, False otherwise
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE user_sessions 
                    SET is_active = 0, logout_time = datetime('now')
                    WHERE session_id = ?
                """, (session_id,))
                conn.commit()
            
            # Clear current session if it matches
            if self.current_session and self.current_session.session_id == session_id:
                user_info = {'user_id': self.current_session.user.user_id, 
                           'username': self.current_session.user.username}
                self.current_session = None
                
                # Log session end
                self._log_activity('SESSION_ENDED', 'user_sessions', session_id, user_info)
            
            logger.info(f"Session {session_id[:8]}... ended successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error ending session {session_id[:8]}...: {e}")
            return False
    
    def has_permission(self, permission: str) -> bool:
        """
        Check if current session has specific permission.
        
        Args:
            permission: Permission to check
            
        Returns:
            True if permission granted, False otherwise
        """
        if not self.current_session:
            return False
        
        return self.current_session.user.permissions.get(permission, False)
    
    def is_read_only(self) -> bool:
        """Check if current session is in read-only mode."""
        return not self.has_permission('write')
    
    def is_admin(self) -> bool:
        """Check if current session has admin privileges."""
        return self.has_permission('admin')
    
    def get_current_user(self) -> Optional[User]:
        """Get current authenticated user."""
        return self.current_session.user if self.current_session else None
    
    def get_current_session_id(self) -> Optional[str]:
        """Get current session ID."""
        return self.current_session.session_id if self.current_session else None
    
    def _update_session_activity(self, session_id: str) -> None:
        """Update session last activity timestamp."""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE user_sessions 
                    SET last_activity = datetime('now')
                    WHERE session_id = ?
                """, (session_id,))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error updating session activity for {session_id[:8]}...: {e}")
    
    def _log_activity(self, action_type: str, table_name: str, record_id: str, 
                     additional_data: Dict[str, Any] = None) -> None:
        """Log user activity to audit log."""
        try:
            user_id = self.current_session.user.user_id if self.current_session else None
            session_id = self.current_session.session_id if self.current_session else None
            
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO audit_log 
                    (user_id, session_id, action_type, table_name, record_id, 
                     new_values, operation_result, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
                """, (
                    user_id,
                    session_id,
                    action_type,
                    table_name,
                    record_id,
                    json.dumps(additional_data) if additional_data else None,
                    'SUCCESS'
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error logging activity: {e}")
    
    def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions from database.
        
        Returns:
            Number of sessions cleaned up
        """
        try:
            # Only run cleanup periodically
            if datetime.now() - self._last_cleanup < timedelta(hours=self.CLEANUP_INTERVAL_HOURS):
                return 0
            
            with get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Find expired sessions
                cursor.execute("""
                    SELECT session_id, last_activity, session_data
                    FROM user_sessions 
                    WHERE is_active = 1
                """)
                
                sessions = cursor.fetchall()
                expired_count = 0
                
                for session in sessions:
                    try:
                        last_activity = datetime.fromisoformat(session['last_activity'])
                        session_data = json.loads(session.get('session_data', '{}'))
                        timeout_hours = session_data.get('timeout_hours', self.DEFAULT_TIMEOUT_HOURS)
                        
                        if datetime.now() - last_activity > timedelta(hours=timeout_hours):
                            cursor.execute("""
                                UPDATE user_sessions 
                                SET is_active = 0, logout_time = datetime('now')
                                WHERE session_id = ?
                            """, (session['session_id'],))
                            expired_count += 1
                            
                    except (ValueError, TypeError, json.JSONDecodeError):
                        # Invalid session data, mark as expired
                        cursor.execute("""
                            UPDATE user_sessions 
                            SET is_active = 0, logout_time = datetime('now')
                            WHERE session_id = ?
                        """, (session['session_id'],))
                        expired_count += 1
                
                conn.commit()
                self._last_cleanup = datetime.now()
                
                if expired_count > 0:
                    logger.info(f"Cleaned up {expired_count} expired sessions")
                
                return expired_count
                
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
            return 0
