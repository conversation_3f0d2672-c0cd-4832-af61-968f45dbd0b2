"""
User Management Dialog for PROJECT-ALPHA
Provides comprehensive user account management interface for Administrator users.
"""

import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QHeaderView, QMessageBox,
                             QGroupBox, QLabel, QFrame, QAbstractItemView, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon
from auth.authentication_service import AuthenticationService
from auth.signup_dialog import SignUpDialog
from ui.window_utils import DPIScaler

logger = logging.getLogger(__name__)


class UserManagementDialog(QDialog):
    """
    Comprehensive user management dialog for Administrator users.
    Provides functionality to create, view, modify, and manage user accounts.
    """
    
    # Signals
    user_created = pyqtSignal(int)  # user_id
    user_modified = pyqtSignal(int)  # user_id
    user_deactivated = pyqtSignal(int)  # user_id
    
    def __init__(self, parent=None, current_user=None):
        """
        Initialize user management dialog.
        
        Args:
            parent: Parent widget
            current_user: Current authenticated administrator user
        """
        super().__init__(parent)
        self.current_user = current_user
        self.users_data = []
        
        if not current_user or current_user.role_name != 'Administrator':
            raise ValueError("User management requires Administrator privileges")
        
        self.setWindowTitle("PROJECT-ALPHA - User Management")
        self.setModal(True)
        self.setMinimumSize(DPIScaler.scale_size(800), DPIScaler.scale_size(600))
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        self.setup_connections()
        self.apply_styles()
        self.load_users()
        
        logger.info("UserManagementDialog initialized")
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(DPIScaler.scale_size(15))
        layout.setContentsMargins(DPIScaler.scale_size(20), DPIScaler.scale_size(20),
                                  DPIScaler.scale_size(20), DPIScaler.scale_size(20))
        
        # Create header
        self.create_header(layout)
        
        # Create user table
        self.create_user_table(layout)
        
        # Create action buttons
        self.create_action_buttons(layout)
        
        # Create status bar
        self.create_status_bar(layout)
    
    def create_header(self, layout):
        """Create header section with title and info."""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        
        # Title
        title_label = QLabel("User Account Management")
        title_font = QFont()
        title_font.setPointSize(DPIScaler.scale_font_size(16))
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Manage user accounts, roles, and permissions")
        subtitle_font = QFont()
        subtitle_font.setPointSize(DPIScaler.scale_font_size(10))
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
    
    def create_user_table(self, layout):
        """Create user table with user information."""
        table_group = QGroupBox("User Accounts")
        table_layout = QVBoxLayout(table_group)
        
        # Create table
        self.user_table = QTableWidget()
        self.user_table.setColumnCount(7)
        self.user_table.setHorizontalHeaderLabels([
            "ID", "Username", "Full Name", "Email", "Role", "Status", "Last Login"
        ])
        
        # Configure table
        self.user_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.user_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.user_table.setAlternatingRowColors(True)
        self.user_table.setSortingEnabled(True)
        
        # Set column widths
        header = self.user_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Username
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Full Name
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Email
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Role
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # Last Login
        
        # Enable context menu
        self.user_table.setContextMenuPolicy(Qt.CustomContextMenu)
        
        table_layout.addWidget(self.user_table)
        layout.addWidget(table_group)
    
    def create_action_buttons(self, layout):
        """Create action buttons for user management."""
        button_group = QGroupBox("Actions")
        button_layout = QHBoxLayout(button_group)
        button_layout.setSpacing(DPIScaler.scale_size(10))
        
        # Create New User button
        self.create_user_button = QPushButton("Create New User")
        self.create_user_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.create_user_button.setMinimumWidth(DPIScaler.scale_size(120))
        button_layout.addWidget(self.create_user_button)
        
        # Edit User button
        self.edit_user_button = QPushButton("Edit User")
        self.edit_user_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.edit_user_button.setMinimumWidth(DPIScaler.scale_size(100))
        self.edit_user_button.setEnabled(False)
        button_layout.addWidget(self.edit_user_button)
        
        # Reset Password button
        self.reset_password_button = QPushButton("Reset Password")
        self.reset_password_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.reset_password_button.setMinimumWidth(DPIScaler.scale_size(120))
        self.reset_password_button.setEnabled(False)
        button_layout.addWidget(self.reset_password_button)
        
        # Deactivate User button
        self.deactivate_user_button = QPushButton("Deactivate User")
        self.deactivate_user_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.deactivate_user_button.setMinimumWidth(DPIScaler.scale_size(120))
        self.deactivate_user_button.setEnabled(False)
        button_layout.addWidget(self.deactivate_user_button)
        
        button_layout.addStretch()
        
        # Refresh button
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.refresh_button.setMinimumWidth(DPIScaler.scale_size(80))
        button_layout.addWidget(self.refresh_button)
        
        # Close button
        self.close_button = QPushButton("Close")
        self.close_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.close_button.setMinimumWidth(DPIScaler.scale_size(80))
        button_layout.addWidget(self.close_button)
        
        layout.addWidget(button_group)
    
    def create_status_bar(self, layout):
        """Create status bar for displaying information."""
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #666; font-size: 10px; padding: 5px;")
        layout.addWidget(self.status_label)
    
    def setup_connections(self):
        """Set up signal connections."""
        # Button connections
        self.create_user_button.clicked.connect(self.create_new_user)
        self.edit_user_button.clicked.connect(self.edit_selected_user)
        self.reset_password_button.clicked.connect(self.reset_user_password)
        self.deactivate_user_button.clicked.connect(self.deactivate_selected_user)
        self.refresh_button.clicked.connect(self.load_users)
        self.close_button.clicked.connect(self.accept)
        
        # Table connections
        self.user_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.user_table.customContextMenuRequested.connect(self.show_context_menu)
        self.user_table.itemDoubleClicked.connect(self.edit_selected_user)
    
    def apply_styles(self):
        """Apply consistent styling to the dialog."""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                gridline-color: #e0e0e0;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #4CAF50;
                color: white;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

    def load_users(self):
        """Load all users from database and populate table."""
        try:
            self.status_label.setText("Loading users...")

            # Get all users from database
            from database import get_db_connection

            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT u.user_id, u.username, u.full_name, u.email,
                           r.role_name, u.is_active, u.last_login, u.created_at
                    FROM users u
                    JOIN roles r ON u.role_id = r.role_id
                    ORDER BY u.created_at DESC
                """)

                self.users_data = cursor.fetchall()

            # Populate table
            self.user_table.setRowCount(len(self.users_data))

            for row, user in enumerate(self.users_data):
                # ID
                self.user_table.setItem(row, 0, QTableWidgetItem(str(user['user_id'])))

                # Username
                self.user_table.setItem(row, 1, QTableWidgetItem(user['username']))

                # Full Name
                self.user_table.setItem(row, 2, QTableWidgetItem(user['full_name']))

                # Email
                email = user['email'] if user['email'] else ""
                self.user_table.setItem(row, 3, QTableWidgetItem(email))

                # Role
                self.user_table.setItem(row, 4, QTableWidgetItem(user['role_name']))

                # Status
                status = "Active" if user['is_active'] else "Inactive"
                status_item = QTableWidgetItem(status)
                if not user['is_active']:
                    status_item.setBackground(Qt.red)
                    status_item.setForeground(Qt.white)
                self.user_table.setItem(row, 5, status_item)

                # Last Login
                last_login = user['last_login'] if user['last_login'] else "Never"
                self.user_table.setItem(row, 6, QTableWidgetItem(last_login))

            # Update status
            self.status_label.setText(f"Loaded {len(self.users_data)} users")

            logger.info(f"Loaded {len(self.users_data)} users in management dialog")

        except Exception as e:
            logger.error(f"Error loading users: {e}")
            self.status_label.setText("Error loading users")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to load users: {str(e)}"
            )

    def on_selection_changed(self):
        """Handle table selection changes."""
        selected_rows = self.user_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        # Enable/disable buttons based on selection
        self.edit_user_button.setEnabled(has_selection)
        self.reset_password_button.setEnabled(has_selection)
        self.deactivate_user_button.setEnabled(has_selection)

        if has_selection:
            row = selected_rows[0].row()
            user_data = self.users_data[row]

            # Don't allow deactivating current user
            if user_data['user_id'] == self.current_user.user_id:
                self.deactivate_user_button.setEnabled(False)
                self.deactivate_user_button.setText("Cannot Deactivate Self")
            else:
                self.deactivate_user_button.setEnabled(True)
                if user_data['is_active']:
                    self.deactivate_user_button.setText("Deactivate User")
                else:
                    self.deactivate_user_button.setText("Activate User")

    def get_selected_user(self):
        """Get the currently selected user data."""
        selected_rows = self.user_table.selectionModel().selectedRows()
        if not selected_rows:
            return None

        row = selected_rows[0].row()
        return self.users_data[row] if row < len(self.users_data) else None

    def create_new_user(self):
        """Create a new user account."""
        try:
            # Show signup dialog in admin mode
            signup_dialog = SignUpDialog(
                parent=self,
                current_user=self.current_user,
                is_admin_mode=True
            )

            # Connect success signal
            signup_dialog.registration_successful.connect(self.on_user_created)

            # Show dialog
            result = signup_dialog.exec_()

            if result == QDialog.Accepted:
                logger.info("New user created successfully")

        except Exception as e:
            logger.error(f"Error creating new user: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to create new user: {str(e)}"
            )

    def on_user_created(self, user_id):
        """Handle successful user creation."""
        logger.info(f"New user created with ID: {user_id}")

        # Refresh user list
        self.load_users()

        # Emit signal
        self.user_created.emit(user_id)

        # Show success message
        QMessageBox.information(
            self,
            "Success",
            "New user account created successfully!"
        )

    def edit_selected_user(self):
        """Edit the selected user account."""
        selected_user = self.get_selected_user()
        if not selected_user:
            QMessageBox.warning(self, "No Selection", "Please select a user to edit.")
            return

        # TODO: Implement user editing dialog
        # For now, show a placeholder message
        QMessageBox.information(
            self,
            "Edit User",
            f"Edit user functionality will be implemented in the next phase.\n\n"
            f"Selected user: {selected_user['username']} ({selected_user['full_name']})\n"
            f"Role: {selected_user['role_name']}\n"
            f"Status: {'Active' if selected_user['is_active'] else 'Inactive'}"
        )

    def reset_user_password(self):
        """Reset password for the selected user."""
        selected_user = self.get_selected_user()
        if not selected_user:
            QMessageBox.warning(self, "No Selection", "Please select a user to reset password.")
            return

        # Confirm password reset
        reply = QMessageBox.question(
            self,
            "Reset Password",
            f"Are you sure you want to reset the password for user '{selected_user['username']}'?\n\n"
            f"This will generate a temporary password that must be changed on next login.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # TODO: Implement password reset functionality
            # For now, show a placeholder message
            QMessageBox.information(
                self,
                "Password Reset",
                f"Password reset functionality will be implemented in the next phase.\n\n"
                f"This would:\n"
                f"• Generate a secure temporary password\n"
                f"• Force password change on next login\n"
                f"• Send notification to user (if email configured)\n"
                f"• Log the password reset action"
            )

    def deactivate_selected_user(self):
        """Deactivate or activate the selected user."""
        selected_user = self.get_selected_user()
        if not selected_user:
            QMessageBox.warning(self, "No Selection", "Please select a user to modify.")
            return

        # Don't allow deactivating current user
        if selected_user['user_id'] == self.current_user.user_id:
            QMessageBox.warning(
                self,
                "Cannot Deactivate",
                "You cannot deactivate your own account."
            )
            return

        # Determine action
        is_active = selected_user['is_active']
        action = "deactivate" if is_active else "activate"

        # Confirm action
        reply = QMessageBox.question(
            self,
            f"{action.title()} User",
            f"Are you sure you want to {action} user '{selected_user['username']}'?\n\n"
            f"This will {'prevent' if is_active else 'allow'} the user from logging in.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Update user status in database
                from database import get_db_connection

                with get_db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE users
                        SET is_active = ?, updated_at = datetime('now')
                        WHERE user_id = ?
                    """, (not is_active, selected_user['user_id']))

                    conn.commit()

                # Refresh user list
                self.load_users()

                # Emit signal
                self.user_modified.emit(selected_user['user_id'])

                # Show success message
                QMessageBox.information(
                    self,
                    "Success",
                    f"User '{selected_user['username']}' has been {action}d successfully."
                )

                logger.info(f"User {selected_user['user_id']} {action}d by {self.current_user.user_id}")

            except Exception as e:
                logger.error(f"Error {action}ing user: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to {action} user: {str(e)}"
                )

    def show_context_menu(self, position):
        """Show context menu for table items."""
        if self.user_table.itemAt(position) is None:
            return

        selected_user = self.get_selected_user()
        if not selected_user:
            return

        # Create context menu
        menu = QMenu(self)

        # Edit action
        edit_action = menu.addAction("Edit User")
        edit_action.triggered.connect(self.edit_selected_user)

        # Reset password action
        reset_action = menu.addAction("Reset Password")
        reset_action.triggered.connect(self.reset_user_password)

        menu.addSeparator()

        # Activate/Deactivate action
        if selected_user['user_id'] != self.current_user.user_id:
            if selected_user['is_active']:
                deactivate_action = menu.addAction("Deactivate User")
                deactivate_action.triggered.connect(self.deactivate_selected_user)
            else:
                activate_action = menu.addAction("Activate User")
                activate_action.triggered.connect(self.deactivate_selected_user)

        # Show menu
        menu.exec_(self.user_table.mapToGlobal(position))
