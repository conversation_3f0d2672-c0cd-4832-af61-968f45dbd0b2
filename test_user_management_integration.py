#!/usr/bin/env python3
"""
PROJECT-ALPHA User Management Integration Tests
Tests the user management functionality integration with the main application.
"""

import sys
import os
import logging
from unittest.mock import MagicMock

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_authentication_and_session():
    """Test authentication and session creation for admin user."""
    print("🔍 Testing authentication and session management...")
    
    try:
        import database
        database.init_db()
        
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Try to authenticate with default admin user
        admin_user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not admin_user:
            print("❌ Default admin user authentication failed")
            return False, None, None
        
        print(f"✅ Admin user authenticated: {admin_user.username} (Role: {admin_user.role_name})")
        
        # Create session
        session_manager = SessionManager()
        session_id = session_manager.create_session(admin_user)
        
        if not session_id:
            print("❌ Session creation failed")
            return False, None, None
        
        print(f"✅ Session created successfully: {session_id}")
        
        return True, admin_user, session_manager
        
    except Exception as e:
        print(f"❌ Authentication test error: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_main_window_user_menu():
    """Test main window user menu integration."""
    print("\n🔍 Testing main window user menu integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Get authenticated admin user and session
        success, admin_user, session_manager = test_authentication_and_session()
        if not success:
            print("❌ Cannot test main window without authentication")
            return False
        
        # Import and create MainWindow
        from main import MainWindow
        main_window = MainWindow(session_manager=session_manager)
        
        # Test that main window has session manager
        if not hasattr(main_window, 'session_manager'):
            print("❌ MainWindow missing session_manager attribute")
            return False
        
        print("✅ MainWindow created with session manager")
        
        # Test that main window has show_user_management method
        if not hasattr(main_window, 'show_user_management'):
            print("❌ MainWindow missing show_user_management method")
            return False
        
        print("✅ MainWindow has show_user_management method")
        
        # Test user management method (should not crash)
        try:
            # This will try to show the dialog, but we'll catch any errors
            main_window.show_user_management()
            print("✅ User management method executed successfully")
        except Exception as e:
            print(f"⚠️ User management method error (expected in test): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Main window test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_management_dialog_direct():
    """Test UserManagementDialog directly."""
    print("\n🔍 Testing UserManagementDialog directly...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from auth.user_management_dialog import UserManagementDialog
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Get authenticated admin user
        success, admin_user, session_manager = test_authentication_and_session()
        if not success:
            print("❌ Cannot test dialog without authentication")
            return False
        
        # Create UserManagementDialog
        dialog = UserManagementDialog(parent=None, current_user=admin_user)
        
        if not dialog:
            print("❌ Failed to create UserManagementDialog")
            return False
        
        print("✅ UserManagementDialog created successfully")
        
        # Test dialog has required components
        if not hasattr(dialog, 'user_table'):
            print("❌ Dialog missing user_table")
            return False
        
        print("✅ Dialog has user table")
        
        if not hasattr(dialog, 'create_user_btn'):
            print("❌ Dialog missing create_user_btn")
            return False
        
        print("✅ Dialog has create user button")
        
        # Test that dialog loads users
        row_count = dialog.user_table.rowCount()
        print(f"✅ Dialog loaded {row_count} users in table")
        
        return True
        
    except Exception as e:
        print(f"❌ UserManagementDialog test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signup_dialog_admin_mode():
    """Test SignUpDialog in admin mode."""
    print("\n🔍 Testing SignUpDialog in admin mode...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from auth.signup_dialog import SignUpDialog
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Get authenticated admin user
        success, admin_user, session_manager = test_authentication_and_session()
        if not success:
            print("❌ Cannot test dialog without authentication")
            return False
        
        # Create SignUpDialog in admin mode
        dialog = SignUpDialog(parent=None, current_user=admin_user, is_admin_mode=True)
        
        if not dialog:
            print("❌ Failed to create SignUpDialog in admin mode")
            return False
        
        print("✅ SignUpDialog created successfully in admin mode")
        
        # Test dialog has required components
        if not hasattr(dialog, 'username_input'):
            print("❌ Dialog missing username_input")
            return False
        
        print("✅ Dialog has username input")
        
        if not hasattr(dialog, 'role_combo'):
            print("❌ Dialog missing role_combo")
            return False
        
        print("✅ Dialog has role selection")
        
        # Test role options available to admin
        role_count = dialog.role_combo.count()
        print(f"✅ Admin has access to {role_count} roles")
        
        if role_count < 2:  # Should have at least Read-Only and Administrator
            print("❌ Admin should have access to multiple roles")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SignUpDialog admin mode test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_creation_workflow():
    """Test the complete user creation workflow."""
    print("\n🔍 Testing user creation workflow...")
    
    try:
        from auth.authentication_service import AuthenticationService
        import time
        
        # Generate unique username
        unique_suffix = str(int(time.time() * 1000))[-6:]
        test_username = f"mgmttest{unique_suffix}"
        
        # Test user creation by admin
        success, message, user_id = AuthenticationService.register_user(
            username=test_username,
            password="MgmtTest123!",
            full_name="Management Test User",
            email="<EMAIL>",
            role_name="Read-Only"
        )
        
        if not success:
            print(f"❌ User creation failed: {message}")
            return False
        
        print(f"✅ User created successfully - ID: {user_id}")
        
        # Test that new user can authenticate
        new_user = AuthenticationService.authenticate_user(test_username, "MgmtTest123!")
        if not new_user:
            print("❌ Newly created user cannot authenticate")
            return False
        
        print(f"✅ Newly created user can authenticate: {new_user.username}")
        
        # Test user count increased
        total_users = AuthenticationService.count_total_users()
        print(f"✅ Total users now: {total_users}")
        
        return True
        
    except Exception as e:
        print(f"❌ User creation workflow test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_user_management_tests():
    """Run all user management integration tests."""
    print("🚀 Starting User Management Integration Tests")
    print("=" * 60)
    
    tests = [
        test_authentication_and_session,
        test_main_window_user_menu,
        test_user_management_dialog_direct,
        test_signup_dialog_admin_mode,
        test_user_creation_workflow
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            # Skip the authentication test in the loop since it's called by others
            if test == test_authentication_and_session:
                continue
                
            if test():
                passed += 1
                print("✅ PASSED")
            else:
                failed += 1
                print("❌ FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print("-" * 40)
    
    print(f"\n📊 User Management Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All user management tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = run_user_management_tests()
    sys.exit(0 if success else 1)
