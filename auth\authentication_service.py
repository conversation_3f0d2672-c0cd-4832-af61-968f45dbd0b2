"""
Authentication Service for PROJECT-ALPHA
Provides secure user authentication, password management, and session creation.
"""

import hashlib
import secrets
import base64
import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple, List
from dataclasses import dataclass

from database import get_db_connection, execute_query

logger = logging.getLogger(__name__)


@dataclass
class User:
    """User data class for authentication system."""
    user_id: int
    username: str
    full_name: str
    email: Optional[str]
    role_id: int
    role_name: str
    permissions: Dict[str, Any]
    is_active: bool
    last_login: Optional[str]
    failed_login_attempts: int
    account_locked_until: Optional[str]
    password_changed_at: str
    created_at: str


class AuthenticationService:
    """Core authentication service for PROJECT-ALPHA."""
    
    # Security configuration
    HASH_ITERATIONS = 100000  # PBKDF2 iterations
    SALT_LENGTH = 32  # Salt length in bytes
    MAX_LOGIN_ATTEMPTS = 5  # Maximum failed login attempts
    LOCKOUT_DURATION_MINUTES = 30  # Account lockout duration
    
    @staticmethod
    def hash_password(password: str, salt: str = None) -> <PERSON><PERSON>[str, str]:
        """
        Hash a password using PBKDF2 with SHA-256.
        
        Args:
            password: Plain text password to hash
            salt: Optional salt (base64 encoded). If None, generates new salt.
            
        Returns:
            Tuple of (password_hash_b64, salt_b64)
        """
        try:
            # Generate salt if not provided
            if salt is None:
                salt_bytes = secrets.token_bytes(AuthenticationService.SALT_LENGTH)
                salt = base64.b64encode(salt_bytes).decode('utf-8')
            else:
                salt_bytes = base64.b64decode(salt)
            
            # Hash password with PBKDF2
            password_hash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt_bytes,
                AuthenticationService.HASH_ITERATIONS
            )
            
            password_hash_b64 = base64.b64encode(password_hash).decode('utf-8')
            
            logger.debug(f"Password hashed successfully with {AuthenticationService.HASH_ITERATIONS} iterations")
            return password_hash_b64, salt
            
        except Exception as e:
            logger.error(f"Error hashing password: {e}")
            raise ValueError("Failed to hash password")
    
    @staticmethod
    def verify_password(password: str, stored_hash: str, salt: str) -> bool:
        """
        Verify a password against stored hash and salt.
        
        Args:
            password: Plain text password to verify
            stored_hash: Base64 encoded stored password hash
            salt: Base64 encoded salt
            
        Returns:
            True if password matches, False otherwise
        """
        try:
            # Hash the provided password with the stored salt
            computed_hash, _ = AuthenticationService.hash_password(password, salt)
            
            # Compare hashes using constant-time comparison
            return secrets.compare_digest(computed_hash, stored_hash)
            
        except Exception as e:
            logger.error(f"Error verifying password: {e}")
            return False

    @staticmethod
    def hash_pin(pin: str, salt: str = None) -> Tuple[str, str]:
        """
        Hash a PIN using PBKDF2 with SHA-256.

        Args:
            pin: Plain text PIN (4-6 digits)
            salt: Optional salt (base64 encoded), generates new if None

        Returns:
            Tuple of (base64_encoded_hash, base64_encoded_salt)
        """
        try:
            # Generate salt if not provided
            if salt is None:
                salt_bytes = secrets.token_bytes(32)
                salt = base64.b64encode(salt_bytes).decode('utf-8')
            else:
                salt_bytes = base64.b64decode(salt.encode('utf-8'))

            # Hash PIN with PBKDF2
            pin_hash = hashlib.pbkdf2_hmac('sha256', pin.encode('utf-8'), salt_bytes, 100000)
            pin_hash_b64 = base64.b64encode(pin_hash).decode('utf-8')

            return pin_hash_b64, salt

        except Exception as e:
            logger.error(f"Error hashing PIN: {e}")
            raise ValueError("Failed to hash PIN")

    @staticmethod
    def verify_pin(pin: str, stored_hash: str, salt: str) -> bool:
        """
        Verify a PIN against stored hash and salt.

        Args:
            pin: Plain text PIN to verify (4-6 digits)
            stored_hash: Base64 encoded stored PIN hash
            salt: Base64 encoded salt

        Returns:
            True if PIN matches, False otherwise
        """
        try:
            # Validate PIN format (4-6 digits)
            if not pin.isdigit() or not (4 <= len(pin) <= 6):
                logger.warning("Invalid PIN format provided")
                return False

            # Hash the provided PIN with the stored salt
            computed_hash, _ = AuthenticationService.hash_pin(pin, salt)

            # Compare hashes using constant-time comparison
            return secrets.compare_digest(computed_hash, stored_hash)

        except Exception as e:
            logger.error(f"Error verifying PIN: {e}")
            return False

    @staticmethod
    def is_account_locked(user_data: Dict[str, Any]) -> bool:
        """
        Check if user account is currently locked.
        
        Args:
            user_data: User record from database
            
        Returns:
            True if account is locked, False otherwise
        """
        if not user_data.get('account_locked_until'):
            return False
            
        try:
            lockout_time = datetime.fromisoformat(user_data['account_locked_until'])
            return datetime.now() < lockout_time
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def lock_account(user_id: int) -> bool:
        """
        Lock user account for the configured lockout duration.
        
        Args:
            user_id: ID of user to lock
            
        Returns:
            True if account locked successfully, False otherwise
        """
        try:
            lockout_until = datetime.now() + timedelta(minutes=AuthenticationService.LOCKOUT_DURATION_MINUTES)
            lockout_until_str = lockout_until.isoformat()
            
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE users 
                    SET account_locked_until = ?, updated_at = datetime('now')
                    WHERE user_id = ?
                """, (lockout_until_str, user_id))
                conn.commit()
                
            logger.warning(f"Account locked for user ID {user_id} until {lockout_until_str}")
            return True
            
        except Exception as e:
            logger.error(f"Error locking account for user ID {user_id}: {e}")
            return False
    
    @staticmethod
    def unlock_account(user_id: int) -> bool:
        """
        Unlock user account and reset failed login attempts.
        
        Args:
            user_id: ID of user to unlock
            
        Returns:
            True if account unlocked successfully, False otherwise
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE users 
                    SET account_locked_until = NULL, 
                        failed_login_attempts = 0,
                        updated_at = datetime('now')
                    WHERE user_id = ?
                """, (user_id,))
                conn.commit()
                
            logger.info(f"Account unlocked for user ID {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error unlocking account for user ID {user_id}: {e}")
            return False
    
    @staticmethod
    def increment_failed_attempts(user_id: int) -> int:
        """
        Increment failed login attempts for user.
        
        Args:
            user_id: ID of user
            
        Returns:
            New failed attempts count
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE users 
                    SET failed_login_attempts = failed_login_attempts + 1,
                        updated_at = datetime('now')
                    WHERE user_id = ?
                """, (user_id,))
                
                cursor.execute("SELECT failed_login_attempts FROM users WHERE user_id = ?", (user_id,))
                result = cursor.fetchone()
                failed_attempts = result['failed_login_attempts'] if result else 0
                
                conn.commit()
                
                # Lock account if max attempts reached
                if failed_attempts >= AuthenticationService.MAX_LOGIN_ATTEMPTS:
                    AuthenticationService.lock_account(user_id)
                    
                return failed_attempts
                
        except Exception as e:
            logger.error(f"Error incrementing failed attempts for user ID {user_id}: {e}")
            return 0
    
    @staticmethod
    def reset_failed_attempts(user_id: int) -> bool:
        """
        Reset failed login attempts for user.
        
        Args:
            user_id: ID of user
            
        Returns:
            True if reset successfully, False otherwise
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE users 
                    SET failed_login_attempts = 0,
                        updated_at = datetime('now')
                    WHERE user_id = ?
                """, (user_id,))
                conn.commit()
                
            return True
            
        except Exception as e:
            logger.error(f"Error resetting failed attempts for user ID {user_id}: {e}")
            return False
    
    @staticmethod
    def get_user_by_username(username: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve user data by username.
        
        Args:
            username: Username to search for
            
        Returns:
            User data dictionary or None if not found
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT u.*, r.role_name, r.permissions
                    FROM users u
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE u.username = ? AND u.is_active = 1 AND r.is_active = 1
                """, (username,))
                
                return cursor.fetchone()
                
        except Exception as e:
            logger.error(f"Error retrieving user by username '{username}': {e}")
            return None

    @staticmethod
    def get_all_active_users() -> List[Dict[str, Any]]:
        """
        Retrieve all active users for profile selection.

        Returns:
            List of user data dictionaries
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT u.user_id, u.username, u.full_name, u.email,
                           u.role_id, r.role_name, r.permissions, u.last_login
                    FROM users u
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE u.is_active = 1 AND r.is_active = 1
                    ORDER BY u.username
                """)

                return cursor.fetchall()

        except Exception as e:
            logger.error(f"Error retrieving all active users: {e}")
            return []

    @staticmethod
    def get_all_roles() -> List[Dict[str, Any]]:
        """
        Retrieve all active roles for role selection.

        Returns:
            List of role data dictionaries
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT role_id, role_name, permissions
                    FROM roles
                    WHERE is_active = 1
                    ORDER BY role_name
                """)

                return cursor.fetchall()

        except Exception as e:
            logger.error(f"Error retrieving all roles: {e}")
            return []

    @staticmethod
    def authenticate_user(username: str, password: str) -> Optional[User]:
        """
        Authenticate user with username and password.
        
        Args:
            username: Username
            password: Plain text password
            
        Returns:
            User object if authentication successful, None otherwise
        """
        try:
            # Get user data
            user_data = AuthenticationService.get_user_by_username(username)
            if not user_data:
                logger.warning(f"Authentication failed: User '{username}' not found")
                return None
            
            # Check if account is locked
            if AuthenticationService.is_account_locked(user_data):
                logger.warning(f"Authentication failed: Account '{username}' is locked")
                return None
            
            # Verify password
            if not AuthenticationService.verify_password(
                password, 
                user_data['password_hash'], 
                user_data['salt']
            ):
                # Increment failed attempts
                failed_attempts = AuthenticationService.increment_failed_attempts(user_data['user_id'])
                logger.warning(f"Authentication failed: Invalid password for '{username}' (attempt {failed_attempts})")
                return None
            
            # Reset failed attempts on successful login
            AuthenticationService.reset_failed_attempts(user_data['user_id'])
            
            # Update last login time
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE users 
                    SET last_login = datetime('now'), updated_at = datetime('now')
                    WHERE user_id = ?
                """, (user_data['user_id'],))
                conn.commit()
            
            # Parse permissions
            try:
                permissions = json.loads(user_data['permissions'])
            except (json.JSONDecodeError, TypeError):
                permissions = {}
            
            # Create User object
            user = User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                full_name=user_data['full_name'],
                email=user_data['email'],
                role_id=user_data['role_id'],
                role_name=user_data['role_name'],
                permissions=permissions,
                is_active=bool(user_data['is_active']),
                last_login=user_data['last_login'],
                failed_login_attempts=user_data['failed_login_attempts'],
                account_locked_until=user_data['account_locked_until'],
                password_changed_at=user_data['password_changed_at'],
                created_at=user_data['created_at']
            )
            
            logger.info(f"User '{username}' authenticated successfully")
            return user
            
        except Exception as e:
            logger.error(f"Error during authentication for '{username}': {e}")
            return None

    @staticmethod
    def authenticate_user_pin(username: str, pin: str) -> Optional[User]:
        """
        Authenticate user with PIN instead of password.

        Args:
            username: Username to authenticate
            pin: PIN code (4-6 digits)

        Returns:
            User object if authentication successful, None otherwise
        """
        try:
            logger.info(f"PIN authentication attempt for user: {username}")

            # Get user data
            user_data = AuthenticationService.get_user_by_username(username)
            if not user_data:
                logger.warning(f"PIN authentication failed: User '{username}' not found")
                return None

            # Check if user is active
            if not user_data['is_active']:
                logger.warning(f"PIN authentication failed: User '{username}' is inactive")
                return None

            # Check if account is locked
            if AuthenticationService.is_account_locked(user_data):
                logger.warning(f"PIN authentication failed: Account '{username}' is locked")
                return None

            # Verify PIN (stored in password_hash field for PIN-based users)
            if not AuthenticationService.verify_pin(
                pin,
                user_data['password_hash'],
                user_data['salt']
            ):
                # Increment failed attempts
                failed_attempts = AuthenticationService.increment_failed_attempts(user_data['user_id'])
                logger.warning(f"PIN authentication failed: Invalid PIN for '{username}' (attempt {failed_attempts})")
                return None

            # Reset failed attempts on successful login
            AuthenticationService.reset_failed_attempts(user_data['user_id'])

            # Update last login time
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE users
                    SET last_login = datetime('now'), updated_at = datetime('now')
                    WHERE user_id = ?
                """, (user_data['user_id'],))
                conn.commit()

            # Parse permissions
            try:
                permissions = json.loads(user_data['permissions'])
            except (json.JSONDecodeError, TypeError):
                permissions = {}

            # Create User object
            user = User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                full_name=user_data['full_name'],
                email=user_data['email'],
                role_id=user_data['role_id'],
                role_name=user_data['role_name'],
                permissions=permissions,
                is_active=bool(user_data['is_active']),
                last_login=user_data['last_login'],
                failed_login_attempts=user_data['failed_login_attempts'],
                account_locked_until=user_data['account_locked_until'],
                password_changed_at=user_data['password_changed_at'],
                created_at=user_data['created_at']
            )

            logger.info(f"PIN authentication successful for user: {username}")
            return user

        except Exception as e:
            logger.error(f"Error during PIN authentication for '{username}': {e}")
            return None

    @staticmethod
    def change_password(user_id: int, old_password: str, new_password: str) -> bool:
        """
        Change user password after verifying old password.
        
        Args:
            user_id: ID of user
            old_password: Current password
            new_password: New password
            
        Returns:
            True if password changed successfully, False otherwise
        """
        try:
            # Get current user data
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT password_hash, salt FROM users WHERE user_id = ?
                """, (user_id,))
                user_data = cursor.fetchone()
                
                if not user_data:
                    logger.error(f"User ID {user_id} not found for password change")
                    return False
                
                # Verify old password
                if not AuthenticationService.verify_password(
                    old_password, 
                    user_data['password_hash'], 
                    user_data['salt']
                ):
                    logger.warning(f"Password change failed: Invalid old password for user ID {user_id}")
                    return False
                
                # Hash new password
                new_hash, new_salt = AuthenticationService.hash_password(new_password)
                
                # Update password
                cursor.execute("""
                    UPDATE users 
                    SET password_hash = ?, 
                        salt = ?, 
                        password_changed_at = datetime('now'),
                        updated_at = datetime('now')
                    WHERE user_id = ?
                """, (new_hash, new_salt, user_id))
                
                conn.commit()
                
            logger.info(f"Password changed successfully for user ID {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error changing password for user ID {user_id}: {e}")
            return False

    @staticmethod
    def validate_password_strength(password: str) -> Tuple[bool, str]:
        """
        Validate password strength according to security requirements.

        Args:
            password: Password to validate

        Returns:
            Tuple of (is_valid, error_message)
        """
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"

        if len(password) > 128:
            return False, "Password must be no more than 128 characters long"

        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

        if not has_upper:
            return False, "Password must contain at least one uppercase letter"

        if not has_lower:
            return False, "Password must contain at least one lowercase letter"

        if not has_digit:
            return False, "Password must contain at least one number"

        if not has_special:
            return False, "Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)"

        # Check for common weak passwords
        weak_passwords = ['password', '12345678', 'qwerty123', 'admin123', 'password123']
        if password.lower() in weak_passwords:
            return False, "Password is too common. Please choose a more secure password"

        return True, ""

    @staticmethod
    def validate_username(username: str) -> Tuple[bool, str]:
        """
        Validate username format and requirements.

        Args:
            username: Username to validate

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not username:
            return False, "Username is required"

        if len(username) < 3:
            return False, "Username must be at least 3 characters long"

        if len(username) > 50:
            return False, "Username must be no more than 50 characters long"

        # Check for valid characters (alphanumeric, underscore, hyphen)
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            return False, "Username can only contain letters, numbers, underscores, and hyphens"

        # Username cannot start with number
        if username[0].isdigit():
            return False, "Username cannot start with a number"

        return True, ""

    @staticmethod
    def validate_email(email: str) -> Tuple[bool, str]:
        """
        Validate email format (optional field).

        Args:
            email: Email to validate

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not email:
            return True, ""  # Email is optional

        import re

        # Check for consecutive dots (not allowed)
        if '..' in email:
            return False, "Please enter a valid email address"

        # Check basic email pattern
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False, "Please enter a valid email address"

        if len(email) > 255:
            return False, "Email address is too long"

        return True, ""

    @staticmethod
    def is_username_available(username: str) -> bool:
        """
        Check if username is available (not already taken).

        Args:
            username: Username to check

        Returns:
            True if username is available, False if taken
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) as count FROM users WHERE username = ?", (username,))
                result = cursor.fetchone()
                return result['count'] == 0

        except Exception as e:
            logger.error(f"Error checking username availability: {e}")
            return False

    @staticmethod
    def get_available_roles(current_user_role: str = None) -> list:
        """
        Get list of roles that can be assigned based on current user's permissions.

        Args:
            current_user_role: Role of the user creating the account (None for open registration)

        Returns:
            List of role dictionaries with role_id, role_name, and role_description
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                if current_user_role == 'Administrator':
                    # Administrators can assign any role
                    cursor.execute("""
                        SELECT role_id, role_name, role_description
                        FROM roles
                        WHERE is_active = 1
                        ORDER BY role_name
                    """)
                elif current_user_role is None:
                    # Open registration - check if this is first user
                    total_users = AuthenticationService.count_total_users()

                    if total_users == 0:
                        # First user - allow Administrator role
                        cursor.execute("""
                            SELECT role_id, role_name, role_description
                            FROM roles
                            WHERE is_active = 1 AND role_name IN ('Administrator', 'Read-Only')
                            ORDER BY CASE WHEN role_name = 'Administrator' THEN 1 ELSE 2 END
                        """)
                    else:
                        # Subsequent open registration - only Read-Only
                        cursor.execute("""
                            SELECT role_id, role_name, role_description
                            FROM roles
                            WHERE is_active = 1 AND role_name = 'Read-Only'
                            ORDER BY role_name
                        """)
                else:
                    # Non-administrators can only assign Read-Only role
                    cursor.execute("""
                        SELECT role_id, role_name, role_description
                        FROM roles
                        WHERE is_active = 1 AND role_name = 'Read-Only'
                        ORDER BY role_name
                    """)

                return cursor.fetchall()

        except Exception as e:
            logger.error(f"Error getting available roles: {e}")
            return []

    @staticmethod
    def count_total_users() -> int:
        """
        Count total number of active users in the system.

        Returns:
            Number of active users
        """
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) as count FROM users WHERE is_active = 1")
                result = cursor.fetchone()
                return result['count']

        except Exception as e:
            logger.error(f"Error counting users: {e}")
            return 0

    @staticmethod
    def register_user(username: str, password: str, full_name: str, email: str = None,
                     role_name: str = 'Read-Only', created_by_user_id: int = None) -> Tuple[bool, str, int]:
        """
        Register a new user account with validation and security checks.

        Args:
            username: Unique username for the account
            password: Plain text password (will be hashed)
            full_name: User's full name
            email: Optional email address
            role_name: Role to assign to the user (default: Read-Only)
            created_by_user_id: ID of the user creating this account (None for self-registration)

        Returns:
            Tuple of (success, message, user_id)
        """
        try:
            # Validate input data
            username_valid, username_error = AuthenticationService.validate_username(username)
            if not username_valid:
                return False, username_error, 0

            password_valid, password_error = AuthenticationService.validate_password_strength(password)
            if not password_valid:
                return False, password_error, 0

            email_valid, email_error = AuthenticationService.validate_email(email)
            if not email_valid:
                return False, email_error, 0

            if not full_name or not full_name.strip():
                return False, "Full name is required", 0

            if len(full_name.strip()) > 100:
                return False, "Full name is too long (maximum 100 characters)", 0

            # Check username availability
            if not AuthenticationService.is_username_available(username):
                return False, "Username is already taken. Please choose a different username.", 0

            # Get role information
            with get_db_connection() as conn:
                cursor = conn.cursor()

                # Validate role exists and is active
                cursor.execute("""
                    SELECT role_id, role_name FROM roles
                    WHERE role_name = ? AND is_active = 1
                """, (role_name,))
                role_data = cursor.fetchone()

                if not role_data:
                    return False, f"Invalid role: {role_name}", 0

                role_id = role_data['role_id']

                # Hash password
                password_hash, salt = AuthenticationService.hash_password(password)

                # Create user record
                cursor.execute("""
                    INSERT INTO users (
                        username, password_hash, salt, full_name, email, role_id,
                        is_active, failed_login_attempts, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, 1, 0, ?)
                """, (
                    username.strip(),
                    password_hash,
                    salt,
                    full_name.strip(),
                    email.strip() if email else None,
                    role_id,
                    created_by_user_id
                ))

                user_id = cursor.lastrowid
                conn.commit()

                logger.info(f"User '{username}' registered successfully with role '{role_name}' (ID: {user_id})")

                if created_by_user_id:
                    logger.info(f"User {user_id} created by user {created_by_user_id}")
                else:
                    logger.info(f"User {user_id} self-registered")

                return True, f"Account created successfully for {username}", user_id

        except Exception as e:
            logger.error(f"Error registering user '{username}': {e}")
            return False, f"Registration failed: {str(e)}", 0

    @staticmethod
    def register_user_pin(username: str, pin: str, role_name: str = 'Read-Only',
                         created_by_user_id: int = None) -> Tuple[bool, str, int]:
        """
        Register a new user account with PIN-based authentication.

        Args:
            username: Unique username for the account (also serves as display name)
            pin: PIN code (4-6 digits)
            role_name: Role to assign to the user (default: Read-Only)
            created_by_user_id: ID of the user creating this account (None for self-registration)

        Returns:
            Tuple of (success, message, user_id)
        """
        try:
            # Validate input data
            username_valid, username_error = AuthenticationService.validate_username(username)
            if not username_valid:
                return False, username_error, 0

            # Validate PIN format (4-6 digits)
            if not pin.isdigit() or not (4 <= len(pin) <= 6):
                return False, "PIN must be 4-6 digits", 0

            # Check if username already exists
            existing_user = AuthenticationService.get_user_by_username(username)
            if existing_user:
                return False, f"Username '{username}' is already taken", 0

            # Get role ID
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT role_id FROM roles WHERE role_name = ? AND is_active = 1", (role_name,))
                role_data = cursor.fetchone()

                if not role_data:
                    return False, f"Role '{role_name}' not found", 0

                role_id = role_data['role_id']

                # Hash the PIN
                pin_hash, salt = AuthenticationService.hash_pin(pin)

                # Insert new user with PIN
                cursor.execute("""
                    INSERT INTO users (username, password_hash, salt, full_name, email, role_id, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    username,
                    pin_hash,
                    salt,
                    username,  # Use username as full_name for simplified registration
                    None,      # No email required for PIN-based registration
                    role_id,
                    created_by_user_id
                ))

                user_id = cursor.lastrowid
                conn.commit()

                logger.info(f"PIN-based user '{username}' registered successfully with role '{role_name}' (ID: {user_id})")

                if created_by_user_id:
                    logger.info(f"PIN user {user_id} created by user {created_by_user_id}")
                else:
                    logger.info(f"PIN user {user_id} self-registered")

                return True, f"Profile created successfully for {username}", user_id

        except Exception as e:
            logger.error(f"Error registering PIN user '{username}': {e}")
            return False, f"Registration failed: {str(e)}", 0
