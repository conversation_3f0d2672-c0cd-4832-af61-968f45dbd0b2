#!/usr/bin/env python3
"""
Comprehensive test suite for authentication system integration with main application.
Tests the complete authentication flow including login, session management, and UI integration.
"""

import sys
import os
import logging
import unittest
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_authentication_imports():
    """Test that all authentication modules can be imported successfully."""
    print("🔍 Testing authentication imports...")
    
    try:
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        from auth.login_dialog import LoginDialog
        from ui.button_state_manager import ButtonStateManager
        print("✅ All authentication imports successful")
        return True
    except ImportError as e:
        print(f"❌ Authentication import failed: {e}")
        return False

def test_database_authentication_tables():
    """Test that authentication tables exist and have default data."""
    print("🔍 Testing authentication database tables...")
    
    try:
        import database
        database.init_db()
        
        from auth.authentication_service import AuthenticationService
        
        # Test default admin user exists
        admin_user = AuthenticationService.get_user_by_username('admin')
        if not admin_user:
            print("❌ Default admin user not found")
            return False
        
        print(f"✅ Default admin user found: {admin_user['full_name']}")
        
        # Test authentication
        user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not user:
            print("❌ Admin authentication failed")
            return False
        
        print(f"✅ Admin authentication successful: {user.username}")
        return True
        
    except Exception as e:
        print(f"❌ Database authentication test failed: {e}")
        return False

def test_session_management():
    """Test session creation and management."""
    print("🔍 Testing session management...")
    
    try:
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Authenticate user
        user = AuthenticationService.authenticate_user('admin', 'admin123')
        if not user:
            print("❌ User authentication failed")
            return False
        
        # Create session
        session_manager = SessionManager()
        session_id = session_manager.create_session(user)
        
        if not session_id:
            print("❌ Session creation failed")
            return False
        
        print(f"✅ Session created: {session_id}")
        
        # Test session validation
        if not session_manager.is_session_valid():
            print("❌ Session validation failed")
            return False
        
        print("✅ Session validation successful")
        
        # Test permissions
        if not session_manager.has_permission('user_management'):
            print("❌ Admin permission check failed")
            return False
        
        print("✅ Permission check successful")
        
        # End session
        session_manager.end_session()
        print("✅ Session ended successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Session management test failed: {e}")
        return False

def test_button_state_manager():
    """Test ButtonStateManager with authentication."""
    print("🔍 Testing ButtonStateManager integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QPushButton
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        from ui.button_state_manager import ButtonStateManager, ButtonType
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Authenticate and create session
        user = AuthenticationService.authenticate_user('admin', 'admin123')
        session_manager = SessionManager()
        session_id = session_manager.create_session(user)
        
        # Create ButtonStateManager
        button_manager = ButtonStateManager(session_manager=session_manager)
        
        # Create test button
        test_button = QPushButton("Test Button")
        
        # Register button with permission requirement
        button_manager.register_button(
            name="test_button",
            button=test_button,
            button_type=ButtonType.ACTION,
            required_permission="user_management"
        )
        
        # Test button state
        if not test_button.isEnabled():
            print("❌ Button should be enabled for admin user")
            return False
        
        print("✅ ButtonStateManager working correctly")
        
        # Clean up
        session_manager.end_session()
        return True
        
    except Exception as e:
        print(f"❌ ButtonStateManager test failed: {e}")
        return False

def test_main_window_integration():
    """Test MainWindow integration with authentication."""
    print("🔍 Testing MainWindow authentication integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Authenticate and create session
        user = AuthenticationService.authenticate_user('admin', 'admin123')
        session_manager = SessionManager()
        session_id = session_manager.create_session(user)
        
        # Import and create MainWindow with session manager
        from main import MainWindow
        main_window = MainWindow(session_manager=session_manager)
        
        # Test that session manager is stored
        if not hasattr(main_window, 'session_manager'):
            print("❌ MainWindow missing session_manager attribute")
            return False
        
        if main_window.session_manager != session_manager:
            print("❌ MainWindow session_manager not set correctly")
            return False
        
        # Test that ButtonStateManager is initialized
        if not hasattr(main_window, 'button_state_manager'):
            print("❌ MainWindow missing button_state_manager attribute")
            return False
        
        if main_window.button_state_manager is None:
            print("❌ MainWindow button_state_manager not initialized")
            return False
        
        print("✅ MainWindow authentication integration successful")
        
        # Clean up
        session_manager.end_session()
        main_window.close()
        return True
        
    except Exception as e:
        print(f"❌ MainWindow integration test failed: {e}")
        return False

def run_all_tests():
    """Run all authentication integration tests."""
    print("🚀 Starting Authentication Integration Tests")
    print("=" * 60)
    
    tests = [
        test_authentication_imports,
        test_database_authentication_tables,
        test_session_management,
        test_button_state_manager,
        test_main_window_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print("-" * 40)
    
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All authentication integration tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
