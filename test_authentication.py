#!/usr/bin/env python3
"""
Test script for PROJECT-ALPHA authentication system.
Tests database schema creation, user authentication, and session management.
"""

import sys
import os
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_database_initialization():
    """Test database initialization with authentication tables."""
    print("=" * 60)
    print("TESTING DATABASE INITIALIZATION")
    print("=" * 60)
    
    try:
        from database import init_db
        
        # Initialize database
        success, first_run = init_db()
        print(f"✓ Database initialization: success={success}, first_run={first_run}")
        
        if not success:
            print("✗ Database initialization failed!")
            return False
            
        # Check if authentication tables exist
        from database import get_db_connection
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Check for authentication tables
            tables_to_check = ['users', 'roles', 'user_sessions', 'audit_log']
            
            for table in tables_to_check:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                result = cursor.fetchone()
                
                if result:
                    print(f"✓ Table '{table}' exists")
                    
                    # Get table info
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()
                    print(f"  Columns: {[col['name'] for col in columns]}")
                else:
                    print(f"✗ Table '{table}' missing!")
                    return False
            
            # Check default data
            cursor.execute("SELECT COUNT(*) as count FROM roles")
            role_count = cursor.fetchone()['count']
            print(f"✓ Default roles created: {role_count} roles")
            
            cursor.execute("SELECT COUNT(*) as count FROM users")
            user_count = cursor.fetchone()['count']
            print(f"✓ Default users created: {user_count} users")
            
            if user_count > 0:
                cursor.execute("SELECT username, full_name, role_id FROM users")
                users = cursor.fetchall()
                for user in users:
                    print(f"  User: {user['username']} ({user['full_name']}) - Role ID: {user['role_id']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_authentication_service():
    """Test authentication service functionality."""
    print("\n" + "=" * 60)
    print("TESTING AUTHENTICATION SERVICE")
    print("=" * 60)
    
    try:
        from auth.authentication_service import AuthenticationService
        
        # Test password hashing
        print("Testing password hashing...")
        password = "test123"
        hash1, salt1 = AuthenticationService.hash_password(password)
        hash2, salt2 = AuthenticationService.hash_password(password)
        
        print(f"✓ Password hashing works")
        print(f"  Hash 1: {hash1[:20]}...")
        print(f"  Hash 2: {hash2[:20]}...")
        print(f"  Different salts: {salt1 != salt2}")
        
        # Test password verification
        print("\nTesting password verification...")
        is_valid = AuthenticationService.verify_password(password, hash1, salt1)
        is_invalid = AuthenticationService.verify_password("wrong", hash1, salt1)
        
        print(f"✓ Correct password verified: {is_valid}")
        print(f"✓ Wrong password rejected: {not is_invalid}")
        
        # Test user authentication with default admin
        print("\nTesting user authentication...")
        user = AuthenticationService.authenticate_user("admin", "admin123")
        
        if user:
            print(f"✓ Admin authentication successful")
            print(f"  Username: {user.username}")
            print(f"  Full Name: {user.full_name}")
            print(f"  Role: {user.role_name}")
            print(f"  Permissions: {user.permissions}")
        else:
            print("✗ Admin authentication failed!")
            return False
        
        # Test invalid authentication
        invalid_user = AuthenticationService.authenticate_user("admin", "wrongpassword")
        if invalid_user is None:
            print("✓ Invalid password correctly rejected")
        else:
            print("✗ Invalid password incorrectly accepted!")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Authentication service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_session_manager():
    """Test session manager functionality."""
    print("\n" + "=" * 60)
    print("TESTING SESSION MANAGER")
    print("=" * 60)
    
    try:
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        
        # Authenticate user first
        user = AuthenticationService.authenticate_user("admin", "admin123")
        if not user:
            print("✗ Cannot test session manager - authentication failed")
            return False
        
        # Create session manager
        session_manager = SessionManager()
        
        # Create session
        print("Testing session creation...")
        session_id = session_manager.create_session(user, "127.0.0.1", "Test-Agent")
        print(f"✓ Session created: {session_id[:16]}...")
        
        # Validate session
        print("\nTesting session validation...")
        is_valid = session_manager.validate_session(session_id)
        print(f"✓ Session validation: {is_valid}")
        
        # Test permissions
        print("\nTesting permission checks...")
        print(f"✓ Has 'read' permission: {session_manager.has_permission('read')}")
        print(f"✓ Has 'write' permission: {session_manager.has_permission('write')}")
        print(f"✓ Has 'admin' permission: {session_manager.has_permission('admin')}")
        print(f"✓ Is read-only: {session_manager.is_read_only()}")
        print(f"✓ Is admin: {session_manager.is_admin()}")
        
        # Get current user
        current_user = session_manager.get_current_user()
        if current_user:
            print(f"✓ Current user: {current_user.username} ({current_user.role_name})")
        else:
            print("✗ Failed to get current user")
            return False
        
        # End session
        print("\nTesting session termination...")
        ended = session_manager.end_session(session_id)
        print(f"✓ Session ended: {ended}")
        
        # Validate ended session
        is_valid_after_end = session_manager.validate_session(session_id)
        print(f"✓ Session invalid after end: {not is_valid_after_end}")
        
        return True
        
    except Exception as e:
        print(f"✗ Session manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_button_state_manager():
    """Test button state manager functionality."""
    print("\n" + "=" * 60)
    print("TESTING BUTTON STATE MANAGER")
    print("=" * 60)

    try:
        from auth.authentication_service import AuthenticationService
        from auth.session_manager import SessionManager
        from ui.button_state_manager import ButtonStateManager, ButtonType, ButtonStateContext
        from PyQt5.QtWidgets import QApplication, QPushButton

        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # Authenticate user first
        user = AuthenticationService.authenticate_user("admin", "admin123")
        if not user:
            print("✗ Cannot test button state manager - authentication failed")
            return False

        # Create session manager
        session_manager = SessionManager()
        session_id = session_manager.create_session(user)

        # Create button state manager
        button_manager = ButtonStateManager(session_manager=session_manager)

        # Create test buttons
        create_btn = QPushButton("Create")
        edit_btn = QPushButton("Edit")
        delete_btn = QPushButton("Delete")

        # Register buttons
        print("Testing button registration...")
        button_manager.register_button("create", create_btn, ButtonType.CREATE, required_permission="write")
        button_manager.register_button("edit", edit_btn, ButtonType.EDIT, required_permission="write")
        button_manager.register_button("delete", delete_btn, ButtonType.DELETE, required_permission="delete")

        print(f"✓ Registered buttons: {button_manager.get_registered_buttons()}")

        # Test context changes
        print("\nTesting context changes...")

        # Empty context
        button_manager.set_context(ButtonStateContext.EMPTY)
        print(f"✓ EMPTY context - Create: {create_btn.isEnabled()}, Edit: {edit_btn.isEnabled()}, Delete: {delete_btn.isEnabled()}")

        # Viewing context
        button_manager.set_context(ButtonStateContext.VIEWING)
        print(f"✓ VIEWING context - Create: {create_btn.isEnabled()}, Edit: {edit_btn.isEnabled()}, Delete: {delete_btn.isEnabled()}")

        # Editing context
        button_manager.set_context(ButtonStateContext.EDITING)
        print(f"✓ EDITING context - Create: {create_btn.isEnabled()}, Edit: {edit_btn.isEnabled()}, Delete: {delete_btn.isEnabled()}")

        # Test permission checking
        print("\nTesting permission checks...")
        print(f"✓ Has 'write' permission: {button_manager.has_permission('write')}")
        print(f"✓ Has 'delete' permission: {button_manager.has_permission('delete')}")
        print(f"✓ Is read-only: {button_manager.is_read_only()}")

        # End session
        session_manager.end_session(session_id)

        return True

    except Exception as e:
        print(f"✗ Button state manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all authentication tests."""
    print("PROJECT-ALPHA Authentication System Test")
    print("=" * 60)

    tests = [
        ("Database Initialization", test_database_initialization),
        ("Authentication Service", test_authentication_service),
        ("Session Manager", test_session_manager),
        ("Button State Manager", test_button_state_manager)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All authentication tests passed!")
        return True
    else:
        print("❌ Some authentication tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
