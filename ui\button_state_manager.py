"""
Button State Manager for PROJECT-ALPHA
Provides centralized button state management with role-based access control.
"""

import logging
from enum import Enum
from typing import Dict, Optional, Any, Set
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QPushButton

logger = logging.getLogger(__name__)


class ButtonType(Enum):
    """Button types for different operations."""
    CREATE = "create"
    EDIT = "edit"
    DELETE = "delete"
    SAVE = "save"
    CANCEL = "cancel"
    VIEW = "view"
    REFRESH = "refresh"
    EXPORT = "export"
    IMPORT = "import"
    SEARCH = "search"
    CLEAR = "clear"
    HISTORY = "history"
    PRINT = "print"
    COPY = "copy"
    PASTE = "paste"
    CUSTOM = "custom"


class ButtonStateContext(Enum):
    """Button state contexts for different UI modes."""
    EMPTY = "empty"                    # No records selected
    VIEWING = "viewing"                # Viewing a record
    EDITING = "editing"                # Editing a record
    CREATING = "creating"              # Creating a new record
    MULTI_SELECT = "multi_select"      # Multiple records selected
    READ_ONLY_MODE = "read_only_mode"  # Read-only user mode
    LOADING = "loading"                # Data loading state
    ERROR = "error"                    # Error state


class ButtonStateManager(QObject):
    """
    Manages button states with role-based access control.
    Provides centralized button state management that respects user permissions.
    """
    
    # Signal emitted when button states change
    button_states_changed = pyqtSignal(str)  # context name
    
    def __init__(self, parent=None, session_manager=None):
        """
        Initialize button state manager.
        
        Args:
            parent: Parent QObject
            session_manager: SessionManager instance for permission checking
        """
        super().__init__(parent)
        self.session_manager = session_manager
        self.buttons: Dict[str, Dict[str, Any]] = {}
        self.current_context = ButtonStateContext.EMPTY
        self.context_data = {}
        
        # Define default context rules
        self._setup_default_context_rules()
    
    def _setup_default_context_rules(self):
        """Setup default button state rules for each context."""
        self.context_rules = {
            ButtonStateContext.EMPTY: {
                ButtonType.CREATE: True,
                ButtonType.EDIT: False,
                ButtonType.DELETE: False,
                ButtonType.SAVE: False,
                ButtonType.CANCEL: False,
                ButtonType.VIEW: False,
                ButtonType.REFRESH: True,
                ButtonType.EXPORT: True,
                ButtonType.IMPORT: True,
                ButtonType.SEARCH: True,
                ButtonType.CLEAR: True,
                ButtonType.HISTORY: False,
                ButtonType.PRINT: False,
                ButtonType.COPY: False,
                ButtonType.PASTE: True,
                ButtonType.CUSTOM: True
            },
            ButtonStateContext.VIEWING: {
                ButtonType.CREATE: True,
                ButtonType.EDIT: True,
                ButtonType.DELETE: True,
                ButtonType.SAVE: False,
                ButtonType.CANCEL: False,
                ButtonType.VIEW: True,
                ButtonType.REFRESH: True,
                ButtonType.EXPORT: True,
                ButtonType.IMPORT: True,
                ButtonType.SEARCH: True,
                ButtonType.CLEAR: True,
                ButtonType.HISTORY: True,
                ButtonType.PRINT: True,
                ButtonType.COPY: True,
                ButtonType.PASTE: True,
                ButtonType.CUSTOM: True
            },
            ButtonStateContext.EDITING: {
                ButtonType.CREATE: False,
                ButtonType.EDIT: False,
                ButtonType.DELETE: False,
                ButtonType.SAVE: True,
                ButtonType.CANCEL: True,
                ButtonType.VIEW: False,
                ButtonType.REFRESH: False,
                ButtonType.EXPORT: False,
                ButtonType.IMPORT: False,
                ButtonType.SEARCH: False,
                ButtonType.CLEAR: False,
                ButtonType.HISTORY: False,
                ButtonType.PRINT: False,
                ButtonType.COPY: False,
                ButtonType.PASTE: False,
                ButtonType.CUSTOM: False
            },
            ButtonStateContext.CREATING: {
                ButtonType.CREATE: False,
                ButtonType.EDIT: False,
                ButtonType.DELETE: False,
                ButtonType.SAVE: True,
                ButtonType.CANCEL: True,
                ButtonType.VIEW: False,
                ButtonType.REFRESH: False,
                ButtonType.EXPORT: False,
                ButtonType.IMPORT: False,
                ButtonType.SEARCH: False,
                ButtonType.CLEAR: False,
                ButtonType.HISTORY: False,
                ButtonType.PRINT: False,
                ButtonType.COPY: False,
                ButtonType.PASTE: False,
                ButtonType.CUSTOM: False
            },
            ButtonStateContext.MULTI_SELECT: {
                ButtonType.CREATE: True,
                ButtonType.EDIT: False,
                ButtonType.DELETE: True,
                ButtonType.SAVE: False,
                ButtonType.CANCEL: False,
                ButtonType.VIEW: False,
                ButtonType.REFRESH: True,
                ButtonType.EXPORT: True,
                ButtonType.IMPORT: True,
                ButtonType.SEARCH: True,
                ButtonType.CLEAR: True,
                ButtonType.HISTORY: False,
                ButtonType.PRINT: True,
                ButtonType.COPY: True,
                ButtonType.PASTE: True,
                ButtonType.CUSTOM: True
            },
            ButtonStateContext.READ_ONLY_MODE: {
                ButtonType.CREATE: False,
                ButtonType.EDIT: False,
                ButtonType.DELETE: False,
                ButtonType.SAVE: False,
                ButtonType.CANCEL: False,
                ButtonType.VIEW: True,
                ButtonType.REFRESH: True,
                ButtonType.EXPORT: True,
                ButtonType.IMPORT: False,
                ButtonType.SEARCH: True,
                ButtonType.CLEAR: True,
                ButtonType.HISTORY: True,
                ButtonType.PRINT: True,
                ButtonType.COPY: True,
                ButtonType.PASTE: False,
                ButtonType.CUSTOM: False
            },
            ButtonStateContext.LOADING: {
                ButtonType.CREATE: False,
                ButtonType.EDIT: False,
                ButtonType.DELETE: False,
                ButtonType.SAVE: False,
                ButtonType.CANCEL: True,
                ButtonType.VIEW: False,
                ButtonType.REFRESH: False,
                ButtonType.EXPORT: False,
                ButtonType.IMPORT: False,
                ButtonType.SEARCH: False,
                ButtonType.CLEAR: False,
                ButtonType.HISTORY: False,
                ButtonType.PRINT: False,
                ButtonType.COPY: False,
                ButtonType.PASTE: False,
                ButtonType.CUSTOM: False
            },
            ButtonStateContext.ERROR: {
                ButtonType.CREATE: False,
                ButtonType.EDIT: False,
                ButtonType.DELETE: False,
                ButtonType.SAVE: False,
                ButtonType.CANCEL: False,
                ButtonType.VIEW: False,
                ButtonType.REFRESH: True,
                ButtonType.EXPORT: False,
                ButtonType.IMPORT: False,
                ButtonType.SEARCH: True,
                ButtonType.CLEAR: True,
                ButtonType.HISTORY: False,
                ButtonType.PRINT: False,
                ButtonType.COPY: False,
                ButtonType.PASTE: False,
                ButtonType.CUSTOM: False
            }
        }
    
    def register_button(self, name: str, button: QPushButton, button_type: ButtonType, 
                       style_type: str = "default", required_permission: str = None,
                       custom_rules: Dict[ButtonStateContext, bool] = None):
        """
        Register a button with the state manager.
        
        Args:
            name: Unique button name
            button: QPushButton instance
            button_type: ButtonType enum value
            style_type: Style type for the button
            required_permission: Permission required to enable this button
            custom_rules: Custom context rules for this button
        """
        if name in self.buttons:
            logger.warning(f"Button '{name}' already registered, overwriting")
        
        self.buttons[name] = {
            'button': button,
            'type': button_type,
            'style_type': style_type,
            'required_permission': required_permission,
            'custom_rules': custom_rules or {},
            'original_enabled': button.isEnabled()
        }
        
        logger.debug(f"Registered button '{name}' (type: {button_type.value}, permission: {required_permission})")
        
        # Apply current context to new button
        self._update_button_state(name)
    
    def unregister_button(self, name: str):
        """Unregister a button from the state manager."""
        if name in self.buttons:
            del self.buttons[name]
            logger.debug(f"Unregistered button '{name}'")
    
    def set_context(self, context: ButtonStateContext, **kwargs):
        """
        Set the current context and update all button states.
        
        Args:
            context: ButtonStateContext enum value
            **kwargs: Additional context data
        """
        # Check if user should be forced into read-only mode
        if self.session_manager and self.session_manager.is_read_only():
            if context in [ButtonStateContext.EDITING, ButtonStateContext.CREATING]:
                logger.info(f"Forcing read-only mode: {context.value} -> {ButtonStateContext.READ_ONLY_MODE.value}")
                context = ButtonStateContext.READ_ONLY_MODE
        
        self.current_context = context
        self.context_data = kwargs
        
        logger.debug(f"Setting context to '{context.value}' with data: {kwargs}")
        
        # Update all button states
        for button_name in self.buttons:
            self._update_button_state(button_name)
        
        # Emit signal
        self.button_states_changed.emit(context.value)
    
    def _update_button_state(self, button_name: str):
        """Update the state of a specific button based on current context and permissions."""
        if button_name not in self.buttons:
            return
        
        button_info = self.buttons[button_name]
        button = button_info['button']
        button_type = button_info['type']
        required_permission = button_info['required_permission']
        custom_rules = button_info['custom_rules']
        
        # Check permission first
        if required_permission and self.session_manager:
            if not self.session_manager.has_permission(required_permission):
                button.setEnabled(False)
                logger.debug(f"Button '{button_name}' disabled: missing permission '{required_permission}'")
                return
        
        # Check custom rules for this button
        if self.current_context in custom_rules:
            enabled = custom_rules[self.current_context]
        else:
            # Use default context rules
            enabled = self.context_rules.get(self.current_context, {}).get(button_type, False)
        
        button.setEnabled(enabled)
        logger.debug(f"Button '{button_name}' {'enabled' if enabled else 'disabled'} in context '{self.current_context.value}'")
    
    def has_permission(self, permission: str) -> bool:
        """Check if current user has specific permission."""
        if not self.session_manager:
            return True  # No session manager means no restrictions
        return self.session_manager.has_permission(permission)
    
    def is_read_only(self) -> bool:
        """Check if current user is in read-only mode."""
        if not self.session_manager:
            return False
        return self.session_manager.is_read_only()
    
    def get_current_context(self) -> ButtonStateContext:
        """Get current button state context."""
        return self.current_context
    
    def get_context_data(self) -> Dict[str, Any]:
        """Get current context data."""
        return self.context_data.copy()
    
    def get_button_info(self, button_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a registered button."""
        return self.buttons.get(button_name, {}).copy() if button_name in self.buttons else None
    
    def get_registered_buttons(self) -> Set[str]:
        """Get set of all registered button names."""
        return set(self.buttons.keys())
    
    def force_button_state(self, button_name: str, enabled: bool):
        """Force a specific button to be enabled or disabled, overriding context rules."""
        if button_name in self.buttons:
            self.buttons[button_name]['button'].setEnabled(enabled)
            logger.debug(f"Forced button '{button_name}' to {'enabled' if enabled else 'disabled'}")
    
    def refresh_all_buttons(self):
        """Refresh all button states based on current context."""
        for button_name in self.buttons:
            self._update_button_state(button_name)
        logger.debug("Refreshed all button states")
    
    def set_custom_rule(self, button_name: str, context: ButtonStateContext, enabled: bool):
        """Set a custom rule for a specific button in a specific context."""
        if button_name in self.buttons:
            self.buttons[button_name]['custom_rules'][context] = enabled
            if self.current_context == context:
                self._update_button_state(button_name)
            logger.debug(f"Set custom rule for '{button_name}' in '{context.value}': {enabled}")
    
    def clear_custom_rules(self, button_name: str):
        """Clear all custom rules for a specific button."""
        if button_name in self.buttons:
            self.buttons[button_name]['custom_rules'].clear()
            self._update_button_state(button_name)
            logger.debug(f"Cleared custom rules for '{button_name}'")
