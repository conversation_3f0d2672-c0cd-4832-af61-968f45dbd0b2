"""
PIN Entry Dialog for PROJECT-ALPHA
Provides secure PIN-based authentication for selected user profiles.
"""

import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QLineEdit, QFrame, QMessageBox,
                             QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor

from auth.authentication_service import AuthenticationService
from ui.window_utils import DPIScaler
from ui.common_styles import PRIMARY_BUTTON_STYLE, BUTTON_STYLE, DANGER_BUTTON_STYLE

logger = logging.getLogger(__name__)


class PINEntryDialog(QDialog):
    """PIN entry dialog for user authentication."""
    
    authentication_successful = pyqtSignal(object)  # Emits authenticated User object
    back_to_profiles = pyqtSignal()                 # Emits when back button is pressed
    
    def __init__(self, user_data, parent=None):
        """Initialize PIN entry dialog for specific user."""
        super().__init__(parent)
        self.user_data = user_data
        self.authenticated_user = None
        self.failed_attempts = 0
        self.max_attempts = 3
        self.pin_input = ""
        
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("PROJECT-ALPHA - Enter PIN")
        self.setModal(True)
        self.setFixedSize(DPIScaler.scale_size(450), DPIScaler.scale_size(550))
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(DPIScaler.scale_size(30), DPIScaler.scale_size(30),
                                      DPIScaler.scale_size(30), DPIScaler.scale_size(30))
        main_layout.setSpacing(DPIScaler.scale_size(20))
        
        # Header section
        self.create_header(main_layout)
        
        # PIN input section
        self.create_pin_input(main_layout)
        
        # Numeric keypad section
        self.create_keypad(main_layout)
        
        # Footer section
        self.create_footer(main_layout)
        
    def create_header(self, layout):
        """Create dialog header with user info."""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(DPIScaler.scale_size(10))
        
        # User avatar/initials
        avatar_label = QLabel()
        avatar_label.setAlignment(Qt.AlignCenter)
        avatar_label.setFixedSize(DPIScaler.scale_size(80), DPIScaler.scale_size(80))
        avatar_label.setText(self.get_user_initials())
        avatar_label.setObjectName("avatarLabel")
        header_layout.addWidget(avatar_label)
        
        # Username
        username_label = QLabel(self.user_data['username'])
        username_label.setAlignment(Qt.AlignCenter)
        username_label.setObjectName("usernameLabel")
        header_layout.addWidget(username_label)
        
        # Role
        role_label = QLabel(self.user_data['role_name'])
        role_label.setAlignment(Qt.AlignCenter)
        role_label.setObjectName("roleLabel")
        header_layout.addWidget(role_label)
        
        # Instruction
        instruction_label = QLabel("Enter your PIN to continue")
        instruction_label.setAlignment(Qt.AlignCenter)
        instruction_label.setObjectName("instructionLabel")
        header_layout.addWidget(instruction_label)
        
        layout.addWidget(header_frame)
        
    def create_pin_input(self, layout):
        """Create PIN input display."""
        pin_frame = QFrame()
        pin_frame.setObjectName("pinFrame")
        pin_layout = QHBoxLayout(pin_frame)
        pin_layout.setSpacing(DPIScaler.scale_size(10))
        pin_layout.setContentsMargins(DPIScaler.scale_size(20), DPIScaler.scale_size(15),
                                     DPIScaler.scale_size(20), DPIScaler.scale_size(15))
        
        # PIN display dots
        self.pin_dots = []
        for i in range(6):  # Support up to 6-digit PINs
            dot = QLabel("○")
            dot.setAlignment(Qt.AlignCenter)
            dot.setObjectName("pinDot")
            dot.setFixedSize(DPIScaler.scale_size(30), DPIScaler.scale_size(30))
            self.pin_dots.append(dot)
            pin_layout.addWidget(dot)
        
        layout.addWidget(pin_frame)
        
        # Error message label
        self.error_label = QLabel("")
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setObjectName("errorLabel")
        self.error_label.hide()
        layout.addWidget(self.error_label)
        
    def create_keypad(self, layout):
        """Create numeric keypad."""
        keypad_frame = QFrame()
        keypad_frame.setObjectName("keypadFrame")
        keypad_layout = QGridLayout(keypad_frame)
        keypad_layout.setSpacing(DPIScaler.scale_size(10))
        keypad_layout.setContentsMargins(DPIScaler.scale_size(20), DPIScaler.scale_size(20),
                                        DPIScaler.scale_size(20), DPIScaler.scale_size(20))
        
        # Number buttons (1-9)
        for i in range(1, 10):
            row = (i - 1) // 3
            col = (i - 1) % 3
            
            button = QPushButton(str(i))
            button.setObjectName("numberButton")
            button.setFixedSize(DPIScaler.scale_size(60), DPIScaler.scale_size(60))
            button.clicked.connect(lambda checked, num=i: self.add_digit(str(num)))
            
            keypad_layout.addWidget(button, row, col)
        
        # Bottom row: Clear, 0, Backspace
        clear_button = QPushButton("Clear")
        clear_button.setObjectName("clearButton")
        clear_button.setFixedSize(DPIScaler.scale_size(60), DPIScaler.scale_size(60))
        clear_button.clicked.connect(self.clear_pin)
        keypad_layout.addWidget(clear_button, 3, 0)
        
        zero_button = QPushButton("0")
        zero_button.setObjectName("numberButton")
        zero_button.setFixedSize(DPIScaler.scale_size(60), DPIScaler.scale_size(60))
        zero_button.clicked.connect(lambda: self.add_digit("0"))
        keypad_layout.addWidget(zero_button, 3, 1)
        
        backspace_button = QPushButton("⌫")
        backspace_button.setObjectName("backspaceButton")
        backspace_button.setFixedSize(DPIScaler.scale_size(60), DPIScaler.scale_size(60))
        backspace_button.clicked.connect(self.remove_digit)
        keypad_layout.addWidget(backspace_button, 3, 2)
        
        layout.addWidget(keypad_frame)
        
    def create_footer(self, layout):
        """Create dialog footer with action buttons."""
        footer_layout = QHBoxLayout()
        footer_layout.setSpacing(DPIScaler.scale_size(15))
        
        # Back button
        self.back_button = QPushButton("Back to Profiles")
        self.back_button.setObjectName("backButton")
        self.back_button.clicked.connect(self.back_to_profiles.emit)
        footer_layout.addWidget(self.back_button)
        
        # Add spacer
        footer_layout.addStretch()
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.authenticate_pin)
        self.login_button.setEnabled(False)  # Disabled until PIN is entered
        footer_layout.addWidget(self.login_button)
        
        layout.addLayout(footer_layout)
        
    def get_user_initials(self):
        """Get user initials for avatar display."""
        username = self.user_data['username']
        if len(username) >= 2:
            return username[:2].upper()
        return username[0].upper() if username else "?"
        
    def add_digit(self, digit):
        """Add digit to PIN input."""
        if len(self.pin_input) < 6:  # Maximum 6 digits
            self.pin_input += digit
            self.update_pin_display()
            self.hide_error()
            
            # Enable login button if minimum PIN length is met
            if len(self.pin_input) >= 4:
                self.login_button.setEnabled(True)
            
    def remove_digit(self):
        """Remove last digit from PIN input."""
        if self.pin_input:
            self.pin_input = self.pin_input[:-1]
            self.update_pin_display()
            
            # Disable login button if PIN is too short
            if len(self.pin_input) < 4:
                self.login_button.setEnabled(False)
                
    def clear_pin(self):
        """Clear all PIN input."""
        self.pin_input = ""
        self.update_pin_display()
        self.login_button.setEnabled(False)
        self.hide_error()
        
    def update_pin_display(self):
        """Update PIN dots display."""
        for i, dot in enumerate(self.pin_dots):
            if i < len(self.pin_input):
                dot.setText("●")  # Filled dot
                dot.setObjectName("pinDotFilled")
            else:
                dot.setText("○")  # Empty dot
                dot.setObjectName("pinDot")
            
            # Refresh style
            dot.style().unpolish(dot)
            dot.style().polish(dot)
            
    def authenticate_pin(self):
        """Authenticate user with entered PIN."""
        if len(self.pin_input) < 4:
            self.show_error("PIN must be at least 4 digits")
            return
            
        try:
            # Authenticate using PIN instead of password
            user = AuthenticationService.authenticate_user_pin(
                self.user_data['username'], 
                self.pin_input
            )
            
            if user:
                logger.info(f"PIN authentication successful for user: {user.username}")
                self.authenticated_user = user
                self.authentication_successful.emit(user)
                self.accept()
            else:
                self.failed_attempts += 1
                remaining = self.max_attempts - self.failed_attempts
                
                if remaining > 0:
                    self.show_error(f"Invalid PIN. {remaining} attempts remaining.")
                else:
                    self.show_error("Too many failed attempts. Returning to profile selection.")
                    QTimer.singleShot(2000, self.back_to_profiles.emit)
                
                self.clear_pin()
                
        except Exception as e:
            logger.error(f"PIN authentication error: {e}")
            self.show_error("Authentication error. Please try again.")
            
    def show_error(self, message):
        """Show error message."""
        self.error_label.setText(message)
        self.error_label.show()
        
    def hide_error(self):
        """Hide error message."""
        self.error_label.hide()
        
    def keyPressEvent(self, event):
        """Handle keyboard input for PIN entry."""
        key = event.key()
        
        # Handle number keys
        if Qt.Key_0 <= key <= Qt.Key_9:
            digit = str(key - Qt.Key_0)
            self.add_digit(digit)
        # Handle backspace
        elif key == Qt.Key_Backspace:
            self.remove_digit()
        # Handle enter/return
        elif key in (Qt.Key_Return, Qt.Key_Enter):
            if self.login_button.isEnabled():
                self.authenticate_pin()
        # Handle escape
        elif key == Qt.Key_Escape:
            self.back_to_profiles.emit()
        else:
            super().keyPressEvent(event)
            
    def apply_styles(self):
        """Apply styling to the dialog."""
        self.setStyleSheet(f"""
            QDialog {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }}
            
            QFrame#headerFrame {{
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
                padding: {DPIScaler.scale_size(15)}px;
            }}
            
            QLabel#avatarLabel {{
                background-color: #007bff;
                color: white;
                border-radius: {DPIScaler.scale_size(40)}px;
                font-size: {DPIScaler.scale_font_size(32)}px;
                font-weight: bold;
            }}
            
            QLabel#usernameLabel {{
                font-size: {DPIScaler.scale_font_size(18)}px;
                font-weight: bold;
                color: #212529;
            }}
            
            QLabel#roleLabel {{
                font-size: {DPIScaler.scale_font_size(12)}px;
                color: #6c757d;
                background-color: #e9ecef;
                padding: {DPIScaler.scale_size(4)}px {DPIScaler.scale_size(12)}px;
                border-radius: {DPIScaler.scale_size(12)}px;
            }}
            
            QLabel#instructionLabel {{
                font-size: {DPIScaler.scale_font_size(14)}px;
                color: #495057;
                margin-top: {DPIScaler.scale_size(10)}px;
            }}
            
            QFrame#pinFrame {{
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
            }}
            
            QLabel#pinDot {{
                font-size: {DPIScaler.scale_font_size(20)}px;
                color: #dee2e6;
            }}
            
            QLabel#pinDotFilled {{
                font-size: {DPIScaler.scale_font_size(20)}px;
                color: #007bff;
            }}
            
            QLabel#errorLabel {{
                color: #dc3545;
                font-size: {DPIScaler.scale_font_size(12)}px;
                font-weight: bold;
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: {DPIScaler.scale_size(4)}px;
                padding: {DPIScaler.scale_size(8)}px;
            }}
            
            QFrame#keypadFrame {{
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
            }}
            
            QPushButton#numberButton {{
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(30)}px;
                font-size: {DPIScaler.scale_font_size(18)}px;
                font-weight: bold;
                color: #495057;
            }}
            
            QPushButton#numberButton:hover {{
                background-color: #e9ecef;
                border-color: #007bff;
            }}
            
            QPushButton#numberButton:pressed {{
                background-color: #007bff;
                color: white;
            }}
            
            QPushButton#clearButton, QPushButton#backspaceButton {{
                background-color: #ffc107;
                border: 2px solid #ffb300;
                border-radius: {DPIScaler.scale_size(30)}px;
                font-size: {DPIScaler.scale_font_size(14)}px;
                font-weight: bold;
                color: #212529;
            }}
            
            QPushButton#clearButton:hover, QPushButton#backspaceButton:hover {{
                background-color: #ffb300;
            }}
            
            QPushButton#loginButton {{
                {PRIMARY_BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                min-width: {DPIScaler.scale_size(100)}px;
                font-size: {DPIScaler.scale_font_size(14)}px;
            }}
            
            QPushButton#backButton {{
                {BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                min-width: {DPIScaler.scale_size(140)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
            }}
        """)
