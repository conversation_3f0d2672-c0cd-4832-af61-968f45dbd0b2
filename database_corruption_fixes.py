#!/usr/bin/env python3
"""
Database corruption fixes for PROJECT-ALPHA Excel import system.
Implements critical fixes to achieve 10/10 database reliability score.
"""

import sqlite3
import threading
import logging
from contextlib import contextmanager
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtCore import QMutex, QMutexLocker
import time
import json

logger = logging.getLogger(__name__)

class DatabaseIntegrityManager:
    """Manages database integrity and corruption prevention."""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.import_mutex = QMutex()
        self.health_check_interval = 300  # 5 minutes
        self.last_health_check = 0
        
    def perform_health_check(self) -> Dict[str, Any]:
        """Comprehensive database health check."""
        health_report = {
            'timestamp': time.time(),
            'overall_status': 'HEALTHY',
            'checks': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            from database import get_db_connection
            with get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 1. SQLite integrity check
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()
                health_report['checks']['integrity'] = {
                    'status': 'PASS' if integrity_result[0] == 'ok' else 'FAIL',
                    'result': integrity_result[0]
                }
                
                if integrity_result[0] != 'ok':
                    health_report['overall_status'] = 'CRITICAL'
                    health_report['issues'].append(f"Database integrity check failed: {integrity_result[0]}")
                
                # 2. Foreign key constraint check
                cursor.execute("PRAGMA foreign_key_check")
                fk_violations = cursor.fetchall()
                health_report['checks']['foreign_keys'] = {
                    'status': 'PASS' if not fk_violations else 'FAIL',
                    'violations_count': len(fk_violations),
                    'violations': fk_violations[:10]  # First 10 violations
                }
                
                if fk_violations:
                    health_report['overall_status'] = 'CRITICAL'
                    health_report['issues'].append(f"Found {len(fk_violations)} foreign key violations")
                
                # 3. Orphaned records check
                orphaned_checks = [
                    ("fluids", "equipment_id", "equipment", "equipment_id"),
                    ("maintenance", "equipment_id", "equipment", "equipment_id"),
                    ("overhauls", "equipment_id", "equipment", "equipment_id"),
                    ("tyre_maintenance", "equipment_id", "equipment", "equipment_id")
                ]
                
                for child_table, child_fk, parent_table, parent_pk in orphaned_checks:
                    try:
                        cursor.execute(f"""
                            SELECT COUNT(*) FROM {child_table} 
                            WHERE {child_fk} NOT IN (SELECT {parent_pk} FROM {parent_table})
                        """)
                        orphaned_count = cursor.fetchone()[0]
                        
                        health_report['checks'][f'orphaned_{child_table}'] = {
                            'status': 'PASS' if orphaned_count == 0 else 'WARN',
                            'orphaned_count': orphaned_count
                        }
                        
                        if orphaned_count > 0:
                            health_report['issues'].append(f"Found {orphaned_count} orphaned records in {child_table}")
                            
                    except sqlite3.Error as e:
                        logger.warning(f"Could not check orphaned records for {child_table}: {e}")
                
                # 4. Duplicate BA numbers check
                cursor.execute("""
                    SELECT ba_number, COUNT(*) as count 
                    FROM equipment 
                    WHERE ba_number IS NOT NULL AND ba_number != '' 
                    GROUP BY ba_number 
                    HAVING COUNT(*) > 1
                """)
                duplicate_ba_numbers = cursor.fetchall()
                
                health_report['checks']['duplicate_ba_numbers'] = {
                    'status': 'PASS' if not duplicate_ba_numbers else 'FAIL',
                    'duplicate_count': len(duplicate_ba_numbers),
                    'duplicates': duplicate_ba_numbers[:5]  # First 5 duplicates
                }
                
                if duplicate_ba_numbers:
                    health_report['overall_status'] = 'CRITICAL'
                    health_report['issues'].append(f"Found {len(duplicate_ba_numbers)} duplicate BA numbers")
                
                # 5. Null critical fields check
                cursor.execute("SELECT COUNT(*) FROM equipment WHERE make_and_type IS NULL OR make_and_type = ''")
                null_equipment_names = cursor.fetchone()[0]
                
                health_report['checks']['null_equipment_names'] = {
                    'status': 'PASS' if null_equipment_names == 0 else 'WARN',
                    'null_count': null_equipment_names
                }
                
                if null_equipment_names > 0:
                    health_report['issues'].append(f"Found {null_equipment_names} equipment records with null/empty names")
                
        except Exception as e:
            health_report['overall_status'] = 'ERROR'
            health_report['issues'].append(f"Health check failed: {str(e)}")
            logger.error(f"Database health check failed: {e}")
        
        # Generate recommendations
        if health_report['issues']:
            health_report['recommendations'].extend([
                "Run database repair operations",
                "Check recent import operations for errors",
                "Consider database backup and restore if corruption is severe"
            ])
        
        self.last_health_check = time.time()
        return health_report
    
    def repair_database_issues(self, health_report: Dict[str, Any]) -> Dict[str, Any]:
        """Attempt to repair identified database issues."""
        repair_report = {
            'timestamp': time.time(),
            'repairs_attempted': [],
            'repairs_successful': [],
            'repairs_failed': [],
            'manual_intervention_required': []
        }
        
        try:
            from database import get_db_connection
            with get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 1. Remove orphaned records
                for check_name, check_data in health_report['checks'].items():
                    if check_name.startswith('orphaned_') and check_data.get('orphaned_count', 0) > 0:
                        table_name = check_name.replace('orphaned_', '')
                        repair_report['repairs_attempted'].append(f"Remove orphaned records from {table_name}")
                        
                        try:
                            cursor.execute(f"""
                                DELETE FROM {table_name} 
                                WHERE equipment_id NOT IN (SELECT equipment_id FROM equipment)
                            """)
                            deleted_count = cursor.rowcount
                            conn.commit()
                            
                            repair_report['repairs_successful'].append(
                                f"Removed {deleted_count} orphaned records from {table_name}"
                            )
                            
                        except sqlite3.Error as e:
                            repair_report['repairs_failed'].append(
                                f"Failed to remove orphaned records from {table_name}: {str(e)}"
                            )
                
                # 2. Handle duplicate BA numbers (requires manual intervention)
                if health_report['checks'].get('duplicate_ba_numbers', {}).get('duplicate_count', 0) > 0:
                    repair_report['manual_intervention_required'].append(
                        "Duplicate BA numbers detected - manual review required to determine which records to keep"
                    )
                
                # 3. Handle null equipment names
                if health_report['checks'].get('null_equipment_names', {}).get('null_count', 0) > 0:
                    repair_report['repairs_attempted'].append("Fix null equipment names")
                    
                    try:
                        cursor.execute("""
                            UPDATE equipment 
                            SET make_and_type = 'UNKNOWN EQUIPMENT - NEEDS REVIEW' 
                            WHERE make_and_type IS NULL OR make_and_type = ''
                        """)
                        updated_count = cursor.rowcount
                        conn.commit()
                        
                        repair_report['repairs_successful'].append(
                            f"Fixed {updated_count} null equipment names"
                        )
                        
                    except sqlite3.Error as e:
                        repair_report['repairs_failed'].append(
                            f"Failed to fix null equipment names: {str(e)}"
                        )
                
        except Exception as e:
            repair_report['repairs_failed'].append(f"Database repair failed: {str(e)}")
            logger.error(f"Database repair failed: {e}")
        
        return repair_report


class TransactionManager:
    """Manages database transactions with proper error handling and rollback."""
    
    @staticmethod
    @contextmanager
    def atomic_transaction(connection):
        """Context manager for atomic transactions with automatic rollback on error."""
        cursor = connection.cursor()
        try:
            cursor.execute("BEGIN TRANSACTION")
            yield cursor
            connection.commit()
            logger.debug("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error(f"Transaction rolled back due to error: {e}")
            raise
    
    @staticmethod
    def execute_equipment_with_related_data(equipment_data: Dict[str, Any], 
                                          fluids_data: List[Dict[str, Any]] = None,
                                          maintenance_data: List[Dict[str, Any]] = None) -> Tuple[bool, Optional[int]]:
        """Execute equipment insertion with all related data in a single transaction."""
        try:
            from database import get_db_connection
            with get_db_connection() as conn:
                with TransactionManager.atomic_transaction(conn) as cursor:
                    # 1. Insert equipment
                    equipment_query = """
                        INSERT INTO equipment (
                            serial_number, make_and_type, units_held, vintage_years,
                            meterage_kms, meterage_description, km_hrs_run_previous_month,
                            km_hrs_run_current_month, is_active, remarks, section, unit,
                            location, last_service_date, next_service_date, status,
                            ba_number, hours_run_total, hours_run_previous_month,
                            hours_run_current_month, date_of_commission
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    equipment_params = (
                        equipment_data.get('serial_number'),
                        equipment_data.get('make_and_type'),
                        equipment_data.get('units_held', 1),
                        equipment_data.get('vintage_years', 0),
                        equipment_data.get('meterage_kms', 0),
                        equipment_data.get('meterage_description'),
                        equipment_data.get('km_hrs_run_previous_month', 0),
                        equipment_data.get('km_hrs_run_current_month', 0),
                        equipment_data.get('is_active', 1),
                        equipment_data.get('remarks'),
                        equipment_data.get('section'),
                        equipment_data.get('unit'),
                        equipment_data.get('location'),
                        equipment_data.get('last_service_date'),
                        equipment_data.get('next_service_date'),
                        equipment_data.get('status'),
                        equipment_data.get('ba_number'),
                        equipment_data.get('hours_run_total', 0),
                        equipment_data.get('hours_run_previous_month', 0),
                        equipment_data.get('hours_run_current_month', 0),
                        equipment_data.get('date_of_commission')
                    )
                    
                    cursor.execute(equipment_query, equipment_params)
                    equipment_id = cursor.lastrowid
                    
                    # 2. Insert fluids data
                    if fluids_data:
                        fluids_query = """
                            INSERT INTO fluids (
                                equipment_id, fluid_type, sub_type, accounting_unit,
                                capacity_ltrs_kg, addl_10_percent_top_up, grade,
                                periodicity_km, periodicity_hrs, periodicity_months,
                                last_serviced_date, last_serviced_meterage, date_of_change
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        for fluid in fluids_data:
                            fluid_params = (
                                equipment_id,
                                fluid.get('fluid_type'),
                                fluid.get('sub_type'),
                                fluid.get('accounting_unit', 'Ltr'),
                                fluid.get('capacity_ltrs_kg', 0),
                                fluid.get('addl_10_percent_top_up', 0),
                                fluid.get('grade'),
                                fluid.get('periodicity_km', 0),
                                fluid.get('periodicity_hrs', 0),
                                fluid.get('periodicity_months', 0),
                                fluid.get('last_serviced_date'),
                                fluid.get('last_serviced_meterage', 0),
                                fluid.get('date_of_change')
                            )
                            cursor.execute(fluids_query, fluid_params)
                    
                    # 3. Insert maintenance data
                    if maintenance_data:
                        maintenance_query = """
                            INSERT INTO maintenance (
                                equipment_id, maintenance_type, maintenance_category,
                                done_date, due_date, status, completion_notes
                            ) VALUES (?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        for maintenance in maintenance_data:
                            maintenance_params = (
                                equipment_id,
                                maintenance.get('maintenance_type'),
                                maintenance.get('maintenance_category'),
                                maintenance.get('done_date'),
                                maintenance.get('due_date'),
                                maintenance.get('status', 'scheduled'),
                                maintenance.get('completion_notes')
                            )
                            cursor.execute(maintenance_query, maintenance_params)
                    
                    return True, equipment_id
                    
        except Exception as e:
            logger.error(f"Failed to insert equipment with related data: {e}")
            return False, None


class ImportSynchronizationManager:
    """Manages synchronization of import operations to prevent corruption."""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance.import_mutex = QMutex()
                    cls._instance.active_imports = {}
        return cls._instance
    
    @contextmanager
    def synchronized_import(self, import_id: str):
        """Context manager for synchronized import operations."""
        with QMutexLocker(self.import_mutex):
            if import_id in self.active_imports:
                raise RuntimeError(f"Import {import_id} is already in progress")
            
            self.active_imports[import_id] = {
                'start_time': time.time(),
                'status': 'IN_PROGRESS'
            }
            
            try:
                logger.info(f"Starting synchronized import: {import_id}")
                yield
                self.active_imports[import_id]['status'] = 'COMPLETED'
                logger.info(f"Completed synchronized import: {import_id}")
            except Exception as e:
                self.active_imports[import_id]['status'] = 'FAILED'
                self.active_imports[import_id]['error'] = str(e)
                logger.error(f"Failed synchronized import {import_id}: {e}")
                raise
            finally:
                # Keep record for a short time for monitoring
                threading.Timer(60.0, lambda: self.active_imports.pop(import_id, None)).start()
    
    def get_active_imports(self) -> Dict[str, Any]:
        """Get information about currently active imports."""
        return dict(self.active_imports)


def create_database_backup(db_path: str, backup_path: str) -> bool:
    """Create a backup of the database before risky operations."""
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        logger.info(f"Database backup created: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to create database backup: {e}")
        return False


def validate_database_constraints(db_path: str) -> Dict[str, Any]:
    """Validate all database constraints and return detailed report."""
    validation_report = {
        'timestamp': time.time(),
        'valid': True,
        'constraint_violations': [],
        'data_integrity_issues': []
    }
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check foreign key constraints
        cursor.execute("PRAGMA foreign_key_check")
        fk_violations = cursor.fetchall()
        
        if fk_violations:
            validation_report['valid'] = False
            validation_report['constraint_violations'].extend([
                f"Foreign key violation: {violation}" for violation in fk_violations
            ])
        
        # Check unique constraints (BA numbers)
        cursor.execute("""
            SELECT ba_number, COUNT(*) 
            FROM equipment 
            WHERE ba_number IS NOT NULL AND ba_number != '' 
            GROUP BY ba_number 
            HAVING COUNT(*) > 1
        """)
        duplicate_ba_numbers = cursor.fetchall()
        
        if duplicate_ba_numbers:
            validation_report['valid'] = False
            validation_report['data_integrity_issues'].extend([
                f"Duplicate BA number: {ba_number} ({count} occurrences)" 
                for ba_number, count in duplicate_ba_numbers
            ])
        
        conn.close()
        
    except Exception as e:
        validation_report['valid'] = False
        validation_report['constraint_violations'].append(f"Validation failed: {str(e)}")
    
    return validation_report
