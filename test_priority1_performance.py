#!/usr/bin/env python3
"""
Comprehensive Performance Testing Suite for Priority 1 Optimizations
PROJECT-ALPHA Performance Validation

Tests:
1. Excel Import Performance (artificial delay removal)
2. UI Responsiveness (async data loading)
3. Database Query Optimization (N+1 pattern fixes)
4. Backward Compatibility (regression testing)
"""

import sys
import os
import time
import threading
import tempfile
import pandas as pd
from datetime import datetime
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtTest import QTest

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import project modules
from main import ImportWorker
from ui.repairs_widget import RepairsWidget
from ui.fluids_widget import FluidsWidget
from models import Equipment, Fluid, Overhaul
import database
import logger

class PerformanceTestResults:
    """Container for test results and metrics."""
    
    def __init__(self):
        self.results = {}
        self.start_time = datetime.now()
    
    def add_result(self, test_name, metric, value, expected=None, passed=None):
        """Add a test result with metrics."""
        if test_name not in self.results:
            self.results[test_name] = {}
        
        self.results[test_name][metric] = {
            'value': value,
            'expected': expected,
            'passed': passed,
            'timestamp': datetime.now()
        }
    
    def print_summary(self):
        """Print comprehensive test results summary."""
        print("\n" + "="*80)
        print("🚀 PRIORITY 1 PERFORMANCE OPTIMIZATION TEST RESULTS")
        print("="*80)
        print(f"Test Suite Started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Test Suite Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        total_tests = 0
        passed_tests = 0
        
        for test_name, metrics in self.results.items():
            print(f"📊 {test_name}")
            print("-" * 60)
            
            for metric_name, data in metrics.items():
                total_tests += 1
                value = data['value']
                expected = data['expected']
                passed = data['passed']
                
                if passed is True:
                    status = "✅ PASS"
                    passed_tests += 1
                elif passed is False:
                    status = "❌ FAIL"
                else:
                    status = "ℹ️  INFO"
                    passed_tests += 1  # Info counts as pass
                
                print(f"  {metric_name}: {value}", end="")
                if expected:
                    print(f" (Expected: {expected})", end="")
                print(f" {status}")
            
            print()
        
        # Overall summary
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"🎯 OVERALL RESULTS: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("🏆 EXCELLENT: Priority 1 optimizations successfully validated!")
        elif success_rate >= 75:
            print("✅ GOOD: Most optimizations working, minor issues detected")
        else:
            print("⚠️  WARNING: Significant issues detected, review required")
        
        print("="*80)

class ExcelImportPerformanceTest:
    """Test Excel import performance improvements."""
    
    def __init__(self, results):
        self.results = results
        self.test_data_file = None
    
    def create_test_excel_file(self, num_records=1000):
        """Create test Excel file with specified number of records."""
        print(f"📝 Creating test Excel file with {num_records} records...")
        
        # Create test data matching expected Excel import format
        test_data = []
        for i in range(num_records):
            test_data.append({
                'BA Number': f'TEST{i:06d}',
                'Make & Type': f'Test Equipment Type {i % 10}',
                'Serial Number': f'SN{i:06d}',
                'Vintage Years': 5 + (i % 10),
                'Meterage (km)': 10000 + (i * 100),
                'Hours Run': 500 + (i * 5)
            })
        
        df = pd.DataFrame(test_data)
        
        # Create temporary Excel file
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        df.to_excel(temp_file.name, index=False, sheet_name='Equipment')
        temp_file.close()
        
        self.test_data_file = temp_file.name
        print(f"✅ Test Excel file created: {self.test_data_file}")
        return self.test_data_file
    
    def test_import_performance(self, num_records=1000):
        """Test Excel import performance with timing."""
        print(f"\n🔬 Testing Excel Import Performance ({num_records} records)")
        print("-" * 50)
        
        # Create test file
        test_file = self.create_test_excel_file(num_records)
        
        try:
            # Create ImportWorker instance
            import_worker = ImportWorker(test_file)
            
            # Track timing and progress
            start_time = time.time()
            progress_updates = []
            
            def track_progress(percentage, message):
                progress_updates.append({
                    'time': time.time() - start_time,
                    'percentage': percentage,
                    'message': message
                })
                print(f"  Progress: {percentage}% - {message}")
            
            # Connect progress signal
            import_worker.progress_update.connect(track_progress)
            
            # Run import in current thread for testing
            print("⏱️  Starting import operation...")
            import_worker.run()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Analyze results
            print(f"⏱️  Import completed in {total_time:.2f} seconds")
            
            # Calculate performance metrics
            records_per_second = num_records / total_time if total_time > 0 else 0
            
            # Expected performance (after optimization)
            expected_time = 1.9  # Target time for 1000 records
            expected_rps = num_records / expected_time
            
            # Performance improvement calculation
            baseline_time = 3.2  # Original time before optimization
            improvement = ((baseline_time - total_time) / baseline_time * 100) if baseline_time > 0 else 0
            
            # Record results
            self.results.add_result(
                "Excel Import Performance",
                "Import Time (seconds)",
                f"{total_time:.2f}s",
                f"≤{expected_time:.1f}s",
                total_time <= expected_time * 1.1  # 10% tolerance
            )
            
            self.results.add_result(
                "Excel Import Performance",
                "Records per Second",
                f"{records_per_second:.1f}",
                f"≥{expected_rps:.1f}",
                records_per_second >= expected_rps * 0.9  # 10% tolerance
            )
            
            self.results.add_result(
                "Excel Import Performance",
                "Performance Improvement",
                f"{improvement:.1f}%",
                "≥50%",
                improvement >= 50
            )
            
            # Check for artificial delays (should be eliminated)
            delay_detected = any('delay' in update['message'].lower() for update in progress_updates)
            self.results.add_result(
                "Excel Import Performance",
                "Artificial Delays Eliminated",
                "Yes" if not delay_detected else "No",
                "Yes",
                not delay_detected
            )
            
            print(f"📈 Performance improvement: {improvement:.1f}%")
            print(f"📊 Processing rate: {records_per_second:.1f} records/second")
            
        except Exception as e:
            print(f"❌ Import test failed: {e}")
            self.results.add_result(
                "Excel Import Performance",
                "Test Execution",
                f"Failed: {e}",
                "Success",
                False
            )
        
        finally:
            # Cleanup test file
            if self.test_data_file and os.path.exists(self.test_data_file):
                os.unlink(self.test_data_file)

class UIResponsivenessTest:
    """Test UI responsiveness improvements."""
    
    def __init__(self, results, app):
        self.results = results
        self.app = app
        self.ui_freeze_detected = False
        self.response_times = []
    
    def test_repairs_widget_loading(self):
        """Test repairs widget async loading performance."""
        print(f"\n🔬 Testing UI Responsiveness - Repairs Widget")
        print("-" * 50)
        
        try:
            # Create repairs widget
            repairs_widget = RepairsWidget()
            
            # Monitor UI responsiveness during loading
            start_time = time.time()
            
            # Set up UI freeze detection
            freeze_timer = QTimer()
            freeze_timer.timeout.connect(self._check_ui_freeze)
            freeze_timer.start(100)  # Check every 100ms
            
            # Test current tab immediate loading
            print("⏱️  Testing current tab immediate loading...")
            tab_start = time.time()
            
            # Load data (should be immediate for current tab)
            repairs_widget.load_data()
            
            # Process events to allow async operations
            for _ in range(50):  # Process events for 5 seconds
                self.app.processEvents()
                QTest.qWait(100)
            
            tab_end = time.time()
            current_tab_time = tab_end - tab_start
            
            freeze_timer.stop()
            
            # Record results
            self.results.add_result(
                "UI Responsiveness",
                "Current Tab Load Time",
                f"{current_tab_time:.2f}s",
                "≤0.5s",
                current_tab_time <= 0.5
            )
            
            self.results.add_result(
                "UI Responsiveness",
                "UI Freeze Detected",
                "Yes" if self.ui_freeze_detected else "No",
                "No",
                not self.ui_freeze_detected
            )
            
            # Test async background loading
            has_async_loader = hasattr(repairs_widget, 'async_loader')
            self.results.add_result(
                "UI Responsiveness",
                "Async Loader Implemented",
                "Yes" if has_async_loader else "No",
                "Yes",
                has_async_loader
            )
            
            print(f"📊 Current tab load time: {current_tab_time:.2f}s")
            print(f"🔄 Async loading: {'Implemented' if has_async_loader else 'Not found'}")
            
        except Exception as e:
            print(f"❌ UI responsiveness test failed: {e}")
            self.results.add_result(
                "UI Responsiveness",
                "Test Execution",
                f"Failed: {e}",
                "Success",
                False
            )
    
    def _check_ui_freeze(self):
        """Check if UI is frozen by measuring response time."""
        start = time.time()
        self.app.processEvents()
        response_time = time.time() - start
        
        self.response_times.append(response_time)
        
        # Consider UI frozen if response time > 100ms
        if response_time > 0.1:
            self.ui_freeze_detected = True

class DatabaseQueryOptimizationTest:
    """Test database query optimization improvements."""
    
    def __init__(self, results):
        self.results = results
        self.query_count = 0
        self.original_execute_query = None
    
    def setup_query_monitoring(self):
        """Set up query monitoring to count database calls."""
        self.original_execute_query = database.execute_query
        
        def monitored_execute_query(*args, **kwargs):
            self.query_count += 1
            return self.original_execute_query(*args, **kwargs)
        
        database.execute_query = monitored_execute_query
    
    def teardown_query_monitoring(self):
        """Restore original query function."""
        if self.original_execute_query:
            database.execute_query = self.original_execute_query
    
    def test_fluids_widget_queries(self):
        """Test fluids widget query optimization."""
        print(f"\n🔬 Testing Database Query Optimization - Fluids Widget")
        print("-" * 50)
        
        try:
            # Set up monitoring
            self.setup_query_monitoring()
            
            # Create fluids widget
            fluids_widget = FluidsWidget()
            
            # Reset query count
            self.query_count = 0
            
            # Load data and count queries
            print("⏱️  Loading fluids data...")
            fluids_widget.load_data()
            
            queries_used = self.query_count
            
            # Expected: 1 query (Fluid.get_all() with JOIN)
            # Before optimization: 2 queries (Fluid.get_all() + Equipment.get_active())
            expected_queries = 1
            
            print(f"📊 Database queries executed: {queries_used}")
            
            # Record results
            self.results.add_result(
                "Database Query Optimization",
                "Fluids Widget Query Count",
                f"{queries_used}",
                f"≤{expected_queries}",
                queries_used <= expected_queries
            )
            
            # Test that optimized method exists
            has_optimized_method = hasattr(fluids_widget, 'load_equipment_data_optimized')
            self.results.add_result(
                "Database Query Optimization",
                "Optimized Method Implemented",
                "Yes" if has_optimized_method else "No",
                "Yes",
                has_optimized_method
            )
            
            # Calculate query reduction
            baseline_queries = 2  # Before optimization
            reduction = ((baseline_queries - queries_used) / baseline_queries * 100) if baseline_queries > 0 else 0
            
            self.results.add_result(
                "Database Query Optimization",
                "Query Reduction",
                f"{reduction:.1f}%",
                "≥50%",
                reduction >= 50
            )
            
        except Exception as e:
            print(f"❌ Fluids widget query test failed: {e}")
            self.results.add_result(
                "Database Query Optimization",
                "Fluids Test Execution",
                f"Failed: {e}",
                "Success",
                False
            )
        
        finally:
            self.teardown_query_monitoring()
    
    def test_repairs_widget_queries(self):
        """Test repairs widget query optimization."""
        print(f"\n🔬 Testing Database Query Optimization - Repairs Widget")
        print("-" * 50)
        
        try:
            # Set up monitoring
            self.setup_query_monitoring()
            
            # Create repairs widget
            repairs_widget = RepairsWidget()
            
            # Get one of the overhaul sub-widgets
            if repairs_widget.tab_widget.count() > 0:
                sub_widget = repairs_widget.tab_widget.widget(0)
                
                # Reset query count
                self.query_count = 0
                
                # Load data and count queries
                print("⏱️  Loading overhaul data...")
                sub_widget.load_data()
                
                queries_used = self.query_count
                
                # Expected: 1 query (Overhaul.get_all() with equipment data)
                # Before optimization: 2 queries (Overhaul.get_all() + Equipment.get_active())
                expected_queries = 1
                
                print(f"📊 Database queries executed: {queries_used}")
                
                # Record results
                self.results.add_result(
                    "Database Query Optimization",
                    "Repairs Widget Query Count",
                    f"{queries_used}",
                    f"≤{expected_queries}",
                    queries_used <= expected_queries
                )
                
                # Calculate query reduction
                baseline_queries = 2  # Before optimization
                reduction = ((baseline_queries - queries_used) / baseline_queries * 100) if baseline_queries > 0 else 0
                
                self.results.add_result(
                    "Database Query Optimization",
                    "Repairs Query Reduction",
                    f"{reduction:.1f}%",
                    "≥50%",
                    reduction >= 50
                )
            
        except Exception as e:
            print(f"❌ Repairs widget query test failed: {e}")
            self.results.add_result(
                "Database Query Optimization",
                "Repairs Test Execution",
                f"Failed: {e}",
                "Success",
                False
            )
        
        finally:
            self.teardown_query_monitoring()

def main():
    """Run comprehensive performance test suite."""
    print("🚀 Starting Priority 1 Performance Optimization Test Suite")
    print("=" * 80)
    
    # Initialize test results
    results = PerformanceTestResults()
    
    # Initialize Qt Application for UI tests
    app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
    
    try:
        # Test 1: Excel Import Performance
        print("\n1️⃣  EXCEL IMPORT PERFORMANCE TESTING")
        excel_test = ExcelImportPerformanceTest(results)
        excel_test.test_import_performance(1000)
        
        # Test 2: UI Responsiveness
        print("\n2️⃣  UI RESPONSIVENESS TESTING")
        ui_test = UIResponsivenessTest(results, app)
        ui_test.test_repairs_widget_loading()
        
        # Test 3: Database Query Optimization
        print("\n3️⃣  DATABASE QUERY OPTIMIZATION TESTING")
        db_test = DatabaseQueryOptimizationTest(results)
        db_test.test_fluids_widget_queries()
        db_test.test_repairs_widget_queries()
        
        # Test 4: Backward Compatibility (basic functionality test)
        print("\n4️⃣  BACKWARD COMPATIBILITY TESTING")
        print("-" * 50)
        
        try:
            # Test that all widgets can be created without errors
            fluids_widget = FluidsWidget()
            repairs_widget = RepairsWidget()
            
            results.add_result(
                "Backward Compatibility",
                "Widget Creation",
                "Success",
                "Success",
                True
            )
            
            # Test that legacy methods still work
            fluids_widget.load_equipment_data()  # Should redirect to optimized version
            
            results.add_result(
                "Backward Compatibility",
                "Legacy Method Support",
                "Success",
                "Success",
                True
            )
            
            print("✅ Widget creation and legacy methods working correctly")
            
        except Exception as e:
            print(f"❌ Backward compatibility test failed: {e}")
            results.add_result(
                "Backward Compatibility",
                "Test Execution",
                f"Failed: {e}",
                "Success",
                False
            )
        
    except Exception as e:
        print(f"❌ Test suite execution failed: {e}")
        results.add_result(
            "Test Suite",
            "Overall Execution",
            f"Failed: {e}",
            "Success",
            False
        )
    
    finally:
        # Print comprehensive results
        results.print_summary()
    
    return results

if __name__ == "__main__":
    main()
