"""
Excel Import Preview and Conflict Resolution System for PROJECT-ALPHA

This module provides:
1. Import Preview Dialog - Shows data preview and statistics before import
2. Conflict Detection Engine - Identifies BA number conflicts with existing data
3. Conflict Resolution Dialog - Side-by-side comparison with Accept/Skip options
4. Integration with existing import pipeline and progress monitoring

Designed to integrate seamlessly with the existing robust_excel_importer_working.py
and main.py import architecture while adding user control and data preview capabilities.
"""

import pandas as pd
import sqlite3
import logging
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QGroupBox, QTextEdit, QSplitter, QHeaderView,
    QMessageBox, QProgressBar, QFrame, QScrollArea, QWidget, QApplication
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor

logger = logging.getLogger('excel_import_preview')

class ExcelDataPreviewEngine:
    """Engine for analyzing Excel data and detecting conflicts before import."""
    
    def __init__(self):
        self.session_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def analyze_excel_file(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze Excel file and return preview data with conflict detection.
        
        Returns:
            dict: Contains preview_data, statistics, conflicts, and validation results
        """
        logger.info(f"Starting Excel file analysis: {file_path}")
        
        try:
            # Use the same logic as robust_excel_importer_working.py for consistency
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            analysis_result = {
                'file_path': file_path,
                'session_id': self.session_id,
                'sheets': sheet_names,
                'preview_data': [],
                'statistics': {
                    'total_sheets': len(sheet_names),
                    'total_equipment_rows': 0,
                    'total_fluid_rows': 0,
                    'total_maintenance_rows': 0,
                    'valid_ba_numbers': 0,
                    'invalid_rows': 0
                },
                'conflicts': [],
                'validation_errors': [],
                'success': True
            }
            
            # Analyze each sheet
            for sheet_name in sheet_names:
                sheet_analysis = self._analyze_sheet(excel_file, sheet_name)
                if sheet_analysis:
                    analysis_result['preview_data'].append(sheet_analysis)
                    
                    # Update statistics
                    stats = sheet_analysis.get('statistics', {})
                    analysis_result['statistics']['total_equipment_rows'] += stats.get('equipment_rows', 0)
                    analysis_result['statistics']['total_fluid_rows'] += stats.get('fluid_rows', 0)
                    analysis_result['statistics']['total_maintenance_rows'] += stats.get('maintenance_rows', 0)
                    analysis_result['statistics']['valid_ba_numbers'] += stats.get('valid_ba_numbers', 0)
                    analysis_result['statistics']['invalid_rows'] += stats.get('invalid_rows', 0)
                    
                    # Collect conflicts
                    if sheet_analysis.get('conflicts'):
                        analysis_result['conflicts'].extend(sheet_analysis['conflicts'])
            
            # Detect BA number conflicts with existing database
            analysis_result['conflicts'] = self._detect_ba_conflicts(analysis_result['preview_data'])
            
            logger.info(f"Excel analysis completed: {analysis_result['statistics']}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing Excel file: {e}")
            return {
                'file_path': file_path,
                'session_id': self.session_id,
                'success': False,
                'error': str(e),
                'preview_data': [],
                'statistics': {},
                'conflicts': [],
                'validation_errors': [str(e)]
            }
    
    def _analyze_sheet(self, excel_file: pd.ExcelFile, sheet_name: str) -> Optional[Dict[str, Any]]:
        """Analyze a single sheet and extract preview data."""
        try:
            # Use the same header detection logic as robust importer
            df = self._read_sheet_with_headers(excel_file, sheet_name)
            
            if df is None or len(df) == 0:
                logger.warning(f"Could not read or empty sheet: {sheet_name}")
                return None
            
            # Extract equipment data for preview (first 20 rows)
            preview_rows = []
            equipment_rows = 0
            valid_ba_numbers = 0
            invalid_rows = 0
            
            # Map columns using same logic as robust importer
            col_map = self._map_equipment_columns(df.columns)
            
            # Process rows for preview (limit to first 20 for performance)
            max_preview_rows = min(20, len(df))
            
            for index, row in df.head(max_preview_rows).iterrows():
                try:
                    equipment_data = self._extract_equipment_data(row, col_map, sheet_name)
                    
                    if equipment_data and equipment_data.get('make_and_type'):
                        if self._is_valid_equipment_record(equipment_data):
                            preview_rows.append({
                                'row_index': index,
                                'ba_number': equipment_data.get('ba_number', ''),
                                'make_and_type': equipment_data.get('make_and_type', ''),
                                'serial_number': equipment_data.get('serial_number', ''),
                                'meterage_kms': equipment_data.get('meterage_kms', 0),
                                'hours_run_total': equipment_data.get('hours_run_total', 0),
                                'is_valid': True,
                                'raw_data': equipment_data
                            })
                            equipment_rows += 1
                            if equipment_data.get('ba_number'):
                                valid_ba_numbers += 1
                        else:
                            invalid_rows += 1
                    else:
                        invalid_rows += 1
                        
                except Exception as e:
                    logger.warning(f"Error processing row {index} in {sheet_name}: {e}")
                    invalid_rows += 1
            
            return {
                'sheet_name': sheet_name,
                'total_rows': len(df),
                'preview_rows': preview_rows,
                'columns': df.columns.tolist(),
                'column_mapping': col_map,
                'statistics': {
                    'equipment_rows': equipment_rows,
                    'fluid_rows': 0,  # Will be calculated separately if needed
                    'maintenance_rows': 0,  # Will be calculated separately if needed
                    'valid_ba_numbers': valid_ba_numbers,
                    'invalid_rows': invalid_rows
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sheet {sheet_name}: {e}")
            return None
    
    def _read_sheet_with_headers(self, excel_file: pd.ExcelFile, sheet_name: str) -> Optional[pd.DataFrame]:
        """Read sheet with enhanced header detection - mirrors robust_excel_importer_working.py logic."""
        # Import the robust importer to use its exact header detection logic
        try:
            from robust_excel_importer_working import RobustExcelImporter
            importer = RobustExcelImporter()
            return importer._read_sheet_with_headers(excel_file, sheet_name)
        except Exception as e:
            logger.error(f"Error using robust importer header detection: {e}")
            # Fallback to simple read
            try:
                return pd.read_excel(excel_file, sheet_name=sheet_name, header=[0, 1, 2])
            except:
                try:
                    return pd.read_excel(excel_file, sheet_name=sheet_name, header=0)
                except Exception as fallback_error:
                    logger.error(f"All header detection methods failed for {sheet_name}: {fallback_error}")
                    return None
    
    def _map_equipment_columns(self, columns: list) -> Dict[str, str]:
        """Map DataFrame columns to equipment fields - mirrors robust_excel_importer_working.py logic."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            importer = RobustExcelImporter()
            return importer._map_equipment_columns(columns)
        except Exception as e:
            logger.error(f"Error using robust importer column mapping: {e}")
            # Fallback basic mapping
            return {}
    
    def _extract_equipment_data(self, row: pd.Series, col_map: Dict[str, str], sheet_name: str) -> Dict[str, Any]:
        """Extract equipment data from row - mirrors robust_excel_importer_working.py logic."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            importer = RobustExcelImporter()
            return importer._extract_equipment_data(row, col_map, sheet_name)
        except Exception as e:
            logger.error(f"Error using robust importer data extraction: {e}")
            return {}
    
    def _is_valid_equipment_record(self, equipment_data: Dict[str, Any]) -> bool:
        """Validate equipment record - mirrors robust_excel_importer_working.py logic."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            importer = RobustExcelImporter()
            return importer._is_valid_equipment_record(equipment_data)
        except Exception as e:
            logger.error(f"Error using robust importer validation: {e}")
            return False
    
    def _detect_ba_conflicts(self, preview_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect BA number conflicts with existing database records."""
        conflicts = []
        
        try:
            import config
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Get all existing BA numbers and their associated data
            cursor.execute('''
                SELECT equipment_id, ba_number, make_and_type, serial_number, 
                       meterage_kms, hours_run_total, date_of_commission
                FROM equipment 
                WHERE ba_number IS NOT NULL AND ba_number != ''
            ''')
            
            existing_equipment = {row[1]: {
                'equipment_id': row[0],
                'ba_number': row[1],
                'make_and_type': row[2],
                'serial_number': row[3],
                'meterage_kms': row[4],
                'hours_run_total': row[5],
                'date_of_commission': row[6]
            } for row in cursor.fetchall()}
            
            conn.close()
            
            # Check each preview row for conflicts
            for sheet_data in preview_data:
                for row_data in sheet_data.get('preview_rows', []):
                    ba_number = row_data.get('ba_number', '').strip()
                    
                    if ba_number and ba_number in existing_equipment:
                        existing_data = existing_equipment[ba_number]
                        new_data = row_data.get('raw_data', {})
                        
                        # Create conflict record
                        conflict = {
                            'ba_number': ba_number,
                            'sheet_name': sheet_data['sheet_name'],
                            'row_index': row_data['row_index'],
                            'existing_data': existing_data,
                            'new_data': new_data,
                            'differences': self._find_data_differences(existing_data, new_data)
                        }
                        
                        conflicts.append(conflict)
                        logger.info(f"Detected BA number conflict: {ba_number}")
            
            logger.info(f"Found {len(conflicts)} BA number conflicts")
            return conflicts
            
        except Exception as e:
            logger.error(f"Error detecting BA conflicts: {e}")
            return []
    
    def _find_data_differences(self, existing: Dict[str, Any], new: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find differences between existing and new equipment data."""
        differences = []
        
        # Compare key fields
        compare_fields = [
            ('make_and_type', 'Equipment Type'),
            ('serial_number', 'Serial Number'),
            ('meterage_kms', 'Meterage (KM)'),
            ('hours_run_total', 'Hours Run Total'),
            ('date_of_commission', 'Commission Date')
        ]
        
        for field, display_name in compare_fields:
            existing_val = existing.get(field, '')
            new_val = new.get(field, '')
            
            # Convert to strings for comparison
            existing_str = str(existing_val) if existing_val is not None else ''
            new_str = str(new_val) if new_val is not None else ''
            
            if existing_str != new_str:
                differences.append({
                    'field': field,
                    'display_name': display_name,
                    'existing_value': existing_str,
                    'new_value': new_str
                })
        
        return differences
