"""
Profile Selection Dialog for PROJECT-ALPHA
Provides kiosk-style user profile selection interface for streamlined authentication.
"""

import logging
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QFrame, QScrollArea, QWidget,
                             QSizePolicy, QSpacerItem, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QPainter, QFontMetrics

from auth.authentication_service import AuthenticationService
from ui.window_utils import DPIScaler
from ui.common_styles import PRIMARY_BUTTON_STYLE, BUTTON_STYLE, DANGER_BUTTON_STYLE

logger = logging.getLogger(__name__)


class ProfileCard(QFrame):
    """Individual profile card widget for user selection."""
    
    profile_selected = pyqtSignal(dict)  # Emits user data when selected
    
    def __init__(self, user_data, parent=None):
        """Initialize profile card with user data."""
        super().__init__(parent)
        self.user_data = user_data
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """Set up the profile card UI."""
        self.setFixedSize(DPIScaler.scale_size(200), DPIScaler.scale_size(160))
        self.setCursor(Qt.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(DPIScaler.scale_size(15), DPIScaler.scale_size(15), 
                                 DPIScaler.scale_size(15), DPIScaler.scale_size(15))
        layout.setSpacing(DPIScaler.scale_size(10))
        
        # Avatar/Icon section
        avatar_label = QLabel()
        avatar_label.setAlignment(Qt.AlignCenter)
        avatar_label.setFixedSize(DPIScaler.scale_size(60), DPIScaler.scale_size(60))
        
        # Try to load user avatar or use default
        avatar_pixmap = self.get_user_avatar()
        if avatar_pixmap:
            avatar_label.setPixmap(avatar_pixmap.scaled(
                DPIScaler.scale_size(60), DPIScaler.scale_size(60),
                Qt.KeepAspectRatio, Qt.SmoothTransformation
            ))
        else:
            # Create default avatar with user initials
            avatar_label.setText(self.get_user_initials())
            avatar_label.setObjectName("avatarLabel")
        
        layout.addWidget(avatar_label)
        
        # Username label
        username_label = QLabel(self.user_data['username'])
        username_label.setAlignment(Qt.AlignCenter)
        username_label.setWordWrap(True)
        username_label.setObjectName("usernameLabel")
        layout.addWidget(username_label)
        
        # Role label
        role_label = QLabel(self.user_data['role_name'])
        role_label.setAlignment(Qt.AlignCenter)
        role_label.setObjectName("roleLabel")
        layout.addWidget(role_label)
        
        # Add stretch to center content
        layout.addStretch()
        
    def get_user_avatar(self):
        """Get user avatar image if available."""
        try:
            # Check for user-specific avatar
            avatar_path = f"resources/avatars/{self.user_data['username']}.png"
            if os.path.exists(avatar_path):
                return QPixmap(avatar_path)
            
            # Check for role-based avatar
            role_avatar_path = f"resources/avatars/{self.user_data['role_name'].lower().replace('-', '_')}.png"
            if os.path.exists(role_avatar_path):
                return QPixmap(role_avatar_path)
                
        except Exception as e:
            logger.debug(f"Could not load avatar for {self.user_data['username']}: {e}")
        
        return None
        
    def get_user_initials(self):
        """Get user initials for default avatar."""
        username = self.user_data['username']
        if len(username) >= 2:
            return username[:2].upper()
        return username[0].upper() if username else "?"
        
    def apply_styles(self):
        """Apply styling to the profile card."""
        self.setStyleSheet(f"""
            ProfileCard {{
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(12)}px;
                margin: {DPIScaler.scale_size(5)}px;
            }}
            
            ProfileCard:hover {{
                border-color: #007bff;
                background-color: #f8f9fa;
                transform: translateY(-2px);
            }}
            
            QLabel#avatarLabel {{
                background-color: #007bff;
                color: white;
                border-radius: {DPIScaler.scale_size(30)}px;
                font-size: {DPIScaler.scale_font_size(24)}px;
                font-weight: bold;
            }}
            
            QLabel#usernameLabel {{
                font-size: {DPIScaler.scale_font_size(14)}px;
                font-weight: bold;
                color: #212529;
                margin: {DPIScaler.scale_size(5)}px 0;
            }}
            
            QLabel#roleLabel {{
                font-size: {DPIScaler.scale_font_size(11)}px;
                color: #6c757d;
                background-color: #e9ecef;
                padding: {DPIScaler.scale_size(4)}px {DPIScaler.scale_size(8)}px;
                border-radius: {DPIScaler.scale_size(12)}px;
            }}
        """)
        
    def mousePressEvent(self, event):
        """Handle mouse press event for profile selection."""
        if event.button() == Qt.LeftButton:
            logger.info(f"Profile selected: {self.user_data['username']}")
            self.profile_selected.emit(self.user_data)
        super().mousePressEvent(event)


class ProfileSelectionDialog(QDialog):
    """Main profile selection dialog for kiosk-style authentication."""
    
    profile_selected = pyqtSignal(dict)  # Emits selected user data
    add_user_requested = pyqtSignal()    # Emits when add user is requested
    
    def __init__(self, parent=None):
        """Initialize profile selection dialog."""
        super().__init__(parent)
        self.users = []
        self.setup_ui()
        self.load_users()
        self.apply_styles()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("PROJECT-ALPHA - Select User Profile")
        self.setModal(True)
        self.setFixedSize(DPIScaler.scale_size(800), DPIScaler.scale_size(600))
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(DPIScaler.scale_size(30), DPIScaler.scale_size(30),
                                      DPIScaler.scale_size(30), DPIScaler.scale_size(30))
        main_layout.setSpacing(DPIScaler.scale_size(20))
        
        # Header section
        self.create_header(main_layout)
        
        # Profile grid section
        self.create_profile_grid(main_layout)
        
        # Footer section
        self.create_footer(main_layout)
        
    def create_header(self, layout):
        """Create dialog header."""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(DPIScaler.scale_size(10))
        
        # Title
        title_label = QLabel("Select Your Profile")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Choose your user profile to continue")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
        
    def create_profile_grid(self, layout):
        """Create scrollable grid of user profiles."""
        # Scroll area for profiles
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setObjectName("profileScrollArea")
        
        # Container widget for profiles
        self.profile_container = QWidget()
        self.profile_layout = QGridLayout(self.profile_container)
        self.profile_layout.setSpacing(DPIScaler.scale_size(15))
        self.profile_layout.setContentsMargins(DPIScaler.scale_size(10), DPIScaler.scale_size(10),
                                              DPIScaler.scale_size(10), DPIScaler.scale_size(10))
        
        scroll_area.setWidget(self.profile_container)
        layout.addWidget(scroll_area)
        
    def create_footer(self, layout):
        """Create dialog footer with action buttons."""
        footer_layout = QHBoxLayout()
        footer_layout.setSpacing(DPIScaler.scale_size(15))
        
        # Add spacer
        footer_layout.addStretch()
        
        # Add User button
        self.add_user_button = QPushButton("Add New User")
        self.add_user_button.setObjectName("addUserButton")
        self.add_user_button.clicked.connect(self.add_user_requested.emit)
        footer_layout.addWidget(self.add_user_button)
        
        # Exit button
        self.exit_button = QPushButton("Exit")
        self.exit_button.setObjectName("exitButton")
        self.exit_button.clicked.connect(self.reject)
        footer_layout.addWidget(self.exit_button)
        
        layout.addLayout(footer_layout)
        
    def load_users(self):
        """Load user profiles from database."""
        try:
            self.users = AuthenticationService.get_all_active_users()
            logger.info(f"Loaded {len(self.users)} active user profiles")
            self.populate_profile_grid()
            
        except Exception as e:
            logger.error(f"Error loading user profiles: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to load user profiles: {str(e)}"
            )
            
    def populate_profile_grid(self):
        """Populate the grid with user profile cards."""
        # Clear existing profiles
        for i in reversed(range(self.profile_layout.count())):
            child = self.profile_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # Add profile cards
        columns = 3  # Number of columns in grid
        for index, user in enumerate(self.users):
            row = index // columns
            col = index % columns
            
            profile_card = ProfileCard(user)
            profile_card.profile_selected.connect(self.on_profile_selected)
            
            self.profile_layout.addWidget(profile_card, row, col)
        
        # Add stretch to fill remaining space
        self.profile_layout.setRowStretch(self.profile_layout.rowCount(), 1)
        
    def on_profile_selected(self, user_data):
        """Handle profile selection."""
        logger.info(f"User profile selected: {user_data['username']}")
        self.profile_selected.emit(user_data)
        
    def apply_styles(self):
        """Apply styling to the dialog."""
        self.setStyleSheet(f"""
            QDialog {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }}
            
            QFrame#headerFrame {{
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
                padding: {DPIScaler.scale_size(20)}px;
            }}
            
            QLabel#titleLabel {{
                font-size: {DPIScaler.scale_font_size(24)}px;
                font-weight: bold;
                color: #212529;
                margin-bottom: {DPIScaler.scale_size(5)}px;
            }}
            
            QLabel#subtitleLabel {{
                font-size: {DPIScaler.scale_font_size(14)}px;
                color: #6c757d;
            }}
            
            QScrollArea#profileScrollArea {{
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
                background-color: #ffffff;
            }}
            
            QPushButton#addUserButton {{
                {PRIMARY_BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                min-width: {DPIScaler.scale_size(120)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
            }}
            
            QPushButton#exitButton {{
                {DANGER_BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                min-width: {DPIScaler.scale_size(80)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
            }}
        """)
