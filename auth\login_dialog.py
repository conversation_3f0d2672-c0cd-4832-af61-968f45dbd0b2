"""
Login Dialog for PROJECT-ALPHA
Provides secure user authentication interface.
"""

import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QCheckBox, QFrame,
                             QMessageBox, QProgressBar, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon
from auth.authentication_service import AuthenticationService
from ui.window_utils import DPIScaler

logger = logging.getLogger(__name__)


class AuthenticationWorker(QThread):
    """Worker thread for authentication to prevent UI blocking."""
    
    authentication_complete = pyqtSignal(object)  # User object or None
    authentication_error = pyqtSignal(str)
    
    def __init__(self, username, password):
        super().__init__()
        self.username = username
        self.password = password
    
    def run(self):
        """Perform authentication in background thread."""
        try:
            user = AuthenticationService.authenticate_user(self.username, self.password)
            self.authentication_complete.emit(user)
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            self.authentication_error.emit(str(e))


class LoginDialog(QDialog):
    """
    Secure login dialog with modern UI design.
    Provides user authentication with error handling and account lockout protection.
    """
    
    # Signals
    login_successful = pyqtSignal(object)  # User object
    login_cancelled = pyqtSignal()
    
    def __init__(self, parent=None):
        """Initialize login dialog."""
        super().__init__(parent)
        self.user = None
        self.auth_worker = None
        self.failed_attempts = 0
        self.max_attempts = 5
        
        self.setWindowTitle("PROJECT-ALPHA - User Authentication")
        self.setModal(True)
        self.setFixedSize(DPIScaler.scale_size(400), DPIScaler.scale_size(300))
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)
        
        self.setup_ui()
        self.setup_connections()
        self.apply_styles()
        
        # Focus on username field
        self.username_edit.setFocus()
    
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(DPIScaler.scale_size(15))
        layout.setContentsMargins(DPIScaler.scale_size(20), DPIScaler.scale_size(20),
                                 DPIScaler.scale_size(20), DPIScaler.scale_size(20))
        
        # Header
        self.create_header(layout)
        
        # Login form
        self.create_login_form(layout)
        
        # Progress bar (hidden initially)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Buttons
        self.create_buttons(layout)
        
        # Status label
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)
    
    def create_header(self, layout):
        """Create header section with logo and title."""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        
        # Title
        title_label = QLabel("PROJECT-ALPHA")
        title_font = QFont()
        title_font.setPointSize(DPIScaler.scale_font_size(16))
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Army Equipment Inventory Management System")
        subtitle_font = QFont()
        subtitle_font.setPointSize(DPIScaler.scale_font_size(10))
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
    
    def create_login_form(self, layout):
        """Create login form with username and password fields."""
        form_group = QGroupBox("User Authentication")
        form_layout = QFormLayout(form_group)
        form_layout.setSpacing(DPIScaler.scale_size(10))
        
        # Username field
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Enter username")
        self.username_edit.setMinimumHeight(DPIScaler.scale_size(32))
        form_layout.addRow("Username:", self.username_edit)
        
        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Enter password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(DPIScaler.scale_size(32))
        form_layout.addRow("Password:", self.password_edit)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("Remember username")
        form_layout.addRow("", self.remember_checkbox)
        
        layout.addWidget(form_group)
    
    def create_buttons(self, layout):
        """Create login and cancel buttons."""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(DPIScaler.scale_size(10))
        
        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setMinimumHeight(DPIScaler.scale_size(36))
        self.cancel_button.setMinimumWidth(DPIScaler.scale_size(80))
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setMinimumHeight(DPIScaler.scale_size(36))
        self.login_button.setMinimumWidth(DPIScaler.scale_size(80))
        self.login_button.setDefault(True)
        button_layout.addWidget(self.login_button)
        
        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """Setup signal connections."""
        self.login_button.clicked.connect(self.attempt_login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.attempt_login)
    
    def apply_styles(self):
        """Apply modern styling to the dialog."""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(5)}px;
                margin-top: {DPIScaler.scale_size(10)}px;
                padding-top: {DPIScaler.scale_size(10)}px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {DPIScaler.scale_size(10)}px;
                padding: 0 {DPIScaler.scale_size(5)}px 0 {DPIScaler.scale_size(5)}px;
            }}
            
            QLineEdit {{
                border: 1px solid #ced4da;
                border-radius: {DPIScaler.scale_size(4)}px;
                padding: {DPIScaler.scale_size(8)}px;
                font-size: {DPIScaler.scale_font_size(11)}px;
                background-color: white;
            }}
            
            QLineEdit:focus {{
                border-color: #007bff;
                outline: none;
            }}
            
            QPushButton {{
                background-color: #007bff;
                border: 1px solid #007bff;
                border-radius: {DPIScaler.scale_size(4)}px;
                color: white;
                font-weight: bold;
                font-size: {DPIScaler.scale_font_size(11)}px;
            }}
            
            QPushButton:hover {{
                background-color: #0056b3;
                border-color: #0056b3;
            }}
            
            QPushButton:pressed {{
                background-color: #004085;
                border-color: #004085;
            }}
            
            QPushButton:disabled {{
                background-color: #6c757d;
                border-color: #6c757d;
            }}
            
            QPushButton#cancel_button {{
                background-color: #6c757d;
                border-color: #6c757d;
            }}
            
            QPushButton#cancel_button:hover {{
                background-color: #545b62;
                border-color: #545b62;
            }}
            
            QCheckBox {{
                font-size: {DPIScaler.scale_font_size(10)}px;
            }}
            
            QProgressBar {{
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(3)}px;
                text-align: center;
                background-color: #f8f9fa;
            }}
            
            QProgressBar::chunk {{
                background-color: #007bff;
                border-radius: {DPIScaler.scale_size(2)}px;
            }}
        """)
        
        # Set object names for styling
        self.cancel_button.setObjectName("cancel_button")
    
    def attempt_login(self):
        """Attempt to authenticate user."""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # Validate input
        if not username:
            self.show_error("Please enter a username.")
            self.username_edit.setFocus()
            return
        
        if not password:
            self.show_error("Please enter a password.")
            self.password_edit.setFocus()
            return
        
        # Check attempt limit
        if self.failed_attempts >= self.max_attempts:
            self.show_error("Too many failed attempts. Please restart the application.")
            return
        
        # Start authentication
        self.set_login_state(True)
        
        # Create and start authentication worker
        self.auth_worker = AuthenticationWorker(username, password)
        self.auth_worker.authentication_complete.connect(self.on_authentication_complete)
        self.auth_worker.authentication_error.connect(self.on_authentication_error)
        self.auth_worker.start()
    
    @pyqtSlot(object)
    def on_authentication_complete(self, user):
        """Handle authentication completion."""
        self.set_login_state(False)
        
        if user:
            # Authentication successful
            self.user = user
            logger.info(f"Login successful for user: {user.username}")
            
            # Save username if remember is checked
            if self.remember_checkbox.isChecked():
                # TODO: Implement username saving to settings
                pass
            
            self.login_successful.emit(user)
            self.accept()
        else:
            # Authentication failed
            self.failed_attempts += 1
            remaining = self.max_attempts - self.failed_attempts
            
            if remaining > 0:
                self.show_error(f"Invalid username or password. {remaining} attempts remaining.")
            else:
                self.show_error("Too many failed attempts. Please restart the application.")
                self.login_button.setEnabled(False)
            
            # Clear password field
            self.password_edit.clear()
            self.password_edit.setFocus()
    
    @pyqtSlot(str)
    def on_authentication_error(self, error_message):
        """Handle authentication error."""
        self.set_login_state(False)
        self.show_error(f"Authentication error: {error_message}")
        logger.error(f"Authentication error: {error_message}")
    
    def set_login_state(self, logging_in):
        """Set UI state during login process."""
        self.login_button.setEnabled(not logging_in)
        self.cancel_button.setEnabled(not logging_in)
        self.username_edit.setEnabled(not logging_in)
        self.password_edit.setEnabled(not logging_in)
        self.remember_checkbox.setEnabled(not logging_in)
        
        if logging_in:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
            self.show_status("Authenticating...", "info")
        else:
            self.progress_bar.setVisible(False)
            self.hide_status()
    
    def show_error(self, message):
        """Show error message to user."""
        self.show_status(message, "error")
        QMessageBox.warning(self, "Authentication Error", message)
    
    def show_status(self, message, status_type="info"):
        """Show status message."""
        self.status_label.setText(message)
        self.status_label.setVisible(True)
        
        if status_type == "error":
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        elif status_type == "success":
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.status_label.setStyleSheet("color: #6c757d;")
    
    def hide_status(self):
        """Hide status message."""
        self.status_label.setVisible(False)
    
    def get_authenticated_user(self):
        """Get the authenticated user object."""
        return self.user
    
    def closeEvent(self, event):
        """Handle dialog close event."""
        if self.auth_worker and self.auth_worker.isRunning():
            self.auth_worker.terminate()
            self.auth_worker.wait()
        
        self.login_cancelled.emit()
        event.accept()
