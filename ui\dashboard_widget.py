"""Dashboard widget for the equipment inventory application - HORIZONTAL LAYOUT."""
import logging
from datetime import datetime, date, timedelta

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                           QScrollArea, QLabel, QFrame, QPushButton, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont

import matplotlib
matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

import database
import utils
from models import (Equipment, Fluid, Maintenance, Repair,
                  DiscardCriteria, TyreMaintenance, Overhaul, DemandForecast, Battery)
from ui.custom_widgets import AlertTile
from conditioning_status_service import conditioning_status_service
from conditioning_status_enums import TyreRotation<PERSON>tatus, TyreConditionStatus, BatteryStatus, StatusColors

# Configure logger
logger = logging.getLogger('dashboard_widget')

class ModernDashboardTile(QFrame):
    """Modern dashboard tile with DPI-aware responsive design and navigation capability."""

    clicked = pyqtSignal(str)  # Signal to emit when clicked (tile type)

    def __init__(self, icon, title, value=0, status="normal", tile_type=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.status = status
        self.icon = icon
        self.tile_type = tile_type  # For navigation purposes

        self.setup_ui()
        self.update_value(value, status)
    
    def setup_ui(self):
        """Set up the tile UI with DPI-aware responsive design."""
        from ui.window_utils import DPIScaler, LayoutManager
        
        # Get responsive tile size based on DPI - adjusted for better container fit
        tile_width, tile_height = DPIScaler.get_responsive_tile_size(260, 110)
        self.setMinimumSize(tile_width, tile_height)
        self.setMaximumSize(tile_width + 20, tile_height + 10)  # Allow some flexibility
        
        # Create main layout with responsive margins
        layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(layout, margins=(10, 8, 10, 8), spacing=6)
        
        # Top row: Icon and value
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # Icon label with scaled font
        self.icon_label = QLabel(self.icon)
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        icon_font = DPIScaler.create_scaled_font(16, bold=False)
        self.icon_label.setFont(icon_font)
        top_layout.addWidget(self.icon_label)
        
        # Spacer
        top_layout.addStretch()
        
        # Value label with scaled font
        self.value_label = QLabel(str(self.value))
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        value_font = DPIScaler.create_scaled_font(22, bold=True)
        self.value_label.setFont(value_font)
        top_layout.addWidget(self.value_label)
        
        layout.addLayout(top_layout)
        
        # Bottom row: Title with scaled font
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        title_font = DPIScaler.create_scaled_font(10, bold=True)
        self.title_label.setFont(title_font)
        self.title_label.setWordWrap(True)
        layout.addWidget(self.title_label)
        
        # Apply initial styling
        self.apply_status_style()

        # Make tile clickable if tile_type is provided
        if self.tile_type:
            self.setCursor(Qt.PointingHandCursor)
            def handle_mouse_press(event):
                self.clicked.emit(self.tile_type)
            self.mousePressEvent = handle_mouse_press
    
    def update_value(self, value, status="normal"):
        """Update the tile value and status."""
        self.value = value
        self.status = status
        self.value_label.setText(str(value))
        self.apply_status_style()
    
    def apply_status_style(self):
        """Apply DPI-aware styling based on status."""
        from ui.window_utils import DPIScaler
        
        # Define color schemes
        colors = {
            "normal": {"bg": "#e8f5e8", "border": "#4CAF50", "text": "#2e7d32", "value": "#1b5e20"},
            "warning": {"bg": "#fff3e0", "border": "#FF9800", "text": "#ef6c00", "value": "#e65100"},
            "critical": {"bg": "#ffebee", "border": "#F44336", "text": "#d32f2f", "value": "#c62828"},
            "neutral": {"bg": "#f3f4f6", "border": "#607D8B", "text": "#455a64", "value": "#37474f"}
        }
        
        color_scheme = colors.get(self.status, colors["normal"])
        
        # Scale border width and radius for DPI
        border_width = DPIScaler.scale_size(2, min_size=1, max_size=4)
        border_radius = DPIScaler.scale_size(8, min_size=4, max_size=12)
        margin = DPIScaler.scale_size(2, min_size=1, max_size=3)  # Reduced margin to prevent squishing
        
        # Style the frame with DPI-aware dimensions
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {color_scheme["bg"]};
                border: {border_width}px solid {color_scheme["border"]};
                border-radius: {border_radius}px;
                margin: {margin}px;
            }}
            QFrame:hover {{
                border: {border_width + 1}px solid {color_scheme["border"]};
                background-color: {color_scheme["bg"]};
            }}
        """)
        
        # Style labels with transparency for proper rendering
        for label, color_key in [(self.icon_label, "border"), 
                               (self.value_label, "value"), 
                               (self.title_label, "text")]:
            label.setStyleSheet(f"""
                QLabel {{
                    color: {color_scheme[color_key]};
                    background: transparent;
                    border: none;
                }}
            """)

class HorizontalAlertTile(QFrame):
    """Horizontal alert tile with DPI-aware responsive design."""
    
    clicked = pyqtSignal(str, int)  # Signal to emit when clicked (type, id)
    
    def __init__(self, title, message, alert_type, item_id, status="critical", parent=None):
        super().__init__(parent)
        
        self.alert_type = alert_type
        self.item_id = item_id
        
        # Set DPI-aware height for better visibility
        from ui.window_utils import DPIScaler
        min_height = DPIScaler.scale_size(85, min_size=70, max_size=120)
        max_height = DPIScaler.scale_size(110, min_size=90, max_size=150)
        self.setMinimumHeight(min_height)
        self.setMaximumHeight(max_height)
        
        self.setup_ui(title, message, status)
    
    def setup_ui(self, title, message, status):
        """Set up the horizontal alert tile UI - OPTIMIZED for compressed column."""
        # Status color mapping - Updated to match new color scheme
        colors = {
            "warning": {"bg": "#fff3e0", "border": "#ff9900", "text": "#cc6600"},
            "critical": {"bg": "#ffebee", "border": "#ff6600", "text": "#cc3300"},
            "normal": {"bg": "#e8f5e8", "border": "#33cc33", "text": "#2a9929"}
        }
        
        color_scheme = colors.get(status, colors["critical"])
        
        # Style the frame with enhanced clickability indicators - COMPRESSED
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {color_scheme["bg"]};
                border: 2px solid {color_scheme["border"]};
                border-radius: 6px;
                margin: 2px;
                padding: 3px;
            }}
            QFrame:hover {{
                border: 3px solid {color_scheme["border"]};
                background-color: {self.lighten_color(color_scheme["bg"])};
            }}
        """)

        # Set cursor using PyQt5 method instead of CSS
        self.setCursor(Qt.PointingHandCursor)
        
        # Create layout - IMPROVED spacing for alerts
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 8, 10, 8)  # Increased margins for better text display
        layout.setSpacing(10)  # Increased spacing
        
        # Left side: Status icon - SMALLER
        status_icons = {"warning": "⚠️", "critical": "🚨", "normal": "ℹ️"}
        icon_label = QLabel(status_icons.get(status, "🚨"))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setFixedSize(20, 20)  # Smaller icon
        icon_font = QFont()
        icon_font.setPointSize(10)  # Smaller font
        icon_label.setFont(icon_font)
        layout.addWidget(icon_label)
        
        # Middle: Content area - IMPROVED
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 2, 0, 2)  # Added vertical margins
        content_layout.setSpacing(3)  # Increased spacing for better text separation
        
        # Title - SMALLER
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        title_font = QFont()
        title_font.setPointSize(9)  # Smaller font
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {color_scheme['text']}; background: transparent; border: none;")
        title_label.setWordWrap(True)
        content_layout.addWidget(title_label)
        
        # Message - IMPROVED
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        message_font = QFont()
        message_font.setPointSize(9)  # Increased font size for better readability
        message_label.setFont(message_font)
        message_label.setStyleSheet(f"color: {color_scheme['text']}; background: transparent; border: none;")
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)
        
        layout.addLayout(content_layout, 1)  # Stretch factor 1
        
        # Right side: View button - SMALLER
        view_button = QPushButton("View")
        view_button.setFixedSize(40, 25)  # Smaller button
        view_button.clicked.connect(self.on_action_clicked)
        view_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color_scheme["border"]};
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {color_scheme["text"]};
            }}
        """)
        layout.addWidget(view_button)
        
        # Make the entire tile clickable
        def handle_mouse_press(a0):
            self.on_action_clicked()
        self.mousePressEvent = handle_mouse_press
    
    def on_action_clicked(self):
        """Emit signal when clicked."""
        self.clicked.emit(self.alert_type, self.item_id)

    def lighten_color(self, color_hex):
        """Lighten a hex color for hover effect."""
        try:
            # Remove # if present
            color_hex = color_hex.lstrip('#')

            # Convert to RGB
            r = int(color_hex[0:2], 16)
            g = int(color_hex[2:4], 16)
            b = int(color_hex[4:6], 16)

            # Lighten by 10%
            r = min(255, int(r + (255 - r) * 0.1))
            g = min(255, int(g + (255 - g) * 0.1))
            b = min(255, int(b + (255 - b) * 0.1))

            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color_hex  # Return original if conversion fails

class TMMaintenanceChart(QWidget):
    """Pie chart widget for TM-1 and TM-2 maintenance distribution with actual matplotlib chart."""

    def __init__(self, parent=None):
        super().__init__(parent)
        # Use responsive height based on screen resolution
        from ui.window_utils import LowResolutionManager
        layout_config = LowResolutionManager.get_optimized_dashboard_layout()
        min_height = layout_config['chart_min_height'] + 80  # Add extra for pie chart
        self.setMinimumHeight(min_height)
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the chart UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # Consistent margins for better alignment
        layout.setSpacing(5)  # Consistent spacing
        
        # Title - consistent across all charts
        title = QLabel("Maintenance Status Distribution")  # UPDATED HEADER
        title_font = QFont()
        title_font.setPointSize(11)  # Slightly larger for better visibility
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Create matplotlib figure and canvas with LARGER sizing for pie charts and longer labels
        self.figure = Figure(figsize=(7.0, 5.0), dpi=80)  # Further increased size for longer labels
        self.canvas = FigureCanvas(self.figure)

        # Optimized canvas sizing for larger pie charts with longer labels
        self.canvas.setMinimumSize(380, 280)  # Increased minimum size for longer labels
        self.canvas.setMaximumSize(700, 450)  # Increased maximum size for longer labels
        layout.addWidget(self.canvas)
    
    def update_chart(self):
        """Update the TM maintenance chart with status distribution."""
        try:
            from performance_optimizations import PerformanceContext, ChartOptimizer, MemoryOptimizer

            with PerformanceContext("TM maintenance chart update"):
                # Clean up previous chart memory
                MemoryOptimizer.cleanup_matplotlib_figures()

                # Clear the figure properly
                self.figure.clear()

                # Get TM-1 and TM-2 maintenance data - FIXED to show actual distribution
                tm_data = self.get_tm_maintenance_distribution()
                print(f"[DEBUG] TM Chart Raw Data: {tm_data}")

                # Show chart if ANY value is nonzero
                if not tm_data or all(v == 0 for v in tm_data.values()):
                    ax = self.figure.add_subplot(111)
                    ax.text(0.5, 0.5, 'No maintenance data available',
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=12, color='#6c757d')
                    ax.set_xlim(0, 1)
                    ax.set_ylim(0, 1)
                    ax.axis('off')
                    self.canvas.draw()
                    import gc
                    gc.collect()
                    return
            
            ax = self.figure.add_subplot(111)
            filtered_data = {k: v for k, v in tm_data.items() if v > 0}
            print(f"[DEBUG] TM Chart Filtered Data: {filtered_data}")
            
            # Fallback: If filtered_data is empty but tm_data has at least one nonzero, use tm_data
            if not filtered_data and any(v > 0 for v in tm_data.values()):
                filtered_data = tm_data
            
            if not filtered_data:
                ax.text(0.5, 0.5, 'No maintenance data available',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='#6c757d')
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
                self.canvas.draw()
                return
            
            labels = list(filtered_data.keys())
            sizes = list(filtered_data.values())
            color_mapping = {
                'overdue': '#cc0000',
                'critical (under 7 days)': '#ff6600',
                'warning (under 30 days)': '#ff9900',
                'upcoming (under 90 days)': '#669900',
                'scheduled (over 90 days)': '#009900',
                'completed': '#33cc33'
            }
            colors = [color_mapping.get(label.lower(), '#95a5a6') for label in labels]
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                            autopct='%1.1f%%', startangle=90,
                                            textprops={'fontsize': 8}, pctdistance=0.85,
                                            labeldistance=1.05)  # Move labels slightly closer
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(11)
            for text in texts:
                text.set_fontsize(8)  # Smaller font for longer labels
                text.set_fontweight('bold')
            ax.set_title('Maintenance Status Distribution', fontsize=12, fontweight='bold', pad=15)
            # Adjust margins to accommodate longer labels
            self.figure.subplots_adjust(left=0.02, right=0.98, top=0.82, bottom=0.02)
            self.canvas.draw()
            import gc
            gc.collect()
        except Exception as e:
            print(f"[ERROR] TM Chart Exception: {e}")
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'Error loading chart:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=10, color='red')
            ax.axis('off')
            self.canvas.draw()
            import gc
            gc.collect()
    
    def get_tm_maintenance_distribution(self):
        """Get TM-1 and TM-2 maintenance status distribution - showing all status categories."""
        try:
            query = """
                SELECT m.*, e.make_and_type, e.ba_number
                FROM maintenance m
                JOIN equipment e ON m.equipment_id = e.equipment_id
                WHERE m.maintenance_category IN ('TM-1', 'TM-2')
                AND e.is_active = 1
                AND (m.status != 'archived' OR m.status IS NULL)
                ORDER BY m.next_due_date
            """
            
            results = database.execute_query(query)
            
            if not results:
                return {}
            
            # Count maintenance by calculated status with proper labels
            status_counts = {
                'Overdue': 0,
                'Critical (Under 7 days)': 0, 
                'Warning (Under 30 days)': 0,
                'Upcoming (Under 90 days)': 0,
                'Scheduled (Over 90 days)': 0,
                'Completed': 0
            }
            
            for maintenance in results:
                # Use the same status calculation logic as the dashboard
                category = maintenance.get('maintenance_category', '')
                calculated_status = self.calculate_maintenance_status_for_category(maintenance, category)
                
                # Map calculated status to pie chart categories with proper labels
                if calculated_status == "overdue":
                    status_counts['Overdue'] += 1
                elif calculated_status == "critical":
                    status_counts['Critical (Under 7 days)'] += 1
                elif calculated_status == "warning":
                    status_counts['Warning (Under 30 days)'] += 1
                elif calculated_status == "upcoming":
                    status_counts['Upcoming (Under 90 days)'] += 1
                elif calculated_status == "scheduled":
                    status_counts['Scheduled (Over 90 days)'] += 1
                elif calculated_status == "completed":
                    status_counts['Completed'] += 1
                    
            return status_counts
            
        except Exception as e:
            logger.error(f"Error getting TM maintenance distribution: {e}")
            return {}

    def calculate_maintenance_status_for_category(self, maintenance, category):
        """
        Calculate maintenance status for a specific category for TM chart.
        Delegates to utils.calculate_maintenance_status for consistency.
        """
        try:
            db_status = maintenance.get('status', '')
            if db_status == 'completed':
                return 'completed'
            done_date_val = maintenance.get('done_date')
            if done_date_val:
                next_due_date = utils.calculate_next_due_date(done_date_val, category)
                if next_due_date:
                    maintenance_for_status = {
                        'status': 'scheduled',
                        'due_date': next_due_date.isoformat()
                    }
                else:
                    maintenance_for_status = {
                        'status': maintenance.get('status', ''),
                        'due_date': maintenance.get('next_due_date')
                    }
            else:
                maintenance_for_status = maintenance
            return utils.calculate_maintenance_status(maintenance_for_status)
        except Exception as e:
            import logging
            logging.getLogger('dashboard_widget').error(f"Error calculating maintenance status for category {category} (TM Chart): {e}")
            return "unknown"

class BatteryMaintenanceChart(QWidget):
    """Area chart widget for battery maintenance status with actual matplotlib chart."""

    def __init__(self, parent=None):
        super().__init__(parent)
        # Use responsive height based on screen resolution
        from ui.window_utils import LowResolutionManager
        layout_config = LowResolutionManager.get_optimized_dashboard_layout()
        min_height = layout_config['chart_min_height'] + 50  # Add extra for area chart
        self.setMinimumHeight(min_height)
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the chart UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)  # Reduced margins for grid layout
        layout.setSpacing(3)  # Reduced spacing for grid layout

        # Title - match TM-1 & TM-2 Distribution header
        title = QLabel("Battery Age Distribution")
        title_font = QFont()
        title_font.setPointSize(10)  # Match other charts
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Create matplotlib figure and canvas with optimized sizing for expanded 2x2 grid
        self.figure = Figure(figsize=(4.5, 3.2), dpi=80)  # Match other charts
        self.canvas = FigureCanvas(self.figure)

        # Optimized canvas sizing for expanded 2x2 grid - match other charts
        self.canvas.setMinimumSize(250, 160)  # Consistent size with other charts
        self.canvas.setMaximumSize(400, 240)  # Allow consistent growth
        layout.addWidget(self.canvas)
    
    def update_chart(self):
        """Update the battery maintenance chart with current data."""
        try:
            # Clear the figure
            self.figure.clear()
            
            # Get battery maintenance data
            battery_data = self.get_battery_maintenance_data()
            
            if not battery_data:
                # Show "no data" message
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, 'No battery\nmaintenance data\navailable', 
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=10, color='#6c757d')
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
                self.canvas.draw()
                return
            
            # Create area chart
            ax = self.figure.add_subplot(111)
            
            # Process data for bar chart
            ages = []
            equipment_labels = []
            
            # Fill data based on query results
            for row in battery_data:
                age = float(row['age_years'])
                ages.append(age)
                # Format equipment label
                label = row['ba_number'] if row['ba_number'] else row['make_and_type']
                equipment_labels.append(label)
            
            if not ages:
                ax.text(0.5, 0.5, 'No battery\nmaintenance data\navailable', 
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=10, color='#6c757d')
                ax.axis('off')
                return
            
            # Create bar chart
            bars = ax.bar(range(len(ages)), ages)
            
            # Color bars based on age
            for bar, age in zip(bars, ages):
                if age <= 2:
                    bar.set_color('#2ecc71')  # Green for new
                elif age <= 4:
                    bar.set_color('#f1c40f')  # Yellow for good
                elif age <= 6:
                    bar.set_color('#e67e22')  # Orange for replace soon
                else:
                    bar.set_color('#e74c3c')  # Red for replace now
            
            # Customize the plot
            ax.set_title('Battery Age Distribution', fontsize=9, fontweight='bold', pad=5)
            ax.set_xlabel('Equipment', fontsize=8)
            ax.set_ylabel('Age (Years)', fontsize=8)
            
            # Rotate x-axis labels for better readability
            plt.xticks(range(len(equipment_labels)), equipment_labels, rotation=45, ha='right', fontsize=6)
            
            # Add horizontal lines for age thresholds
            ax.axhline(y=2, color='#2ecc71', linestyle='--', alpha=0.3)  # New threshold
            ax.axhline(y=4, color='#f1c40f', linestyle='--', alpha=0.3)  # Good threshold
            ax.axhline(y=6, color='#e67e22', linestyle='--', alpha=0.3)  # Replace soon threshold
            
            # Add grid
            ax.grid(True, axis='y', linestyle='--', alpha=0.2)
            
            # Set y-axis limits with some padding
            ax.set_ylim(0, max(ages) * 1.1 if ages else 8)
            
            # Adjust layout
            self.figure.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"Error updating battery maintenance chart: {e}")
            # Show error message
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'Error loading chart:\n{str(e)}', 
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=9, color='red')
            ax.axis('off')
            self.canvas.draw()
    
    def get_battery_maintenance_data(self):
        """Get equipment age data as proxy for battery age (since equipment batteries age with equipment)."""
        try:
            query = """
                SELECT 
                    e.equipment_id,
                    e.make_and_type,
                    e.ba_number,
                    e.vintage_years as age_years
                FROM equipment e
                WHERE e.is_active = 1 AND e.vintage_years > 0
                ORDER BY e.vintage_years DESC
                LIMIT 15
            """
            
            results = database.execute_query(query)
            
            # Return the raw results directly
            return results
            
        except Exception as e:
            logger.error(f"Error getting battery age data: {e}")
            return []

class TyreDemandForecastChart(QWidget):
    """Bar chart widget for tyre demand forecast with actual matplotlib chart."""

    def __init__(self, parent=None):
        super().__init__(parent)
        # Use responsive height based on screen resolution
        from ui.window_utils import LowResolutionManager
        layout_config = LowResolutionManager.get_optimized_dashboard_layout()
        min_height = layout_config['chart_min_height'] + 50  # Add extra for bar chart
        self.setMinimumHeight(min_height)
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the chart UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # Consistent margins for better alignment
        layout.setSpacing(5)  # Consistent spacing
        
        # Title - consistent across all charts
        title = QLabel("Tyre Demand Forecast")
        title_font = QFont()
        title_font.setPointSize(11)  # Consistent with other charts
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Create matplotlib figure and canvas with standardized sizing for better space utilization
        self.figure = Figure(figsize=(5.5, 4.0), dpi=80)  # Consistent with other charts
        self.canvas = FigureCanvas(self.figure)

        # Optimized canvas sizing to fill available space better
        self.canvas.setMinimumSize(280, 200)  # Consistent with other charts
        self.canvas.setMaximumSize(600, 350)  # Consistent sizing for better space utilization
        layout.addWidget(self.canvas)
    
    def update_chart(self):
        """Update the tyre demand forecast chart with current data - OPTIMIZED for expanded space."""
        try:
            # Clear the figure
            self.figure.clear()
            
            # Get tyre demand forecast data
            tyre_data = self.get_tyre_demand_forecast_data()
            
            if not tyre_data:
                # Show "no data" message
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, 'No tyre demand\nforecast data\navailable', 
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=14, color='#6c757d')
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
                self.canvas.draw()
                return
            
            # Create bar chart - OPTIMIZED for larger space
            ax = self.figure.add_subplot(111)
            
            # Prepare data for chart
            categories = list(tyre_data.keys())
            values = list(tyre_data.values())
            
            if not categories:
                ax.text(0.5, 0.5, 'No forecast data available', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=14, color='#6c757d')
                ax.axis('off')
                self.canvas.draw()
                return
            
            # Create larger bar chart with modern color scheme
            colors = ['#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#F44336']
            bars = ax.bar(categories, values, color=colors[:len(categories)], alpha=0.8, width=0.6)
            
            # Add larger value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.annotate(f'{int(value)}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 5),  # Increased offset
                           textcoords="offset points",
                           ha='center', va='bottom',
                           fontsize=12, fontweight='bold')  # Larger font
            
            # Larger labels and title for expanded space
            ax.set_ylabel('Total Requirement', fontsize=12, fontweight='bold')
            ax.set_title('Tyre Demand by Type', fontsize=14, fontweight='bold', pad=15)
            
            # Improved x-axis labels - rotate for longer tyre type names
            ax.tick_params(axis='x', labelsize=9, rotation=45)  # Rotate for better readability
            ax.tick_params(axis='y', labelsize=11)
            
            # Improved chart layout to prevent text cutoff and better alignment - INCREASED bottom space for rotated labels
            self.figure.subplots_adjust(left=0.15, right=0.95, top=0.85, bottom=0.45)
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"Error updating tyre demand forecast chart: {e}")
            # Show error message
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'Error loading chart:\n{str(e)}', 
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=12, color='red')
            ax.axis('off')
            self.canvas.draw()
    
    def get_tyre_demand_forecast_data(self):
        """Get tyre demand forecast data grouped by tyre type for current fiscal year."""
        try:
            from datetime import datetime
            current_year = datetime.now().year
            
            # Import TyreForecast here to avoid circular imports
            from models import TyreForecast
            
            # Get forecasts for current fiscal year
            forecasts = TyreForecast.get_by_fiscal_year(str(current_year))
            
            # If no data for current year, try next year
            if not forecasts:
                forecasts = TyreForecast.get_by_fiscal_year(str(current_year + 1))
            
            # If still no data, return empty
            if not forecasts:
                return {}
            
            # Group by tyre type and sum total requirements - SAME AS DEMAND FORECAST TAB
            tyre_totals = {}
            for forecast in forecasts:
                tyre_type = forecast.get('tyre_type', 'Unknown')
                total_req = forecast.get('total_requirement', 0)
                
                # Use full tyre type name like demand forecast tab - NO TRUNCATION
                if tyre_type in tyre_totals:
                    tyre_totals[tyre_type] += total_req
                else:
                    tyre_totals[tyre_type] = total_req
            
            # Remove zero values for cleaner chart
            tyre_totals = {k: v for k, v in tyre_totals.items() if v > 0}
            
            return tyre_totals
            
        except Exception as e:
            logger.error(f"Error getting tyre demand forecast data: {e}")
            return {}

class BatteryAgeDistributionChart(QWidget):
    """Pie chart widget for battery age distribution with actual matplotlib chart."""

    def __init__(self, parent=None):
        super().__init__(parent)
        # Use responsive height based on screen resolution
        from ui.window_utils import LowResolutionManager
        layout_config = LowResolutionManager.get_optimized_dashboard_layout()
        min_height = layout_config['chart_min_height'] + 80  # Add extra for pie chart
        self.setMinimumHeight(min_height)
        self.setup_ui()

    def setup_ui(self):
        """Set up the chart UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # Consistent margins for better alignment
        layout.setSpacing(5)  # Consistent spacing

        # Title - consistent across all charts
        title = QLabel("Battery Age Distribution")
        title_font = QFont()
        title_font.setPointSize(11)  # Consistent with other charts
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Create matplotlib figure and canvas with LARGER sizing for pie charts
        self.figure = Figure(figsize=(6.0, 4.5), dpi=80)  # Increased size for pie charts
        self.canvas = FigureCanvas(self.figure)

        # Optimized canvas sizing for larger pie charts
        self.canvas.setMinimumSize(320, 220)  # Increased minimum size for pie charts
        self.canvas.setMaximumSize(650, 400)  # Increased maximum size for pie charts
        layout.addWidget(self.canvas)

    def update_chart(self):
        """Update the battery age distribution chart with current data."""
        try:
            # Clear the figure
            self.figure.clear()

            # Get battery age data
            battery_data = self.get_battery_age_data()

            if not battery_data:
                # Show "no data" message
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, 'No battery\nage data\navailable',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='#6c757d')
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
                self.canvas.draw()
                return

            # Process data into age categories
            age_categories = self.categorize_battery_ages(battery_data)

            if not age_categories or sum(age_categories.values()) == 0:
                # Show "no data" message
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, 'No valid battery\nage data\navailable',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='#6c757d')
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
                self.canvas.draw()
                return

            # Create pie chart
            ax = self.figure.add_subplot(111)

            # Filter out categories with zero values
            filtered_categories = {k: v for k, v in age_categories.items() if v > 0}

            if not filtered_categories:
                ax.text(0.5, 0.5, 'No batteries\nwith valid ages',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='#6c757d')
                ax.axis('off')
                self.canvas.draw()
                return

            labels = list(filtered_categories.keys())
            sizes = list(filtered_categories.values())

            # Define colors for age categories
            color_map = {
                'New (0-6 months)': '#2ecc71',
                'Good (6-12 months)': '#f1c40f',
                'Aging (12-18 months)': '#e67e22',
                'Replace Soon (18-24 months)': '#e74c3c',
                'Replace Now (24+ months)': '#8e44ad'
            }
            colors = [color_map.get(label, '#95a5a6') for label in labels]

            # Create pie chart with percentage labels
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                            autopct='%1.1f%%', startangle=90,
                                            textprops={'fontsize': 8})

            # Improve text appearance
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(8)

            # Adjust label text size
            for text in texts:
                text.set_fontsize(7)

            # Calculate average age statistics
            total_age_months = sum(battery['age_months'] for battery in battery_data if battery['age_months'] >= 0)
            avg_age = total_age_months / len([b for b in battery_data if b['age_months'] >= 0]) if battery_data else 0

            # Add average age text below the chart (removed total batteries count)
            summary_text = f"Average Age: {avg_age:.1f} months"
            ax.text(0.5, -0.15, summary_text, ha='center', va='center',
                   transform=ax.transAxes, fontsize=10, fontweight='bold', color='#2c3e50')

            ax.set_title('Battery Fleet Age Distribution', fontsize=10, fontweight='bold', pad=10)

            # Improved chart layout to prevent text cutoff and better alignment
            self.figure.subplots_adjust(left=0.05, right=0.95, top=0.85, bottom=0.15)
            self.canvas.draw()

        except Exception as e:
            logger.error(f"Error updating battery age distribution chart: {e}")
            # Show error message
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'Error loading chart:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=10, color='red')
            ax.axis('off')
            self.canvas.draw()

    def get_battery_age_data(self):
        """Get battery age data from the battery table."""
        try:
            from datetime import datetime, date

            query = """
                SELECT b.battery_id, b.equipment_id, b.done_date, b.custom_life_months,
                       e.make_and_type, e.ba_number
                FROM battery b
                JOIN equipment e ON b.equipment_id = e.equipment_id
                WHERE b.done_date IS NOT NULL
                  AND e.is_active = 1
                  AND b.done_date <= date('now')
                ORDER BY b.done_date DESC
            """

            results = database.execute_query(query)

            if not results:
                return []

            # Calculate age for each battery
            current_date = date.today()
            battery_ages = []

            for row in results:
                try:
                    # Parse done_date (installation date)
                    done_date_str = row['done_date']
                    if isinstance(done_date_str, str):
                        # Try different date formats
                        for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']:
                            try:
                                done_date = datetime.strptime(done_date_str, fmt).date()
                                break
                            except ValueError:
                                continue
                        else:
                            # If no format worked, skip this record
                            logger.warning(f"Could not parse date: {done_date_str}")
                            continue
                    elif isinstance(done_date_str, date):
                        done_date = done_date_str
                    else:
                        continue

                    # Calculate age in months
                    age_days = (current_date - done_date).days
                    age_months = age_days / 30.44  # Average days per month

                    # Filter valid ages (0-60 months)
                    if 0 <= age_months <= 60:
                        battery_ages.append({
                            'battery_id': row['battery_id'],
                            'equipment_id': row['equipment_id'],
                            'make_and_type': row['make_and_type'],
                            'ba_number': row['ba_number'],
                            'done_date': done_date,
                            'age_months': age_months,
                            'custom_life_months': row.get('custom_life_months', 24)
                        })

                except Exception as e:
                    logger.warning(f"Error processing battery record {row.get('battery_id', 'unknown')}: {e}")
                    continue

            return battery_ages

        except Exception as e:
            logger.error(f"Error getting battery age data: {e}")
            return []

    def categorize_battery_ages(self, battery_data):
        """Categorize batteries into age ranges for the pie chart."""
        categories = {
            'New (0-6 months)': 0,
            'Good (6-12 months)': 0,
            'Aging (12-18 months)': 0,
            'Replace Soon (18-24 months)': 0,
            'Replace Now (24+ months)': 0
        }

        for battery in battery_data:
            age_months = battery['age_months']

            if 0 <= age_months < 6:
                categories['New (0-6 months)'] += 1
            elif 6 <= age_months < 12:
                categories['Good (6-12 months)'] += 1
            elif 12 <= age_months < 18:
                categories['Aging (12-18 months)'] += 1
            elif 18 <= age_months < 24:
                categories['Replace Soon (18-24 months)'] += 1
            elif age_months >= 24:
                categories['Replace Now (24+ months)'] += 1

        return categories

class FluidDemandChart(QWidget):
    """Bar chart widget for fluids demand forecast with actual matplotlib chart."""

    def __init__(self, parent=None):
        super().__init__(parent)
        # Use responsive height based on screen resolution
        from ui.window_utils import LowResolutionManager
        layout_config = LowResolutionManager.get_optimized_dashboard_layout()
        min_height = layout_config['chart_min_height'] + 80  # Add extra for bar chart
        self.setMinimumHeight(min_height)
        self.setup_ui()

    def setup_ui(self):
        """Set up the chart UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # Consistent margins for better alignment
        layout.setSpacing(5)  # Consistent spacing

        # Title - consistent across all charts
        title = QLabel("Fluids Demand Forecast")
        title_font = QFont()
        title_font.setPointSize(11)  # Consistent with other charts
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Create matplotlib figure and canvas with standardized sizing for better space utilization
        self.figure = Figure(figsize=(5.5, 4.0), dpi=80)  # Consistent with other charts
        self.canvas = FigureCanvas(self.figure)

        # Optimized canvas sizing to fill available space better
        self.canvas.setMinimumSize(280, 200)  # Consistent with other charts
        self.canvas.setMaximumSize(600, 350)  # Consistent sizing for better space utilization
        layout.addWidget(self.canvas)
    
    def update_chart(self):
        """Update the fluids demand chart with current data - OPTIMIZED for expanded space."""
        try:
            # Clear the figure
            self.figure.clear()
            
            # Get fluids demand forecast data
            demand_data = self.get_fluids_demand_forecast_data()
            
            if not demand_data:
                # Show "no data" message
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, 'No fluids demand\nforecast data\navailable', 
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=14, color='#6c757d')
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
                self.canvas.draw()
                return
            
            # Create bar chart - OPTIMIZED for larger space
            ax = self.figure.add_subplot(111)
            
            # Prepare data for chart - FIXED: Show fluid types vs liters
            fluid_totals = {}
            for fy_data in demand_data.values():
                for fluid_type, amount in fy_data.items():
                    if fluid_type not in fluid_totals:
                        fluid_totals[fluid_type] = 0
                    fluid_totals[fluid_type] += amount
            
            fluid_types = list(fluid_totals.keys())
            totals = list(fluid_totals.values())
            
            if not fluid_types:
                ax.text(0.5, 0.5, 'No data available', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=14, color='#6c757d')
                ax.axis('off')
                self.canvas.draw()
                return
            
            # Create larger bar chart with different colors for each fluid type
            colors = ['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0', '#00BCD4', '#795548', '#607D8B']
            bar_colors = [colors[i % len(colors)] for i in range(len(fluid_types))]
            bars = ax.bar(fluid_types, totals, color=bar_colors, alpha=0.8, width=0.6)
            
            # Add larger value labels on bars
            for bar, total in zip(bars, totals):
                height = bar.get_height()
                ax.annotate(f'{total:.0f}L',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 5),  # Increased offset
                           textcoords="offset points",
                           ha='center', va='bottom',
                           fontsize=11, fontweight='bold')  # Larger font
            
            # Larger labels and title for expanded space
            ax.set_xlabel('Fluid Grade', fontsize=12, fontweight='bold')
            ax.set_ylabel('Total Requirement (Ltrs)', fontsize=12, fontweight='bold')
            ax.set_title('Fluid Demand by Grade', fontsize=14, fontweight='bold', pad=15)
            
            # Improved axis labels with rotation for better fit
            ax.tick_params(axis='x', labelsize=10, rotation=35)  # Slight rotation, larger font
            ax.tick_params(axis='y', labelsize=11)
            
            # Improved chart layout to prevent text cutoff and better alignment
            self.figure.subplots_adjust(left=0.15, right=0.95, top=0.85, bottom=0.30)  # More bottom space for rotated labels
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"Error updating fluids demand chart: {e}")
            # Show error message
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'Error loading chart:\n{str(e)}', 
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=12, color='red')
            ax.axis('off')
            self.canvas.draw()
    
    def get_fluids_demand_forecast_data(self):
        """Get fluids demand forecast data grouped by fiscal year."""
        try:
            query = """
                SELECT 
                    df.fiscal_year, 
                    COALESCE(f.grade, f.fluid_type) as fluid_grade, 
                    SUM(df.total_requirement) as total_requirement
                FROM demand_forecast df
                JOIN fluids f ON df.fluid_id = f.fluid_id
                GROUP BY df.fiscal_year, fluid_grade
                ORDER BY df.fiscal_year, fluid_grade
            """
            
            results = database.execute_query(query)
            
            # Process results into nested structure
            data = {}
            for row in results:
                fy = row['fiscal_year']
                fluid_grade = row['fluid_grade']
                requirement = float(row['total_requirement'] or 0)
                
                if fy not in data:
                    data[fy] = {}
                data[fy][fluid_grade] = requirement
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting fluids demand forecast data: {e}")
            return {}

class DashboardWidget(QWidget):
    """Horizontal 3-column dashboard widget."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._parent_widget = parent  # Store parent widget reference
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the horizontal dashboard UI."""
        # Get low-resolution optimizations
        from ui.window_utils import LowResolutionManager
        layout_config = LowResolutionManager.get_optimized_dashboard_layout()

        # Create main horizontal layout - OPTIMIZED for edge-to-edge panels
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 10, 5, 10)  # Reduced side margins for edge-to-edge
        main_layout.setSpacing(8)  # Reduced spacing between panels

        # ===============================
        # LEFT COLUMN: Equipment Overview (LEFTMOST EDGE)
        # ===============================
        equipment_frame = QFrame()
        # Fixed width for equipment frame to maintain consistency and prevent text truncation
        if layout_config['use_compact_tiles']:
            equipment_frame.setFixedWidth(400)  # Further increased width to prevent Make & Type text cutoff
        else:
            equipment_frame.setFixedWidth(450)  # Further increased width to prevent Make & Type text cutoff
        equipment_frame.setFrameShape(QFrame.Shape.StyledPanel)
        equipment_frame.setFrameShadow(QFrame.Shadow.Raised)
        equipment_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        equipment_layout = QVBoxLayout(equipment_frame)
        # Use responsive margins and spacing
        eq_margin = 8 if layout_config['use_compact_tiles'] else 10
        eq_spacing = 4 if layout_config['use_compact_tiles'] else 5
        equipment_layout.setContentsMargins(eq_margin, eq_margin, eq_margin, eq_margin)
        equipment_layout.setSpacing(eq_spacing)
        
        # Equipment section title
        equipment_title = QLabel("Equipment Overview")
        title_font = QFont()
        title_font.setPointSize(13)
        title_font.setBold(True)
        equipment_title.setFont(title_font)
        equipment_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        equipment_title.setStyleSheet("color: #2c3e50; padding: 5px;")
        equipment_layout.addWidget(equipment_title)
        
        # Create equipment tiles (5 tiles in vertical stack) with navigation capability
        self.total_equipment_tile = ModernDashboardTile("🏠", "Total Equipment", 0, "neutral", "equipment")
        self.maintenance_overdue_tile = ModernDashboardTile("⚠️", "Maintenance Overdue", 0, "critical", "maintenance")
        self.tyre_replacement_tile = ModernDashboardTile("🛞", "Tyre Rotation Due", 0, "warning", "conditioning")
        self.battery_replacement_tile = ModernDashboardTile("🔋", "Battery Replacement Due", 0, "warning", "battery")
        self.overhaul_due_tile = ModernDashboardTile("🔧", "Overhaul Overdue", 0, "critical", "overhaul")

        # Connect tile click signals
        self.total_equipment_tile.clicked.connect(self.handle_tile_clicked)
        self.maintenance_overdue_tile.clicked.connect(self.handle_tile_clicked)
        self.tyre_replacement_tile.clicked.connect(self.handle_tile_clicked)
        self.battery_replacement_tile.clicked.connect(self.handle_tile_clicked)
        self.overhaul_due_tile.clicked.connect(self.handle_tile_clicked)
        
        equipment_layout.addWidget(self.total_equipment_tile)
        equipment_layout.addWidget(self.maintenance_overdue_tile)
        equipment_layout.addWidget(self.tyre_replacement_tile)
        equipment_layout.addWidget(self.battery_replacement_tile)
        equipment_layout.addWidget(self.overhaul_due_tile)
        equipment_layout.addStretch()  # Push tiles to top
        
        main_layout.addWidget(equipment_frame)  # No stretch factor - fixed width
        
        # ===============================
        # MIDDLE COLUMN: Statistics (EXPANDED TO FILL REMAINING SPACE)
        # ===============================
        stats_frame = QFrame()
        # No fixed width - will expand to fill available space
        stats_frame.setFrameShape(QFrame.Shape.StyledPanel)
        stats_frame.setFrameShadow(QFrame.Shadow.Raised)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        stats_layout = QVBoxLayout(stats_frame)
        # Use optimized spacing for expanded statistics
        margin = 15
        spacing = 10
        stats_layout.setContentsMargins(margin, margin, margin, margin)
        stats_layout.setSpacing(spacing)
        
        # Statistics section title
        stats_title = QLabel("Statistics")
        stats_title.setFont(title_font)
        stats_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        stats_title.setStyleSheet("color: #2c3e50; padding: 5px;")
        stats_layout.addWidget(stats_title)
        
        # Create 2x2 grid for charts - IMPROVED with better spacing and alignment
        charts_grid = QGridLayout()
        charts_grid.setSpacing(25)  # Increased spacing between charts to prevent overlap and text cutoff
        charts_grid.setContentsMargins(10, 10, 10, 10)  # Increased margins for better positioning

        # Set column stretches for balanced sizing and different row stretches for different chart types
        charts_grid.setColumnStretch(0, 1)
        charts_grid.setColumnStretch(1, 1)
        charts_grid.setRowStretch(0, 3)  # Larger stretch for pie charts (top row)
        charts_grid.setRowStretch(1, 2)  # Smaller stretch for bar charts (bottom row)

        # Top row: TM Maintenance Chart (left) and Battery Age Distribution Chart (right) - LARGER PIE CHARTS
        self.tm_chart = TMMaintenanceChart()
        self.tm_chart.setMaximumHeight(480)  # Further increased height for longer labels
        self.battery_age_chart = BatteryAgeDistributionChart()
        self.battery_age_chart.setMaximumHeight(400)  # Increased height for larger pie charts
        charts_grid.addWidget(self.tm_chart, 0, 0)
        charts_grid.addWidget(self.battery_age_chart, 0, 1)

        # Optimized vertical spacing - larger for pie charts, smaller for bar charts
        charts_grid.setRowMinimumHeight(0, 480)  # Further increased minimum height for pie chart row with longer labels
        charts_grid.setRowMinimumHeight(1, 320)  # Smaller minimum height for bar chart row

        # Bottom row: Tyre Demand Forecast Chart (left) and Fluids Demand Chart (right) - SHIFTED LOWER
        self.tyre_chart = TyreDemandForecastChart()
        self.tyre_chart.setMaximumHeight(320)  # Smaller height for bar charts
        
        self.fluids_chart = FluidDemandChart()
        self.fluids_chart.setMaximumHeight(320)  # Smaller height for bar charts

        charts_grid.addWidget(self.tyre_chart, 1, 0)
        charts_grid.addWidget(self.fluids_chart, 1, 1)
        
        # Add the grid to the stats layout
        stats_layout.addLayout(charts_grid)
        stats_layout.addStretch()
        main_layout.addWidget(stats_frame, 1)  # Stretch factor 1 - expands to fill space
        
        # ===============================
        # RIGHT COLUMN: Critical Alerts (RIGHTMOST EDGE)
        # ===============================
        alerts_frame = QFrame()
        # Fixed width for alerts frame to maintain consistency
        if layout_config['use_compact_tiles']:
            alerts_frame.setFixedWidth(320)  # Fixed width for consistent layout
        else:
            alerts_frame.setFixedWidth(340)  # Fixed width for consistent layout
        alerts_frame.setFrameShape(QFrame.Shape.StyledPanel)
        alerts_frame.setFrameShadow(QFrame.Shadow.Raised)
        alerts_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        alerts_layout = QVBoxLayout(alerts_frame)
        # Use compact margins and spacing for compressed alerts
        alert_margin = 10
        alert_spacing = 6
        alerts_layout.setContentsMargins(alert_margin, alert_margin, alert_margin, alert_margin)
        alerts_layout.setSpacing(alert_spacing)
        
        # Alerts section title
        alerts_title = QLabel("Critical Alerts")
        alerts_title.setFont(title_font)
        alerts_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        alerts_title.setStyleSheet("color: #2c3e50; padding: 5px;")
        alerts_layout.addWidget(alerts_title)
        
        # Create scroll area for alerts
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                border: 1px solid #dee2e6;
                background: #f8f9fa;
                width: 10px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical {
                background: #6c757d;
                border-radius: 5px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #495057;
            }
        """)
        
        # Create alerts container with vertical layout (single column for compressed space)
        self.alerts_container = QWidget()
        self.alerts_layout = QVBoxLayout(self.alerts_container)  # Changed to QVBoxLayout for single column
        self.alerts_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.alerts_layout.setSpacing(6)  # Compact spacing between alerts
        self.alerts_layout.setContentsMargins(5, 5, 5, 5)
        self.alert_count = 0  # Track alert position
        scroll_area.setWidget(self.alerts_container)
        
        alerts_layout.addWidget(scroll_area)
        main_layout.addWidget(alerts_frame)

        # Set responsive minimum window size based on screen resolution
        if LowResolutionManager.is_low_resolution():
            # For low-res screens, use a smaller minimum width
            self.setMinimumWidth(1000)
        else:
            # For higher-res screens, use the original larger minimum
            self.setMinimumWidth(1200)
    
    def load_data(self):
        """Load dashboard data with performance optimizations."""
        logger.info("Loading horizontal dashboard data")

        try:
            from performance_optimizations import PerformanceContext, UIOptimizer

            with PerformanceContext("Dashboard data loading"):
                # Defer heavy operations to prevent UI freezing
                operations = [
                    self.update_equipment_metrics,
                    self.update_alerts,
                    self.update_statistics
                ]

                # Execute operations with small delays to keep UI responsive
                UIOptimizer.defer_heavy_operations(self, operations, delay=50)

            logger.info("Horizontal dashboard data loading initiated")
        except Exception as e:
            logger.error(f"Error loading horizontal dashboard data: {e}")
            # Fallback to synchronous loading
            try:
                self.update_equipment_metrics()
                self.update_alerts()
                self.update_statistics()
            except Exception as fallback_error:
                logger.error(f"Fallback loading also failed: {fallback_error}")
    
    def update_equipment_metrics(self):
        """Update equipment-related metrics for all 6 tiles with real database data."""
        try:
            # Get all active equipment with validation
            equipment_list = Equipment.get_all()
            active_equipment = [eq for eq in equipment_list if eq and eq.get('is_active', 0) == 1] if equipment_list else []
            total_equipment = len(active_equipment)
            logger.info(f"Dashboard: Found {total_equipment} active equipment")
            self.total_equipment_tile.update_value(total_equipment, "neutral")
            
            # Maintenance overdue - only show truly OVERDUE maintenance (past due date)
            maintenance_overdue_count = self.get_overdue_maintenance_count()
            logger.info(f"Dashboard: Found {maintenance_overdue_count} overdue maintenance items")
            status = "critical" if maintenance_overdue_count > 0 else "normal"
            self.maintenance_overdue_tile.update_value(maintenance_overdue_count, status)
            
            # Tyre rotation due with validation
            tyre_rotation_count = self.get_tyre_rotation_due_count()
            logger.info(f"Dashboard: Found {tyre_rotation_count} equipment due for tyre rotation")
            status = "warning" if tyre_rotation_count > 0 else "normal"
            self.tyre_replacement_tile.update_value(tyre_rotation_count, status)
            
            # Battery replacement due (equipment > 3 years old) with validation
            battery_replacement_count = self.get_battery_replacement_due_count()
            logger.info(f"Dashboard: Found {battery_replacement_count} equipment due for battery replacement")
            status = "warning" if battery_replacement_count > 0 else "normal"
            self.battery_replacement_tile.update_value(battery_replacement_count, status)
            
            # Overhaul overdue with centralized status calculation
            overhaul_overdue_count = self.get_overhaul_due_count()
            logger.info(f"Dashboard: Found {overhaul_overdue_count} overhauls overdue")
            status = "critical" if overhaul_overdue_count > 0 else "normal"
            self.overhaul_due_tile.update_value(overhaul_overdue_count, status)
            
            logger.info("Dashboard equipment metrics updated successfully with real data")
            
        except Exception as e:
            logger.error(f"Error updating equipment metrics: {e}")
            # Set all tiles to 0 in case of error to show something meaningful
            self.total_equipment_tile.update_value(0, "neutral")
            self.maintenance_overdue_tile.update_value(0, "normal")
            self.tyre_replacement_tile.update_value(0, "normal")
            self.battery_replacement_tile.update_value(0, "normal")
            self.overhaul_due_tile.update_value(0, "normal")
    
    def get_tyre_rotation_due_count(self):
        """Get count of equipment due for tyre rotation using centralized status service."""
        try:
            # Get all tyre maintenance records with equipment data
            query = """
                SELECT tm.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms
                FROM tyre_maintenance tm
                JOIN equipment e ON tm.equipment_id = e.equipment_id
                WHERE e.is_active = 1
                ORDER BY e.make_and_type
            """
            tyre_records = database.execute_query(query)

            if not tyre_records:
                return 0

            rotation_due_count = 0

            for record in tyre_records:
                current_meterage = float(record.get('meterage_kms', 0))
                rotation_kms = float(record.get('tyre_rotation_kms', 0))
                last_rotation_date = record.get('last_rotation_date')

                if rotation_kms > 0:
                    # Use centralized service to calculate rotation status
                    rotation_status = conditioning_status_service.calculate_tyre_rotation_status(
                        current_meterage, last_rotation_date, rotation_kms
                    )

                    # Count warning, critical, and overdue statuses
                    if rotation_status.status_level in ['warning', 'critical', 'overdue']:
                        rotation_due_count += 1

            return rotation_due_count

        except Exception as e:
            logger.error(f"Error getting tyre rotation due count: {e}")
            return 0
    
    def get_battery_replacement_due_count(self):
        """Get count of batteries due for replacement using centralized status service."""
        try:
            # Get all battery records
            query = """
                SELECT b.*, e.make_and_type, e.ba_number
                FROM battery b
                JOIN equipment e ON b.equipment_id = e.equipment_id
                WHERE e.is_active = 1
            """
            batteries = database.execute_query(query)

            if not batteries:
                return 0

            due_count = 0

            for battery in batteries:
                done_date = battery.get('done_date', '')
                custom_life_months = battery.get('custom_life_months')

                # Use centralized service to calculate battery status
                battery_status = conditioning_status_service.calculate_battery_status(
                    done_date, custom_life_months
                )

                # Count warning, critical, and overdue statuses
                if battery_status.status_level in ['warning', 'critical', 'overdue']:
                    due_count += 1

            return due_count

        except Exception as e:
            logger.error(f"Error getting battery replacement due count: {e}")
            return 0
    
    def get_overdue_maintenance_count(self):
        """Get count of OVERDUE maintenance across ALL 4 maintenance sub-tabs (TM-1, TM-2, Yearly, Monthly).

        Only counts maintenance that is past due date (negative days until due),
        not maintenance that is simply due within 7 days (critical).
        """
        try:
            # Count overdue maintenance across all 4 categories using same logic as maintenance widgets
            overdue_count = 0
            maintenance_categories = ['TM-1', 'TM-2', 'Yearly', 'Monthly']
            
            for category in maintenance_categories:
                # Get all maintenance records for this category
                query = """
                    SELECT m.*, e.make_and_type, e.ba_number
                    FROM maintenance m
                    JOIN equipment e ON m.equipment_id = e.equipment_id
                    WHERE m.maintenance_category = %s
                    AND e.is_active = 1
                    AND (m.status != 'archived' OR m.status IS NULL)
                    ORDER BY m.next_due_date
                """
                maintenance_list = database.execute_query(query, (category,))
                
                if maintenance_list:
                    for maintenance in maintenance_list:
                        # Calculate status using same logic as maintenance widget
                        calculated_status = self.calculate_maintenance_status_for_category(maintenance, category)
                        # Count only overdue maintenance (past due date), not critical (due within 7 days)
                        if calculated_status == "overdue":
                            overdue_count += 1

            logger.info(f"Dashboard: Found {overdue_count} overdue maintenance items across all categories")
            return overdue_count
            
        except Exception as e:
            logger.error(f"Error getting overdue maintenance count: {e}")
            return 0

    def calculate_maintenance_status_for_category(self, maintenance, category):
        """
        Calculate maintenance status for a specific category.
        DEPRECATED: Use utils.calculate_maintenance_status() instead.
        This method is kept for backward compatibility but delegates to the centralized function.
        """
        try:
            # Status calculation logic with distinction between manual and imported records
            db_status = maintenance.get('status', '')

            if db_status == 'completed':
                # Manually completed records (via UI Complete button) - always show "completed"
                return 'completed'

            # For imported/legacy records and pending maintenance, determine effective due date
            done_date_val = maintenance.get('done_date')

            if done_date_val:
                # Imported/legacy records with done_date but not manually completed
                # Calculate time-based status for the NEXT maintenance cycle
                next_due_date = utils.calculate_next_due_date(done_date_val, category)
                if next_due_date:
                    maintenance_for_status = {
                        'status': 'scheduled',  # Reset status for next cycle calculation
                        'due_date': next_due_date.isoformat()
                    }
                else:
                    # Fallback to original due_date if calculation fails
                    maintenance_for_status = {
                        'status': maintenance.get('status', ''),
                        'due_date': maintenance.get('next_due_date')
                    }
            else:
                # Pending maintenance records - use original due_date and status for time-based calculation
                maintenance_for_status = maintenance

            # Use the centralized status calculation
            return utils.calculate_maintenance_status(maintenance_for_status)

        except Exception as e:
            logger.error(f"Error calculating maintenance status for category {category}: {e}")
            return "unknown"

    def get_overdue_maintenance_records(self):
        """Get OVERDUE maintenance records across ALL 4 maintenance sub-tabs (TM-1, TM-2, Yearly, Monthly).

        Only returns maintenance that is past due date (negative days until due),
        not maintenance that is simply due within 7 days (critical).
        """
        try:
            # Get overdue maintenance across all 4 categories using same logic as maintenance widgets
            overdue_records = []
            maintenance_categories = ['TM-1', 'TM-2', 'Yearly', 'Monthly']
            
            for category in maintenance_categories:
                # Get all maintenance records for this category
                query = """
                    SELECT m.*, e.make_and_type, e.ba_number
                    FROM maintenance m
                    JOIN equipment e ON m.equipment_id = e.equipment_id
                    WHERE m.maintenance_category = %s
                    AND e.is_active = 1
                    AND (m.status != 'archived' OR m.status IS NULL)
                    ORDER BY m.next_due_date
                """
                maintenance_list = database.execute_query(query, (category,))
                
                if maintenance_list:
                    for maintenance in maintenance_list:
                        # Calculate status using same logic as maintenance widget
                        calculated_status = self.calculate_maintenance_status_for_category(maintenance, category)
                        # Include only overdue maintenance (past due date), not critical (due within 7 days)
                        if calculated_status == "overdue":
                            overdue_records.append(maintenance)

            # Sort by due date (handle None values properly)
            overdue_records.sort(key=lambda x: x.get('next_due_date') or '9999-12-31')
            return overdue_records
            
        except Exception as e:
            logger.error(f"Error getting overdue maintenance records: {e}")
            return []

    def get_overhaul_due_count(self):
        """Get count of overhauls that are overdue using centralized status calculation.

        Aggregates overdue counts from both OH-I (First Overhaul) and OH-II (Second Overhaul) types.
        """
        try:
            overhauls = Overhaul.get_all()
            overdue_count = 0
            oh1_overdue = 0
            oh2_overdue = 0
            other_overdue = 0

            for oh in overhauls:
                equipment_id = oh.get('equipment_id')
                equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
                overhaul_type = oh.get('overhaul_type', 'Unknown')

                # Calculate status using centralized logic
                status = utils.calculate_overhaul_status(
                    oh.get('overhaul_type'),
                    oh.get('due_date'),
                    oh.get('done_date'),
                    equipment.date_of_commission if equipment else None,
                    None,  # oh1_done_date - will be calculated internally if needed
                    None,  # custom_intervals
                    equipment.meterage_kms if equipment else None
                )

                # Count only overdue overhauls (past due date or over meterage limit)
                if status == "overdue":
                    overdue_count += 1
                    # Track by overhaul type for detailed logging
                    if overhaul_type == 'OH-I':
                        oh1_overdue += 1
                    elif overhaul_type == 'OH-II':
                        oh2_overdue += 1
                    else:
                        other_overdue += 1

            # Detailed logging for transparency
            logger.info(f"Dashboard overhaul overdue breakdown: OH-I={oh1_overdue}, OH-II={oh2_overdue}, Other={other_overdue}, Total={overdue_count}")

            return overdue_count
        except Exception as e:
            logger.error(f"Error getting overhaul overdue count: {e}")
            return 0
    
    def update_alerts(self):
        """Update alerts section with responsive alert tiles layout."""
        # Clear existing alerts
        while self.alerts_layout.count():
            item = self.alerts_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        # Reset alert counter for grid positioning
        self.alert_count = 0
        
        # Track added alert IDs to prevent duplicates
        self.added_maintenance_ids = set()
        self.added_overhaul_ids = set()
        self.added_tyre_ids = set()
        self.added_equipment_ids = set()
        logger.info("Initialized alert ID tracking sets")
        
        # Priority Order: Maintenance → Batteries → Tyres
        # Add maintenance alerts (HIGHEST PRIORITY)
        self.add_maintenance_alerts()
        
        # Add battery alerts (SECOND PRIORITY)
        self.add_battery_alerts()
        
        # Add tyre alerts (THIRD PRIORITY) 
        self.add_tyre_alerts()
        
        # Overhaul alerts DISABLED per user request
        # self.add_overhaul_alerts()
        
        # Add "No alerts" message if there are no alerts
        if self.alert_count == 0:
            no_alerts_label = QLabel("✅ No critical alerts at this time.\n\nAll systems operational.")
            no_alerts_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_alerts_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 2px solid #4CAF50;
                    border-radius: 8px;
                    padding: 30px;
                    color: #2e7d32;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)
            # Add to vertical layout
            self.alerts_layout.addWidget(no_alerts_label)
    
    def get_alert_columns(self):
        """Get number of columns for alert layout - always single column for better space management."""
        # Always use single column layout for better space utilization on low-res laptops
        return 1
    
    def add_alert_to_grid(self, alert_widget):
        """Add an alert widget to the single column vertical layout."""
        # Add to the vertical layout (no grid positioning needed)
        self.alerts_layout.addWidget(alert_widget)
        self.alert_count += 1
    
    def resizeEvent(self, event):
        """Handle window resize to update alert layout."""
        super().resizeEvent(event)
        # Refresh alerts layout when window is resized
        if hasattr(self, 'alerts_container'):
            self.update_alerts()
    
    def add_maintenance_alerts(self):
        """Add maintenance-related alerts - only truly OVERDUE maintenance (past due date)."""
        logger.info("Adding critical maintenance alerts...")
        
        # Get ALL maintenance records and calculate status dynamically (same as UI logic)
        maintenance_records = self.get_overdue_maintenance_records()
        logger.info(f"Found {len(maintenance_records) if maintenance_records else 0} maintenance records to evaluate")
        
        if maintenance_records:
            for maintenance in maintenance_records:
                maintenance_id = maintenance['maintenance_id']
                
                # Skip if already added
                if maintenance_id in self.added_maintenance_ids:
                    continue
                
                # Calculate actual status using same logic as maintenance widget
                calculated_status = self.calculate_maintenance_status_for_alerts(maintenance)
                
                # Only show alerts for OVERDUE maintenance (past due date)
                if calculated_status == "overdue":
                    # Calculate next due date and days for proper alert message
                    next_due_date, days_until_due = self.get_next_due_info(maintenance)

                    if next_due_date:
                        # Overdue maintenance (days_until_due should be negative)
                        alert_title = f"Overdue Maintenance: {utils.format_equipment_display(maintenance)}"
                        alert_message = f"{maintenance['maintenance_type']} maintenance is overdue by {abs(days_until_due)} days."
                        
                        alert = HorizontalAlertTile(
                            alert_title,
                            alert_message,
                            "maintenance",
                            maintenance['maintenance_id'],
                            "critical"
                        )
                        alert.clicked.connect(self.handle_alert_clicked)
                        self.add_alert_to_grid(alert)
                        self.added_maintenance_ids.add(maintenance_id)
    
    def calculate_maintenance_status_for_alerts(self, maintenance):
        """Calculate maintenance status using same logic as maintenance widget."""
        try:
            # Use the same logic as calculate_maintenance_status_for_category
            category = maintenance.get('maintenance_category', '')
            return self.calculate_maintenance_status_for_category(maintenance, category)
        except Exception as e:
            logger.error(f"Error calculating maintenance status for alerts: {e}")
            return "unknown"
    
    def get_next_due_info(self, maintenance):
        """Get next due date and days until due for alert messages."""
        try:
            from datetime import timedelta
            
            # Get maintenance category and done date
            category = maintenance.get('maintenance_category', '')
            done_date_val = maintenance.get('done_date')
            
            # Calculate Next Due based on Done Date and category interval
            next_due_date = None
            if done_date_val:
                # Parse done date if it's a string
                if isinstance(done_date_val, str):
                    try:
                        done_date_parsed = datetime.strptime(done_date_val, '%Y-%m-%d').date()
                    except Exception:
                        done_date_parsed = None
                else:
                    done_date_parsed = done_date_val
                
                if done_date_parsed:
                    # Calculate next due based on maintenance category
                    if category == 'TM-1':
                        # TM-1 is every 6 months
                        next_due_date = done_date_parsed + timedelta(days=180)  # 6 months ≈ 180 days
                    elif category == 'TM-2':
                        # TM-2 is every 12 months  
                        next_due_date = done_date_parsed + timedelta(days=365)  # 12 months ≈ 365 days
                    else:
                        # For other categories, use due_date from record
                        due_date_val = maintenance.get('due_date')
                        if isinstance(due_date_val, str):
                            try:
                                next_due_date = datetime.strptime(due_date_val, '%Y-%m-%d').date()
                            except Exception:
                                next_due_date = None
                        else:
                            next_due_date = due_date_val
            else:
                # No done date, use original due_date as next due
                due_date_val = maintenance.get('due_date')
                if isinstance(due_date_val, str):
                    try:
                        next_due_date = datetime.strptime(due_date_val, '%Y-%m-%d').date()
                    except Exception:
                        next_due_date = None
                else:
                    next_due_date = due_date_val

            # Calculate days until due
            days_until_due = 0
            if next_due_date:
                today = date.today()
                days_until_due = (next_due_date - today).days
            
            return next_due_date, days_until_due
                
        except Exception as e:
            logger.error(f"Error getting next due info: {e}")
            return None, 0
    
    def add_overhaul_alerts(self):
        """Add overhaul-related alerts (prevents duplicates)."""
        overhauls = Overhaul.get_all()
        today = date.today()
        logger.info(f"Found {len(overhauls) if overhauls else 0} overhaul records")
        
        for oh in overhauls:
            overhaul_id = oh.get('overhaul_id')
            
            # Skip if already added
            if overhaul_id in self.added_overhaul_ids:
                continue
                
            oh_date = oh.get('due_date')
            done_date = oh.get('done_date')
            
            # Skip if overhaul is already completed
            if done_date and done_date not in ['None', 'No', '', None]:
                continue
                
            if oh_date:
                if isinstance(oh_date, str):
                    try:
                        oh_date_dt = datetime.fromisoformat(oh_date).date()
                    except ValueError:
                        try:
                            oh_date_dt = datetime.strptime(oh_date, "%Y-%m-%d").date()
                        except Exception:
                            continue
                else:
                    oh_date_dt = oh_date
                days_overdue = (today - oh_date_dt).days
                if oh_date_dt <= today:
                    status = "critical" if days_overdue > 0 else "warning"
                    overhaul_type = oh.get('overhaul_type', 'N/A')
                    alert = HorizontalAlertTile(
                        f"{overhaul_type} Overhaul Due: {utils.format_equipment_display(oh)}",
                        f"{overhaul_type} overhaul was due on {oh_date_dt.strftime('%Y-%m-%d')} ({abs(days_overdue)} days {'overdue' if days_overdue > 0 else 'due today'}).",
                        "overhaul",
                        overhaul_id,
                        status
                    )
                    alert.clicked.connect(self.handle_alert_clicked)
                    self.add_alert_to_grid(alert)
                    self.added_overhaul_ids.add(overhaul_id)
    
    def add_tyre_alerts(self):
        """Add tyre maintenance-related alerts using centralized status service."""
        tyre_rotation_due = TyreMaintenance.get_due_for_rotation()
        logger.info(f"Found {len(tyre_rotation_due) if tyre_rotation_due else 0} tyre rotation due records")

        if tyre_rotation_due:
            for tyre in tyre_rotation_due:
                tyre_id = tyre['tyre_maintenance_id']

                # Skip if already added
                if tyre_id in self.added_tyre_ids:
                    continue

                current_meterage = float(tyre['meterage_kms'] or 0)
                rotation_interval = float(tyre['tyre_rotation_kms'] or 0)
                last_rotation_date = tyre.get('last_rotation_date')

                # Use centralized service for dashboard alerts with dashboard thresholds
                should_alert, alert_level = conditioning_status_service.get_dashboard_alert_status(
                    current_meterage, rotation_interval, use_dashboard_thresholds=True
                )

                if should_alert:
                    # Get remaining KMs for display
                    kms_since_rotation = conditioning_status_service.get_kms_since_last_rotation(
                        current_meterage, last_rotation_date, None, rotation_interval
                    )

                    # Create appropriate alert based on status
                    tyre_type = tyre.get('tyre_type', 'Standard')
                    equipment_display = utils.format_equipment_display(tyre)

                    if alert_level == "critical":
                        alert = HorizontalAlertTile(
                            f"{tyre_type} Tyre Rotation Due: {equipment_display}",
                            f"Tyre rotation required. Current: {current_meterage:.0f} km, KMs since rotation: {kms_since_rotation:.0f}.",
                            "tyre",
                            tyre_id,
                            "critical"
                        )
                    else:  # warning
                        alert = HorizontalAlertTile(
                            f"{tyre_type} Rotation Warning: {equipment_display}",
                            f"Tyre rotation needed soon. Current: {current_meterage:.0f} km, KMs since rotation: {kms_since_rotation:.0f}.",
                            "tyre",
                            tyre_id,
                            "warning"
                        )

                    alert.clicked.connect(self.handle_alert_clicked)
                    self.add_alert_to_grid(alert)
                    self.added_tyre_ids.add(tyre_id)
    
    def add_discard_alerts(self):
        """Add discard criteria-related alerts (prevents duplicates)."""
        discard_due = DiscardCriteria.get_equipment_due_for_discard()
        if discard_due:
            for criteria in discard_due:
                discard_id = criteria['discard_criteria_id']
                
                # Skip if already added
                if discard_id in self.added_discard_ids:
                    continue
                
                status = "warning"
                message = ""
                
                # Check if criteria is met by years or kilometers
                if criteria['criteria_years'] > 0 and criteria['vintage_years'] >= criteria['criteria_years']:
                    years_over = criteria['vintage_years'] - criteria['criteria_years']
                    message += f"Age exceeds discard criteria by {years_over:.1f} years. "
                    status = "critical" if years_over > 2 else "warning"
                
                if criteria['criteria_kms'] > 0 and criteria['meterage_kms'] >= criteria['criteria_kms']:
                    kms_over = criteria['meterage_kms'] - criteria['criteria_kms']
                    message += f"Meterage exceeds discard criteria by {kms_over:.0f} km."
                    status = "critical" if kms_over > 1000 else "warning"
                
                alert = HorizontalAlertTile(
                    f"Discard Due: {utils.format_equipment_display(criteria)} ({criteria['component']})",
                    message,
                    "discard",
                    discard_id,
                    status
                )
                alert.clicked.connect(self.handle_alert_clicked)
                self.add_alert_to_grid(alert)
                self.added_discard_ids.add(discard_id)
    
    def add_battery_alerts(self):
        """Add battery replacement alerts using centralized status service."""
        try:
            # Get all battery records
            query = """
                SELECT b.*, e.make_and_type, e.ba_number, e.equipment_id
                FROM battery b
                JOIN equipment e ON b.equipment_id = e.equipment_id
                WHERE e.is_active = 1
                ORDER BY b.done_date
                LIMIT 15
            """
            batteries = database.execute_query(query)

            if not batteries:
                return

            for battery in batteries:
                equipment_id = battery['equipment_id']

                # Skip if already added
                if equipment_id in self.added_equipment_ids:
                    continue

                done_date = battery.get('done_date', '')
                custom_life_months = battery.get('custom_life_months')

                # Use centralized service to calculate battery status
                battery_status = conditioning_status_service.calculate_battery_status(
                    done_date, custom_life_months
                )

                # Only create alerts for warning, critical, or overdue statuses
                if battery_status.status_level in ['warning', 'critical', 'overdue']:
                    equipment_display = battery['ba_number'] or battery['make_and_type']

                    # Calculate age for display
                    age_months = 0
                    if done_date:
                        try:
                            from datetime import datetime
                            done_dt = datetime.strptime(str(done_date), '%Y-%m-%d')
                            today = datetime.now()
                            age_months = int((today - done_dt).days / 30.44)
                        except ValueError:
                            pass

                    # Create alert with appropriate status
                    alert = HorizontalAlertTile(
                        f"Battery {str(battery_status)}: {equipment_display}",
                        f"Battery is {age_months} months old, {str(battery_status).lower()}.",
                        "battery",
                        equipment_id,
                        battery_status.status_level
                    )
                    alert.clicked.connect(self.handle_alert_clicked)
                    self.add_alert_to_grid(alert)
                    self.added_equipment_ids.add(equipment_id)
                    
        except Exception as e:
            logger.error(f"Error adding battery alerts: {e}")
    
    def update_statistics(self):
        """Update statistics charts."""
        logger.info("Updating horizontal dashboard statistics")
        try:
            # Update all charts
            self.tm_chart.update_chart()
            self.battery_age_chart.update_chart()  # NEW: Battery Age Distribution Chart
            self.tyre_chart.update_chart()
            self.fluids_chart.update_chart()

            logger.info("Statistics charts updated successfully")
        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
    
    def handle_alert_clicked(self, alert_type, item_id):
        """Handle alert click event with enhanced navigation."""
        try:
            # Find the main window by traversing up the widget hierarchy
            main_window = self.get_main_window()
            if not main_window:
                logger.warning("Main window not found for navigation")
                return

            logger.info(f"Alert clicked: type={alert_type}, id={item_id}")

            # Navigate based on alert type with equipment selection
            if alert_type == "maintenance":
                self.navigate_to_maintenance(item_id, main_window)
            elif alert_type == "overhaul":
                self.navigate_to_overhaul(item_id, main_window)
            elif alert_type == "tyre":
                self.navigate_to_conditioning(item_id, main_window)
            elif alert_type == "battery":
                self.navigate_to_battery_conditioning(item_id, main_window)
            elif alert_type == "discard":
                self.navigate_to_discard_criteria(item_id, main_window)
            elif alert_type == "equipment":
                self.navigate_to_equipment(item_id, main_window)
            else:
                logger.warning(f"Unknown alert type: {alert_type}")

        except Exception as e:
            logger.error(f"Error handling alert click: {e}")

    def get_main_window(self):
        """Find the main window by traversing up the widget hierarchy."""
        try:
            widget = self
            while widget is not None:
                # Check if this widget has the tab_widget attribute (main window)
                if hasattr(widget, 'tab_widget') and hasattr(widget, 'equipment_widget'):
                    return widget
                # Check if this is a QMainWindow with the expected structure
                if hasattr(widget, '__class__') and 'MainWindow' in widget.__class__.__name__:
                    if hasattr(widget, 'tab_widget'):
                        return widget
                widget = widget.parent()

            logger.warning("Could not find main window in widget hierarchy")
            return None

        except Exception as e:
            logger.error(f"Error finding main window: {e}")
            return None

    def navigate_to_equipment(self, equipment_id, main_window):
        """Navigate to equipment tab and select specific equipment."""
        try:
            # Switch to Equipment tab (index 1)
            main_window.tab_widget.setCurrentIndex(1)

            # Get the equipment widget
            equipment_widget = main_window.equipment_widget
            if not equipment_widget:
                logger.error("Equipment widget not found")
                return

            # Use database query to get equipment data instead of model method
            try:
                with database.get_db_connection() as conn:
                    equipment = conn.execute(
                        'SELECT * FROM equipment WHERE equipment_id = ?',
                        (equipment_id,)
                    ).fetchone()

                if equipment:
                    ba_number = equipment.get('ba_number')
                    if ba_number:
                        # Filter by BA number to quickly locate the equipment
                        self.filter_and_select_equipment(equipment_widget, str(ba_number), equipment_id)
                    else:
                        # If no BA number, use make and type
                        make_type = equipment.get('make_and_type')
                        if make_type:
                            self.filter_and_select_equipment(equipment_widget, make_type, equipment_id, use_make_type=True)

                    logger.info(f"Navigated to equipment {equipment_id}")
                else:
                    logger.warning(f"Equipment {equipment_id} not found")

            except Exception as db_error:
                logger.error(f"Database error getting equipment {equipment_id}: {db_error}")

        except Exception as e:
            logger.error(f"Error navigating to equipment: {e}")

    def navigate_to_maintenance(self, maintenance_id, main_window):
        """Navigate to maintenance tab and select specific maintenance record."""
        try:
            # Switch to Maintenance tab (index 3)
            main_window.tab_widget.setCurrentIndex(3)

            # Get the maintenance widget
            maintenance_widget = main_window.maintenance_widget
            if not maintenance_widget:
                logger.error("Maintenance widget not found")
                return

            # Get maintenance record using database query
            try:
                with database.get_db_connection() as conn:
                    maintenance = conn.execute(
                        'SELECT * FROM maintenance WHERE maintenance_id = ?',
                        (maintenance_id,)
                    ).fetchone()

                if maintenance:
                    # Switch to appropriate maintenance category
                    category = maintenance.get('maintenance_category', 'TM-1')
                    if hasattr(maintenance_widget, 'switch_to_category'):
                        maintenance_widget.switch_to_category(category)

                    # Get equipment for filtering
                    equipment_id = maintenance.get('equipment_id')
                    if equipment_id:
                        equipment = conn.execute(
                            'SELECT * FROM equipment WHERE equipment_id = ?',
                            (equipment_id,)
                        ).fetchone()

                        if equipment and equipment.get('ba_number'):
                            # Filter by BA number in maintenance widget
                            self.filter_maintenance_by_ba(maintenance_widget, str(equipment.get('ba_number')), maintenance_id)

                    logger.info(f"Navigated to maintenance {maintenance_id} in category {category}")
                else:
                    logger.warning(f"Maintenance {maintenance_id} not found")

            except Exception as db_error:
                logger.error(f"Database error getting maintenance {maintenance_id}: {db_error}")

        except Exception as e:
            logger.error(f"Error navigating to maintenance: {e}")

    def navigate_to_overhaul(self, overhaul_id, main_window):
        """Navigate to overhaul tab and select specific overhaul record."""
        try:
            # Switch to Overhaul tab (index 4)
            main_window.tab_widget.setCurrentIndex(4)

            # Get the overhaul widget
            overhaul_widget = main_window.overhaul_widget
            if not overhaul_widget:
                logger.error("Overhaul widget not found")
                return

            # Get overhaul record using database query
            try:
                with database.get_db_connection() as conn:
                    overhaul = conn.execute(
                        'SELECT * FROM overhauls WHERE overhaul_id = ?',
                        (overhaul_id,)
                    ).fetchone()

                if overhaul:
                    # Switch to appropriate overhaul type tab
                    overhaul_type = overhaul.get('overhaul_type', 'OH-I')
                    if hasattr(overhaul_widget, 'tab_widget'):
                        if overhaul_type == 'OH-I':
                            overhaul_widget.tab_widget.setCurrentIndex(0)
                        elif overhaul_type == 'OH-II':
                            overhaul_widget.tab_widget.setCurrentIndex(1)

                    # Get equipment for filtering
                    equipment_id = overhaul.get('equipment_id')
                    if equipment_id:
                        equipment = conn.execute(
                            'SELECT * FROM equipment WHERE equipment_id = ?',
                            (equipment_id,)
                        ).fetchone()

                        if equipment and equipment.get('ba_number'):
                            # Filter by BA number in overhaul widget
                            self.filter_overhaul_by_ba(overhaul_widget, str(equipment.get('ba_number')), overhaul_id)

                    logger.info(f"Navigated to overhaul {overhaul_id} type {overhaul_type}")
                else:
                    logger.warning(f"Overhaul {overhaul_id} not found")

            except Exception as db_error:
                logger.error(f"Database error getting overhaul {overhaul_id}: {db_error}")

        except Exception as e:
            logger.error(f"Error navigating to overhaul: {e}")

    def navigate_to_conditioning(self, tyre_id, main_window):
        """Navigate to conditioning tab and select specific tyre maintenance record."""
        try:
            # Switch to Conditioning tab (index 6)
            main_window.tab_widget.setCurrentIndex(6)

            # Get the conditioning widget
            conditioning_widget = main_window.tyre_maintenance_widget
            if not conditioning_widget:
                logger.error("Conditioning widget not found")
                return

            # Get tyre maintenance record using database query
            try:
                with database.get_db_connection() as conn:
                    tyre = conn.execute(
                        'SELECT * FROM tyre_maintenance WHERE tyre_id = ?',
                        (tyre_id,)
                    ).fetchone()

                if tyre:
                    equipment_id = tyre.get('equipment_id')
                    if equipment_id:
                        equipment = conn.execute(
                            'SELECT * FROM equipment WHERE equipment_id = ?',
                            (equipment_id,)
                        ).fetchone()

                        if equipment and equipment.get('ba_number'):
                            # Filter by BA number in conditioning widget
                            self.filter_conditioning_by_ba(conditioning_widget, str(equipment.get('ba_number')), tyre_id)

                    logger.info(f"Navigated to conditioning {tyre_id}")
                else:
                    logger.warning(f"Tyre maintenance {tyre_id} not found")

            except Exception as db_error:
                logger.error(f"Database error getting tyre maintenance {tyre_id}: {db_error}")

        except Exception as e:
            logger.error(f"Error navigating to conditioning: {e}")

    def navigate_to_battery_conditioning(self, equipment_id, main_window):
        """Navigate to conditioning tab's battery subtab and select specific equipment."""
        try:
            # Switch to Conditioning tab (index 6)
            main_window.tab_widget.setCurrentIndex(6)

            # Get the conditioning widget  
            conditioning_widget = main_window.tyre_maintenance_widget
            if not conditioning_widget:
                logger.error("Conditioning widget not found")
                return

            # Switch to battery subtab (index 1, assuming tyre is 0 and battery is 1)
            if hasattr(conditioning_widget, 'tab_widget'):
                conditioning_widget.tab_widget.setCurrentIndex(1)  # Battery tab
                
            # Get equipment data for filtering
            try:
                with database.get_db_connection() as conn:
                    equipment = conn.execute(
                        'SELECT * FROM equipment WHERE equipment_id = ?',
                        (equipment_id,)
                    ).fetchone()

                if equipment:
                    ba_number = equipment.get('ba_number')
                    if ba_number and hasattr(conditioning_widget, 'battery_search_edit'):
                        # Filter by BA number in battery search
                        conditioning_widget.battery_search_edit.setText(str(ba_number))
                        
                        # Trigger filtering
                        if hasattr(conditioning_widget, 'filter_battery'):
                            conditioning_widget.filter_battery()
                        
                        # Try to select the battery record
                        QTimer.singleShot(200, lambda: self.select_battery_by_equipment_id(conditioning_widget, equipment_id))

                    logger.info(f"Navigated to battery conditioning for equipment {equipment_id}")
                else:
                    logger.warning(f"Equipment {equipment_id} not found")

            except Exception as db_error:
                logger.error(f"Database error getting equipment {equipment_id}: {db_error}")

        except Exception as e:
            logger.error(f"Error navigating to battery conditioning: {e}")

    def select_battery_by_equipment_id(self, conditioning_widget, equipment_id):
        """Select battery record by equipment ID."""
        try:
            if hasattr(conditioning_widget, 'battery_table'):
                battery_table = conditioning_widget.battery_table
                
                # Look for the equipment in the battery table
                for row in range(battery_table.rowCount()):
                    equipment_item = battery_table.item(row, 0)  # Equipment column
                    if equipment_item:
                        stored_equipment_id = equipment_item.data(Qt.UserRole + 1)  # equipment_id stored in UserRole + 1
                        if stored_equipment_id == equipment_id:
                            battery_table.selectRow(row)
                            battery_table.scrollToItem(equipment_item)
                            
                            # Trigger selection handler if it exists
                            if hasattr(conditioning_widget, 'battery_selected'):
                                conditioning_widget.battery_selected(row)
                            
                            logger.info(f"Selected battery for equipment {equipment_id}")
                            return
                            
                logger.warning(f"Battery record for equipment {equipment_id} not found in table")
                
        except Exception as e:
            logger.error(f"Error selecting battery by equipment ID: {e}")

    def navigate_to_discard_criteria(self, criteria_id, main_window):
        """Navigate to discard criteria tab and select specific criteria record."""
        try:
            # Switch to Discard Criteria tab (index 5)
            main_window.tab_widget.setCurrentIndex(5)

            # Get the discard criteria widget
            discard_widget = main_window.discard_criteria_widget
            if not discard_widget:
                logger.error("Discard criteria widget not found")
                return

            # Get discard criteria record using database query
            try:
                with database.get_db_connection() as conn:
                    criteria = conn.execute(
                        'SELECT * FROM discard_criteria WHERE criteria_id = ?',
                        (criteria_id,)
                    ).fetchone()

                if criteria:
                    equipment_id = criteria.get('equipment_id')
                    if equipment_id:
                        equipment = conn.execute(
                            'SELECT * FROM equipment WHERE equipment_id = ?',
                            (equipment_id,)
                        ).fetchone()

                        if equipment and equipment.get('ba_number'):
                            # Filter by BA number in discard criteria widget
                            self.filter_discard_by_ba(discard_widget, str(equipment.get('ba_number')), criteria_id)

                    logger.info(f"Navigated to discard criteria {criteria_id}")
                else:
                    logger.warning(f"Discard criteria {criteria_id} not found")

            except Exception as db_error:
                logger.error(f"Database error getting discard criteria {criteria_id}: {db_error}")

        except Exception as e:
            logger.error(f"Error navigating to discard criteria: {e}")

    def filter_and_select_equipment(self, equipment_widget, filter_value, equipment_id, use_make_type=False):
        """Filter equipment table and select specific equipment."""
        try:
            # Get the equipment table
            equipment_table = equipment_widget.equipment_table
            if not equipment_table:
                logger.error("Equipment table not found")
                return

            # Apply filter based on type
            if use_make_type:
                # Filter by Make & Type
                if hasattr(equipment_table, 'make_type_filter'):
                    equipment_table.make_type_filter.setCurrentText(filter_value)
            else:
                # Filter by BA Number
                if hasattr(equipment_table, 'ba_filter'):
                    if equipment_table.ba_filter.isEditable():
                        equipment_table.ba_filter.lineEdit().setText(filter_value)
                    else:
                        equipment_table.ba_filter.setCurrentText(filter_value)

            # Apply the filter
            if hasattr(equipment_table, 'apply_filters'):
                equipment_table.apply_filters()

            # Wait a moment for filter to apply
            QTimer.singleShot(100, lambda: self.select_equipment_by_id(equipment_table, equipment_id))

        except Exception as e:
            logger.error(f"Error filtering and selecting equipment: {e}")

    def select_equipment_by_id(self, table, equipment_id):
        """Select equipment by ID in the table."""
        try:
            # Look through current page data for the equipment
            for row in range(table.table.rowCount()):
                item = table.table.item(row, 0)  # ID column (hidden)
                if item and item.text() == str(equipment_id):
                    table.table.selectRow(row)
                    table.table.scrollToItem(item)
                    # Trigger selection event
                    if hasattr(table, 'on_selection_changed'):
                        table.on_selection_changed()
                    logger.info(f"Selected equipment {equipment_id} in table")
                    return

            logger.warning(f"Equipment {equipment_id} not found in current table view")

        except Exception as e:
            logger.error(f"Error selecting equipment by ID: {e}")

    def filter_maintenance_by_ba(self, maintenance_widget, ba_number, maintenance_id):
        """Filter maintenance records by BA number."""
        try:
            # Get current maintenance sub-widget
            current_widget = maintenance_widget.tab_widget.currentWidget()
            if not current_widget:
                logger.error("Current maintenance widget not found")
                return

            # Look for table with BA filtering capability
            if hasattr(current_widget, 'maintenance_table'):
                table = current_widget.maintenance_table
                if hasattr(table, 'ba_filter'):
                    if table.ba_filter.isEditable():
                        table.ba_filter.lineEdit().setText(ba_number)
                    else:
                        table.ba_filter.setCurrentText(ba_number)

                    # Apply filter
                    if hasattr(table, 'apply_filters'):
                        table.apply_filters()

                    # Select specific maintenance record
                    QTimer.singleShot(100, lambda: self.select_record_by_id(table, maintenance_id))

        except Exception as e:
            logger.error(f"Error filtering maintenance by BA: {e}")

    def filter_overhaul_by_ba(self, overhaul_widget, ba_number, overhaul_id):
        """Filter overhaul records by BA number."""
        try:
            # Get current overhaul sub-widget
            current_widget = overhaul_widget.tab_widget.currentWidget()
            if not current_widget:
                logger.error("Current overhaul widget not found")
                return

            # Look for table with BA filtering capability
            if hasattr(current_widget, 'overhaul_table'):
                table = current_widget.overhaul_table
                if hasattr(table, 'ba_filter'):
                    if table.ba_filter.isEditable():
                        table.ba_filter.lineEdit().setText(ba_number)
                    else:
                        table.ba_filter.setCurrentText(ba_number)

                    # Apply filter
                    if hasattr(table, 'apply_filters'):
                        table.apply_filters()

                    # Select specific overhaul record
                    QTimer.singleShot(100, lambda: self.select_record_by_id(table, overhaul_id))

        except Exception as e:
            logger.error(f"Error filtering overhaul by BA: {e}")

    def filter_conditioning_by_ba(self, conditioning_widget, ba_number, tyre_id):
        """Filter conditioning records by BA number."""
        try:
            # Look for conditioning table with BA filtering
            if hasattr(conditioning_widget, 'conditioning_table'):
                table = conditioning_widget.conditioning_table
                if hasattr(table, 'ba_filter'):
                    if table.ba_filter.isEditable():
                        table.ba_filter.lineEdit().setText(ba_number)
                    else:
                        table.ba_filter.setCurrentText(ba_number)

                    # Apply filter
                    if hasattr(table, 'apply_filters'):
                        table.apply_filters()

                    # Select specific tyre record
                    QTimer.singleShot(100, lambda: self.select_record_by_id(table, tyre_id))

        except Exception as e:
            logger.error(f"Error filtering conditioning by BA: {e}")

    def filter_discard_by_ba(self, discard_widget, ba_number, criteria_id):
        """Filter discard criteria by BA number."""
        try:
            # Look for criteria table with BA filtering
            if hasattr(discard_widget, 'criteria_table'):
                table = discard_widget.criteria_table
                if hasattr(table, 'ba_filter'):
                    if table.ba_filter.isEditable():
                        table.ba_filter.lineEdit().setText(ba_number)
                    else:
                        table.ba_filter.setCurrentText(ba_number)

                    # Apply filter
                    if hasattr(table, 'apply_filters'):
                        table.apply_filters()

                    # Select specific criteria record
                    QTimer.singleShot(100, lambda: self.select_record_by_id(table, criteria_id))

        except Exception as e:
            logger.error(f"Error filtering discard criteria by BA: {e}")

    def select_record_by_id(self, table, record_id):
        """Generic method to select a record by ID in any table."""
        try:
            # Handle both PaginatedTableWidget and regular QTableWidget
            if hasattr(table, 'table'):
                # PaginatedTableWidget
                actual_table = table.table
            else:
                # Regular QTableWidget
                actual_table = table

            # Look through table rows for the record
            for row in range(actual_table.rowCount()):
                # Check first column (usually ID) or look for UserRole data
                item = actual_table.item(row, 0)
                if item:
                    # Try text comparison first
                    if item.text() == str(record_id):
                        actual_table.selectRow(row)
                        actual_table.scrollToItem(item)
                        logger.info(f"Selected record {record_id} in table")
                        return

                    # Try UserRole data
                    user_data = item.data(Qt.UserRole)
                    if user_data == record_id:
                        actual_table.selectRow(row)
                        actual_table.scrollToItem(item)
                        logger.info(f"Selected record {record_id} in table (UserRole)")
                        return

            logger.warning(f"Record {record_id} not found in current table view")

        except Exception as e:
            logger.error(f"Error selecting record by ID: {e}")

    def handle_tile_clicked(self, tile_type):
        """Handle dashboard tile click for navigation."""
        try:
            # Find the main window by traversing up the widget hierarchy
            main_window = self.get_main_window()
            if not main_window:
                logger.warning("Main window not found for tile navigation")
                return

            logger.info(f"Dashboard tile clicked: {tile_type}")

            # Navigate to appropriate tab based on tile type (correct indices)
            # Tab order: Dashboard(0), Equipment(1), Fluids(2), Maintenance(3), Overhaul(4), Discard Criteria(5), Conditioning(6), Demand Forecast(7)
            if tile_type == "equipment":
                main_window.tab_widget.setCurrentIndex(1)  # Equipment tab
            elif tile_type == "maintenance":
                main_window.tab_widget.setCurrentIndex(3)  # Maintenance tab
                # Show overdue maintenance by default when coming from dashboard tile
                self.navigate_to_maintenance_overdue(main_window)
            elif tile_type == "overhaul":
                main_window.tab_widget.setCurrentIndex(4)  # Overhaul tab
            elif tile_type == "conditioning":
                main_window.tab_widget.setCurrentIndex(6)  # Conditioning tab
            elif tile_type == "battery":
                main_window.tab_widget.setCurrentIndex(6)  # Conditioning tab
                # Navigate to battery subtab specifically
                self.navigate_to_battery_subtab(main_window)
            elif tile_type == "discard":
                main_window.tab_widget.setCurrentIndex(5)  # Discard Criteria tab
            else:
                logger.warning(f"Unknown tile type: {tile_type}")

        except Exception as e:
            logger.error(f"Error handling tile click: {e}")
    
    def navigate_to_maintenance_overdue(self, main_window):
        """Navigate to maintenance tab and filter for overdue maintenance."""
        try:
            # Get the maintenance widget
            maintenance_widget = main_window.maintenance_widget
            if not maintenance_widget:
                logger.error("Maintenance widget not found")
                return

            # Wait a moment for the tab to load, then filter for overdue maintenance
            QTimer.singleShot(200, lambda: self.filter_maintenance_for_overdue(maintenance_widget))
            
            logger.info("Navigated to maintenance tab - filtering for overdue items")

        except Exception as e:
            logger.error(f"Error navigating to maintenance overdue: {e}")
    
    def filter_maintenance_for_overdue(self, maintenance_widget):
        """Filter maintenance widget to show only overdue items."""
        try:
            # Check if maintenance widget has tab structure (TM-1, TM-2, etc.)
            if hasattr(maintenance_widget, 'tab_widget') and maintenance_widget.tab_widget:
                # Get current maintenance category widget
                current_widget = maintenance_widget.tab_widget.currentWidget()
                if current_widget and hasattr(current_widget, 'load_data'):
                    # Refresh the current category to show latest data
                    current_widget.load_data()
                    
                    # If there's a filter method, use it to show overdue items
                    if hasattr(current_widget, 'filter_overdue_only'):
                        current_widget.filter_overdue_only()
                    elif hasattr(current_widget, 'maintenance_table') and hasattr(current_widget.maintenance_table, 'apply_overdue_filter'):
                        current_widget.maintenance_table.apply_overdue_filter()
                    
                    logger.info("Applied overdue filter to maintenance widget")
            else:
                # Fallback: just refresh the main maintenance widget
                if hasattr(maintenance_widget, 'load_data'):
                    maintenance_widget.load_data()
                    
        except Exception as e:
            logger.error(f"Error filtering maintenance for overdue: {e}")
    
    def navigate_to_battery_subtab(self, main_window):
        """Navigate to conditioning tab and switch to battery subtab."""
        try:
            # Get the conditioning widget
            conditioning_widget = main_window.tyre_maintenance_widget
            if not conditioning_widget:
                logger.error("Conditioning widget not found")
                return

            # Wait a moment for the tab to load, then switch to battery subtab
            QTimer.singleShot(200, lambda: self.switch_to_battery_subtab(conditioning_widget))
            
            logger.info("Navigated to conditioning tab - switching to battery subtab")

        except Exception as e:
            logger.error(f"Error navigating to battery subtab: {e}")
    
    def switch_to_battery_subtab(self, conditioning_widget):
        """Switch to the battery subtab in conditioning widget."""
        try:
            # Check if conditioning widget has tab structure
            if hasattr(conditioning_widget, 'tab_widget') and conditioning_widget.tab_widget:
                # Switch to battery tab (index 1: 0=Tyre, 1=Battery)
                conditioning_widget.tab_widget.setCurrentIndex(1)
                
                # Refresh battery data to show latest information
                if hasattr(conditioning_widget, 'load_battery_data'):
                    conditioning_widget.load_battery_data()
                    
                logger.info("Switched to battery subtab and refreshed data")
            else:
                logger.warning("Conditioning widget does not have expected tab structure")
                
        except Exception as e:
            logger.error(f"Error switching to battery subtab: {e}")