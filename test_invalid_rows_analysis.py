#!/usr/bin/env python3
"""
Quick test script to analyze invalid rows in the Excel import preview system.
This will help identify why 31 rows are being marked as invalid.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.excel_import_preview_dialog import ExcelDataAnalyzer
from robust_excel_importer_working import RobustExcelImporter
import pandas as pd

def analyze_invalid_rows():
    """Analyze the Excel file that's showing invalid rows."""
    
    # Use the same file path from the screenshot
    excel_path = r"C:\Users\<USER>\Downloads\LUB DETAILS OF 162 BR COY JUNE 2025sheet for software final - Copy.xlsx"
    
    if not os.path.exists(excel_path):
        print(f"Excel file not found: {excel_path}")
        print("Please update the path in the script or place the file in the correct location.")
        return
    
    print(f"Analyzing Excel file: {excel_path}")
    print("="*80)
    
    try:
        # Read Excel file to get sheet names
        excel_file = pd.ExcelFile(excel_path)
        sheet_names = excel_file.sheet_names
        print(f"Found {len(sheet_names)} sheets: {sheet_names}")
        
        analyzer = ExcelDataAnalyzer()
        importer = RobustExcelImporter()
        
        total_invalid = 0
        
        for sheet_name in sheet_names:
            print(f"\n--- ANALYZING SHEET: {sheet_name} ---")
            
            # Read the sheet
            df = importer._read_sheet_with_fallback(excel_path, sheet_name)
            if df is None or df.empty:
                print(f"Could not read sheet {sheet_name} or it's empty")
                continue
                
            print(f"Total rows in sheet: {len(df)}")
            
            # Get column mapping
            col_map = importer._map_equipment_columns(df.columns)
            print(f"Column mapping: {col_map}")
            
            valid_count = 0
            invalid_count = 0
            invalid_details = []
            
            # Analyze each row
            for index, row in df.iterrows():
                # Extract equipment data
                equipment_data = importer._extract_equipment_data(row, col_map, sheet_name)
                
                # Apply the same validation logic as the preview
                if equipment_data and equipment_data.get('make_and_type'):
                    if importer._is_valid_equipment_record(equipment_data):
                        valid_count += 1
                    else:
                        invalid_count += 1
                        # Get the reason for invalidity
                        reason = get_invalid_reason(equipment_data, importer)
                        invalid_details.append(f"Row {index + 1}: {reason}")
                else:
                    invalid_count += 1
                    if not equipment_data:
                        invalid_details.append(f"Row {index + 1}: No equipment data extracted")
                    else:
                        invalid_details.append(f"Row {index + 1}: No make_and_type found")
            
            print(f"Valid equipment rows: {valid_count}")
            print(f"Invalid rows: {invalid_count}")
            total_invalid += invalid_count
            
            if invalid_details:
                print("Invalid row details:")
                for detail in invalid_details[:10]:  # Show first 10
                    print(f"  - {detail}")
                if len(invalid_details) > 10:
                    print(f"  ... and {len(invalid_details) - 10} more")
        
        print(f"\n{'='*80}")
        print(f"TOTAL INVALID ROWS ACROSS ALL SHEETS: {total_invalid}")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"Error analyzing Excel file: {e}")
        import traceback
        traceback.print_exc()

def get_invalid_reason(equipment_data, importer):
    """Get the specific reason why a row is invalid."""
    make_and_type = equipment_data.get('make_and_type', '').strip().upper()
    ba_number = equipment_data.get('ba_number', '').strip().upper()
    serial_number = equipment_data.get('serial_number', '').strip().upper()
    
    # Check for invalid indicators
    invalid_indicators = [
        'NOT ASSIGNED', 'ALL DATA ARE CORRECT', 'ALL DATA CORRECT',
        'HEADER', 'TOTAL', 'SUMMARY', 'VALIDATION', 'CHECK', 'VERIFY'
    ]
    
    for indicator in invalid_indicators:
        if indicator in make_and_type:
            return f"Invalid text in make_and_type: '{make_and_type}'"
        if indicator in ba_number:
            return f"Invalid text in ba_number: '{ba_number}'"
        if indicator in serial_number:
            return f"Invalid text in serial_number: '{serial_number}'"
    
    # Check for generic fallback names
    if make_and_type.startswith('EQUIPMENT FROM'):
        meaningful_keywords = [
            'BMP', 'TATRA', 'TRUCK', 'TANK', 'VEHICLE', 'GENERATOR', 
            'JCB', 'DOZER', 'CRANE', 'TRAILER', 'ALS', 'MSS', 'CT'
        ]
        has_meaningful_content = any(keyword in make_and_type for keyword in meaningful_keywords)
        if not has_meaningful_content:
            return f"Generic fallback name: '{make_and_type}'"
    
    # Check for all-zero validation
    meterage = equipment_data.get('meterage_kms', 0)
    vintage = equipment_data.get('vintage_years', 0)
    if meterage == 0 and vintage == 0 and not ba_number:
        return f"All zeros and no BA number: '{make_and_type}'"
    
    return f"Unknown reason: '{make_and_type}'"

if __name__ == "__main__":
    analyze_invalid_rows()
