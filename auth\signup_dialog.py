"""
Enhanced Sign-Up Dialog for PROJECT-ALPHA
Provides modern, professional user registration interface with advanced validation,
visual feedback, and seamless integration with PROJECT-ALPHA design system.
"""

import logging
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QComboBox, QFrame,
                             QMessageBox, QProgressBar, QGroupBox, QTextEdit,
                             QCheckBox, QScrollArea, QWidget, QSizePolicy,
                             QGraphicsDropShadowEffect, QSpacerItem)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QPainter, QFontMetrics
from auth.authentication_service import AuthenticationService
from ui.window_utils import DPIScaler
from ui.common_styles import PRIMARY_BUTTON_STYLE, BUTTON_STYLE, DANGER_BUTTON_STYLE

logger = logging.getLogger(__name__)


class RegistrationWorker(QThread):
    """Worker thread for user registration to prevent UI blocking."""

    registration_complete = pyqtSignal(bool, str, int)  # success, message, user_id
    registration_error = pyqtSignal(str)

    def __init__(self, username, password, full_name, email, role_name, created_by_user_id):
        super().__init__()
        self.username = username
        self.password = password
        self.full_name = full_name
        self.email = email
        self.role_name = role_name
        self.created_by_user_id = created_by_user_id

    def run(self):
        """Perform user registration in background thread."""
        try:
            success, message, user_id = AuthenticationService.register_user(
                self.username, self.password, self.full_name, self.email,
                self.role_name, self.created_by_user_id
            )
            self.registration_complete.emit(success, message, user_id)
        except Exception as e:
            logger.error(f"Registration error: {e}")
            self.registration_error.emit(str(e))


class PINRegistrationWorker(QThread):
    """Worker thread for PIN-based user registration to prevent UI blocking."""

    registration_complete = pyqtSignal(bool, str, int)  # success, message, user_id
    registration_error = pyqtSignal(str)

    def __init__(self, username, pin, role_name, created_by_user_id):
        super().__init__()
        self.username = username
        self.pin = pin
        self.role_name = role_name
        self.created_by_user_id = created_by_user_id

    def run(self):
        """Perform PIN-based user registration in background thread."""
        try:
            success, message, user_id = AuthenticationService.register_user_pin(
                self.username, self.pin, self.role_name, self.created_by_user_id
            )
            self.registration_complete.emit(success, message, user_id)
        except Exception as e:
            logger.error(f"PIN registration error: {e}")
            self.registration_error.emit(str(e))


class SignUpDialog(QDialog):
    """
    Comprehensive user registration dialog with validation and role-based access control.
    Supports both open registration and administrator-managed registration.
    """
    
    # Signals
    registration_successful = pyqtSignal(int)  # user_id
    registration_cancelled = pyqtSignal()
    
    def __init__(self, parent=None, current_user=None, is_admin_mode=False):
        """
        Initialize enhanced sign-up dialog with modern UI/UX.

        Args:
            parent: Parent widget
            current_user: Current authenticated user (None for open registration)
            is_admin_mode: True if called from admin user management interface
        """
        super().__init__(parent)
        self.current_user = current_user
        self.is_admin_mode = is_admin_mode
        self.registration_worker = None
        self.available_roles = []

        # UI state tracking for enhanced features
        self.validation_states = {}
        self.field_animations = {}

        self.setWindowTitle("PROJECT-ALPHA - Create New Account")
        self.setModal(True)
        self.setFixedSize(DPIScaler.scale_size(580), DPIScaler.scale_size(720))
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)

        # Set window icon if available
        self.set_window_icon()

        self.setup_ui()
        self.setup_connections()
        self.apply_enhanced_styles()
        self.load_available_roles()
        self.setup_animations()
        
        # Focus on username field
        self.username_edit.setFocus()
        
        logger.info(f"SignUpDialog initialized - Admin mode: {is_admin_mode}")

    def set_window_icon(self):
        """Set window icon if available."""
        try:
            icon_path = os.path.join("resources", "app_icon.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            logger.debug(f"Could not set window icon: {e}")

    def setup_animations(self):
        """Set up animations for enhanced user experience."""
        # Initialize animation objects for smooth transitions
        self.field_animations = {}

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(DPIScaler.scale_size(15))
        layout.setContentsMargins(DPIScaler.scale_size(20), DPIScaler.scale_size(20),
                                  DPIScaler.scale_size(20), DPIScaler.scale_size(20))
        
        # Create header
        self.create_header(layout)
        
        # Create scroll area for form
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Create form widget
        form_widget = QFrame()
        form_layout = QVBoxLayout(form_widget)
        
        # Create registration form
        self.create_registration_form(form_layout)
        
        # Create password requirements info
        self.create_password_requirements(form_layout)
        
        scroll_area.setWidget(form_widget)
        layout.addWidget(scroll_area)
        
        # Create progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Create buttons
        self.create_buttons(layout)
        
        # Create status label
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)
    
    def create_header(self, layout):
        """Create enhanced dialog header with PROJECT-ALPHA branding."""
        # Main header container with enhanced styling
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(DPIScaler.scale_size(15))
        header_layout.setContentsMargins(DPIScaler.scale_size(30), DPIScaler.scale_size(25),
                                        DPIScaler.scale_size(30), DPIScaler.scale_size(25))

        # Logo and title container
        title_container = QHBoxLayout()
        title_container.setSpacing(DPIScaler.scale_size(15))

        # Try to add logo if available
        logo_added = self.add_logo_to_header(title_container)

        # Title section
        title_section = QVBoxLayout()
        title_section.setSpacing(DPIScaler.scale_size(5))

        # Main title
        title_text = "Create New Account"
        if self.is_admin_mode:
            title_text = "Create User Account"

        title_label = QLabel(title_text)
        title_font = QFont()
        title_font.setPointSize(DPIScaler.scale_font_size(20))
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setObjectName("titleLabel")
        if not logo_added:
            title_label.setAlignment(Qt.AlignCenter)
        title_section.addWidget(title_label)

        # Subtitle with PROJECT-ALPHA branding
        if self.is_admin_mode:
            subtitle_text = "PROJECT-ALPHA • Administrator User Management"
        else:
            subtitle_text = "PROJECT-ALPHA • Equipment Inventory System"

        subtitle_label = QLabel(subtitle_text)
        subtitle_font = QFont()
        subtitle_font.setPointSize(DPIScaler.scale_font_size(12))
        subtitle_font.setWeight(QFont.Medium)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setObjectName("subtitleLabel")
        if not logo_added:
            subtitle_label.setAlignment(Qt.AlignCenter)
        title_section.addWidget(subtitle_label)

        title_container.addLayout(title_section)
        if not logo_added:
            title_container.addStretch()

        header_layout.addLayout(title_container)

        # Description with better styling
        if self.is_admin_mode:
            desc_text = "Create a new user account with appropriate role and permissions."
        else:
            desc_text = "Welcome! Please fill out the form below to create your account and get started."

        desc_label = QLabel(desc_text)
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setObjectName("descriptionLabel")
        header_layout.addWidget(desc_label)

        # Add subtle separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setObjectName("headerSeparator")
        header_layout.addWidget(separator)

        layout.addWidget(header_frame)

    def add_logo_to_header(self, layout):
        """Add PROJECT-ALPHA logo to header if available."""
        try:
            logo_path = os.path.join("resources", "app_icon.svg")
            if not os.path.exists(logo_path):
                logo_path = os.path.join("resources", "app_icon.ico")

            if os.path.exists(logo_path):
                logo_label = QLabel()
                pixmap = QPixmap(logo_path)
                if not pixmap.isNull():
                    # Scale logo appropriately
                    scaled_pixmap = pixmap.scaled(
                        DPIScaler.scale_size(48), DPIScaler.scale_size(48),
                        Qt.KeepAspectRatio, Qt.SmoothTransformation
                    )
                    logo_label.setPixmap(scaled_pixmap)
                    logo_label.setAlignment(Qt.AlignCenter)
                    layout.addWidget(logo_label)
                    return True
        except Exception as e:
            logger.debug(f"Could not load logo: {e}")

        return False

    def create_field_with_indicator(self, field_name):
        """Create a form field with validation indicator."""
        container = QFrame()
        container_layout = QHBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(DPIScaler.scale_size(8))

        # Create the input field
        field = QLineEdit()
        field.setObjectName(f"{field_name}Field")
        container_layout.addWidget(field)

        # Create validation indicator label
        indicator = QLabel()
        indicator.setFixedSize(DPIScaler.scale_size(20), DPIScaler.scale_size(20))
        indicator.setAlignment(Qt.AlignCenter)
        indicator.setObjectName(f"{field_name}Indicator")
        indicator.setVisible(False)  # Initially hidden
        container_layout.addWidget(indicator)

        # Store references for validation updates
        self.validation_states[field_name] = {
            "field": field,
            "indicator": indicator,
            "container": container,
            "valid": None
        }

        return {
            "widget": container,
            "field": field,
            "indicator": indicator
        }

    def create_registration_form(self, layout):
        """Create enhanced registration form with visual validation indicators."""
        form_group = QGroupBox("Account Information")
        form_group.setObjectName("formGroup")
        form_layout = QFormLayout(form_group)
        form_layout.setSpacing(DPIScaler.scale_size(15))
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # Username field with validation indicator
        username_container = self.create_field_with_indicator("username")
        self.username_edit = username_container["field"]
        self.username_edit.setPlaceholderText("Enter unique username (3+ characters)")
        self.username_edit.setMinimumHeight(DPIScaler.scale_size(40))
        self.username_edit.setMaxLength(50)
        self.username_edit.setObjectName("usernameField")
        form_layout.addRow("Username *:", username_container["widget"])

        # Full name field with validation indicator
        fullname_container = self.create_field_with_indicator("full_name")
        self.full_name_edit = fullname_container["field"]
        self.full_name_edit.setPlaceholderText("Enter your full name")
        self.full_name_edit.setMinimumHeight(DPIScaler.scale_size(40))
        self.full_name_edit.setMaxLength(100)
        self.full_name_edit.setObjectName("fullNameField")
        form_layout.addRow("Full Name *:", fullname_container["widget"])

        # Email field with validation indicator (optional)
        email_container = self.create_field_with_indicator("email")
        self.email_edit = email_container["field"]
        self.email_edit.setPlaceholderText("Enter email address (optional)")
        self.email_edit.setMinimumHeight(DPIScaler.scale_size(40))
        self.email_edit.setMaxLength(255)
        self.email_edit.setObjectName("emailField")
        form_layout.addRow("Email:", email_container["widget"])

        # Password field with validation indicator
        password_container = self.create_field_with_indicator("password")
        self.password_edit = password_container["field"]
        self.password_edit.setPlaceholderText("Enter secure password (8+ characters)")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(DPIScaler.scale_size(40))
        self.password_edit.setMaxLength(128)
        self.password_edit.setObjectName("passwordField")
        form_layout.addRow("Password *:", password_container["widget"])

        # Confirm password field with validation indicator
        confirm_password_container = self.create_field_with_indicator("confirm_password")
        self.confirm_password_edit = confirm_password_container["field"]
        self.confirm_password_edit.setPlaceholderText("Confirm your password")
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setMinimumHeight(DPIScaler.scale_size(40))
        self.confirm_password_edit.setMaxLength(128)
        self.confirm_password_edit.setObjectName("confirmPasswordField")
        form_layout.addRow("Confirm Password *:", confirm_password_container["widget"])

        # Role selection with enhanced styling
        role_container = QFrame()
        role_layout = QHBoxLayout(role_container)
        role_layout.setContentsMargins(0, 0, 0, 0)
        role_layout.setSpacing(DPIScaler.scale_size(8))

        self.role_combo = QComboBox()
        self.role_combo.setMinimumHeight(DPIScaler.scale_size(40))
        self.role_combo.setObjectName("roleCombo")
        role_layout.addWidget(self.role_combo)

        # Add role info icon/tooltip space
        role_info = QLabel("ⓘ")
        role_info.setFixedSize(DPIScaler.scale_size(20), DPIScaler.scale_size(20))
        role_info.setAlignment(Qt.AlignCenter)
        role_info.setObjectName("roleInfo")
        role_info.setToolTip("Role determines your access permissions in PROJECT-ALPHA")
        role_layout.addWidget(role_info)

        form_layout.addRow("Role *:", role_container)

        # Show password checkbox with better styling
        checkbox_container = QFrame()
        checkbox_layout = QHBoxLayout(checkbox_container)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)

        self.show_password_checkbox = QCheckBox("Show passwords")
        self.show_password_checkbox.setObjectName("showPasswordCheckbox")
        checkbox_layout.addWidget(self.show_password_checkbox)
        checkbox_layout.addStretch()

        form_layout.addRow("", checkbox_container)

        layout.addWidget(form_group)

    def create_password_requirements(self, layout):
        """Create enhanced password requirements panel with visual indicators."""
        req_group = QGroupBox("Password Requirements")
        req_group.setObjectName("passwordRequirementsGroup")
        req_layout = QVBoxLayout(req_group)
        req_layout.setSpacing(DPIScaler.scale_size(8))

        # Create individual requirement items with checkboxes
        requirements = [
            ("length", "At least 8 characters long"),
            ("uppercase", "Contains uppercase letter (A-Z)"),
            ("lowercase", "Contains lowercase letter (a-z)"),
            ("number", "Contains number (0-9)"),
            ("special", "Contains special character (!@#$%^&*...)"),
            ("common", "Not a common weak password")
        ]

        self.password_requirements = {}

        for req_id, req_text in requirements:
            req_container = QHBoxLayout()
            req_container.setSpacing(DPIScaler.scale_size(8))

            # Requirement indicator (initially neutral)
            indicator = QLabel("○")
            indicator.setFixedSize(DPIScaler.scale_size(16), DPIScaler.scale_size(16))
            indicator.setAlignment(Qt.AlignCenter)
            indicator.setObjectName(f"req_{req_id}_indicator")
            req_container.addWidget(indicator)

            # Requirement text
            req_label = QLabel(req_text)
            req_label.setObjectName(f"req_{req_id}_label")
            req_container.addWidget(req_label)
            req_container.addStretch()

            # Store references for updates
            self.password_requirements[req_id] = {
                "indicator": indicator,
                "label": req_label,
                "met": False
            }

            req_layout.addLayout(req_container)

        # Add collapsible behavior
        self.requirements_visible = True
        self.toggle_requirements_btn = QPushButton("Hide Requirements")
        self.toggle_requirements_btn.setObjectName("toggleRequirementsBtn")
        self.toggle_requirements_btn.clicked.connect(self.toggle_password_requirements)
        req_layout.addWidget(self.toggle_requirements_btn)

        layout.addWidget(req_group)

    def toggle_password_requirements(self):
        """Toggle visibility of password requirements details."""
        self.requirements_visible = not self.requirements_visible

        for req_data in self.password_requirements.values():
            req_data["indicator"].setVisible(self.requirements_visible)
            req_data["label"].setVisible(self.requirements_visible)

        if self.requirements_visible:
            self.toggle_requirements_btn.setText("Hide Requirements")
        else:
            self.toggle_requirements_btn.setText("Show Requirements")

    def create_buttons(self, layout):
        """Create dialog buttons."""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(DPIScaler.scale_size(10))

        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.cancel_button.setMinimumWidth(DPIScaler.scale_size(100))
        button_layout.addWidget(self.cancel_button)

        button_layout.addStretch()

        # Create account button
        self.create_button = QPushButton("Create Account")
        self.create_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.create_button.setMinimumWidth(DPIScaler.scale_size(120))
        self.create_button.setDefault(True)
        button_layout.addWidget(self.create_button)

        layout.addLayout(button_layout)

    def setup_connections(self):
        """Set up signal connections."""
        # Button connections
        self.create_button.clicked.connect(self.attempt_registration)
        self.cancel_button.clicked.connect(self.reject)

        # Field validation connections
        self.username_edit.textChanged.connect(self.validate_form)
        self.full_name_edit.textChanged.connect(self.validate_form)
        self.password_edit.textChanged.connect(self.validate_form)
        self.confirm_password_edit.textChanged.connect(self.validate_form)
        self.email_edit.textChanged.connect(self.validate_form)

        # Show password checkbox
        self.show_password_checkbox.toggled.connect(self.toggle_password_visibility)

        # Enter key handling
        self.username_edit.returnPressed.connect(self.attempt_registration)
        self.full_name_edit.returnPressed.connect(self.attempt_registration)
        self.email_edit.returnPressed.connect(self.attempt_registration)
        self.password_edit.returnPressed.connect(self.attempt_registration)
        self.confirm_password_edit.returnPressed.connect(self.attempt_registration)

    def apply_enhanced_styles(self):
        """Apply modern, professional styling consistent with PROJECT-ALPHA design system."""
        self.setStyleSheet(f"""
            /* Main Dialog Styling */
            QDialog {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 1px solid #dee2e6;
            }}

            /* Enhanced Header Styling */
            QFrame#headerFrame {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
                margin-bottom: {DPIScaler.scale_size(10)}px;
            }}

            QLabel#titleLabel {{
                color: #212529;
                font-weight: bold;
                font-size: {DPIScaler.scale_font_size(20)}px;
            }}

            QLabel#subtitleLabel {{
                color: #007bff;
                font-weight: 500;
                font-size: {DPIScaler.scale_font_size(12)}px;
            }}

            QLabel#descriptionLabel {{
                color: #6c757d;
                font-size: {DPIScaler.scale_font_size(11)}px;
                margin: {DPIScaler.scale_size(10)}px 0;
            }}

            QFrame#headerSeparator {{
                color: #dee2e6;
                margin: {DPIScaler.scale_size(5)}px 0;
            }}

            /* Enhanced Form Group Styling */
            QGroupBox#formGroup {{
                font-weight: bold;
                font-size: {DPIScaler.scale_font_size(14)}px;
                color: #495057;
                border: 2px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(8)}px;
                margin-top: {DPIScaler.scale_size(15)}px;
                padding-top: {DPIScaler.scale_size(15)}px;
                background-color: #ffffff;
            }}

            QGroupBox#formGroup::title {{
                subcontrol-origin: margin;
                left: {DPIScaler.scale_size(15)}px;
                padding: 0 {DPIScaler.scale_size(8)}px;
                background-color: #ffffff;
            }}

            /* Enhanced Input Field Styling */
            QLineEdit {{
                border: 2px solid #ced4da;
                border-radius: {DPIScaler.scale_size(6)}px;
                padding: {DPIScaler.scale_size(10)}px {DPIScaler.scale_size(12)}px;
                font-size: {DPIScaler.scale_font_size(12)}px;
                background-color: #ffffff;
                color: #495057;
                selection-background-color: #007bff;
            }}

            QLineEdit:focus {{
                border-color: #007bff;
                outline: none;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }}

            QLineEdit:hover {{
                border-color: #adb5bd;
            }}

            /* Validation States */
            QLineEdit.valid {{
                border-color: #28a745;
            }}

            QLineEdit.invalid {{
                border-color: #dc3545;
            }}

            /* ComboBox Styling */
            QComboBox {{
                border: 2px solid #ced4da;
                border-radius: {DPIScaler.scale_size(6)}px;
                padding: {DPIScaler.scale_size(8)}px {DPIScaler.scale_size(12)}px;
                font-size: {DPIScaler.scale_font_size(12)}px;
                background-color: #ffffff;
                color: #495057;
            }}

            QComboBox:focus {{
                border-color: #007bff;
            }}

            QComboBox::drop-down {{
                border: none;
                width: {DPIScaler.scale_size(20)}px;
            }}

            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
            }}

            /* Enhanced Button Styling */
            QPushButton {{
                {PRIMARY_BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
                font-weight: 600;
            }}

            QPushButton#cancel_button {{
                {DANGER_BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(40)}px;
                font-size: {DPIScaler.scale_font_size(13)}px;
                font-weight: 600;
            }}

            QPushButton#toggleRequirementsBtn {{
                {BUTTON_STYLE}
                min-height: {DPIScaler.scale_size(30)}px;
                font-size: {DPIScaler.scale_font_size(11)}px;
                margin-top: {DPIScaler.scale_size(10)}px;
            }}

            /* Checkbox Styling */
            QCheckBox {{
                font-size: {DPIScaler.scale_font_size(11)}px;
                color: #495057;
                spacing: {DPIScaler.scale_size(8)}px;
            }}

            QCheckBox::indicator {{
                width: {DPIScaler.scale_size(16)}px;
                height: {DPIScaler.scale_size(16)}px;
                border: 2px solid #ced4da;
                border-radius: {DPIScaler.scale_size(3)}px;
                background-color: #ffffff;
            }}

            QCheckBox::indicator:checked {{
                background-color: #007bff;
                border-color: #007bff;
                image: none;
            }}

            /* Password Requirements Group */
            QGroupBox#passwordRequirementsGroup {{
                font-weight: normal;
                font-size: {DPIScaler.scale_font_size(12)}px;
                color: #495057;
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(6)}px;
                margin-top: {DPIScaler.scale_size(10)}px;
                padding-top: {DPIScaler.scale_size(10)}px;
                background-color: #f8f9fa;
            }}

            /* Validation Indicators */
            QLabel[objectName*="Indicator"] {{
                font-size: {DPIScaler.scale_font_size(14)}px;
                font-weight: bold;
                border-radius: {DPIScaler.scale_size(10)}px;
                background-color: #f8f9fa;
            }}

            /* Requirement Labels */
            QLabel[objectName*="req_"][objectName*="_label"] {{
                font-size: {DPIScaler.scale_font_size(11)}px;
                color: #6c757d;
            }}

            QLabel[objectName*="req_"][objectName*="_indicator"] {{
                font-size: {DPIScaler.scale_font_size(12)}px;
                color: #6c757d;
            }}

            /* Progress Bar */
            QProgressBar {{
                border: 1px solid #dee2e6;
                border-radius: {DPIScaler.scale_size(4)}px;
                background-color: #f8f9fa;
                text-align: center;
                font-size: {DPIScaler.scale_font_size(11)}px;
            }}

            QProgressBar::chunk {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #007bff, stop: 1 #0056b3);
                border-radius: {DPIScaler.scale_size(3)}px;
            }}

            /* Tooltips */
            QToolTip {{
                background-color: #212529;
                color: #ffffff;
                border: 1px solid #495057;
                border-radius: {DPIScaler.scale_size(4)}px;
                padding: {DPIScaler.scale_size(5)}px;
                font-size: {DPIScaler.scale_font_size(10)}px;
            }}
        """)

        # Set object names for styling
        if hasattr(self, 'cancel_button'):
            self.cancel_button.setObjectName("cancel_button")

    def load_available_roles(self):
        """Load available roles based on current user permissions."""
        try:
            current_user_role = None
            if self.current_user:
                current_user_role = self.current_user.role_name

            self.available_roles = AuthenticationService.get_available_roles(current_user_role)

            self.role_combo.clear()
            for role in self.available_roles:
                display_text = f"{role['role_name']}"
                if role.get('role_description'):
                    display_text += f" - {role['role_description']}"
                self.role_combo.addItem(display_text, role['role_name'])

            # Set default selection based on context
            if not self.is_admin_mode:
                # Check if this is the first user in the system
                total_users = AuthenticationService.count_total_users()

                if total_users == 0:
                    # First user - default to Administrator
                    for i in range(self.role_combo.count()):
                        if self.role_combo.itemData(i) == 'Administrator':
                            self.role_combo.setCurrentIndex(i)
                            logger.info("First user registration - defaulting to Administrator role")
                            break
                else:
                    # Subsequent users in open registration - default to Read-Only
                    for i in range(self.role_combo.count()):
                        if self.role_combo.itemData(i) == 'Read-Only':
                            self.role_combo.setCurrentIndex(i)
                            break

            logger.info(f"Loaded {len(self.available_roles)} available roles")

        except Exception as e:
            logger.error(f"Error loading available roles: {e}")
            self.show_error("Failed to load available roles. Please try again.")

    def toggle_password_visibility(self, show):
        """Toggle password field visibility."""
        if show:
            self.password_edit.setEchoMode(QLineEdit.Normal)
            self.confirm_password_edit.setEchoMode(QLineEdit.Normal)
        else:
            self.password_edit.setEchoMode(QLineEdit.Password)
            self.confirm_password_edit.setEchoMode(QLineEdit.Password)

    def validate_form(self):
        """Enhanced form validation with real-time visual feedback."""
        username = self.username_edit.text().strip()
        full_name = self.full_name_edit.text().strip()
        email = self.email_edit.text().strip()
        password = self.password_edit.text()
        confirm_password = self.confirm_password_edit.text()

        # Validate individual fields with visual feedback
        username_valid = self.validate_field("username", username, len(username) >= 3)
        fullname_valid = self.validate_field("full_name", full_name, len(full_name) >= 1)
        email_valid = self.validate_field("email", email, True)  # Email is optional
        password_valid = self.validate_password_field(password)
        confirm_password_valid = self.validate_field("confirm_password", confirm_password,
                                                   password == confirm_password and len(password) > 0)

        # Overall form validation
        is_valid = (
            username_valid and
            fullname_valid and
            email_valid and
            password_valid and
            confirm_password_valid and
            self.role_combo.currentData() is not None
        )

        self.create_button.setEnabled(is_valid)

        # Clear status if form becomes valid
        if is_valid and self.status_label.isVisible():
            self.status_label.setVisible(False)

    def validate_field(self, field_name, value, is_valid):
        """Validate individual field and update visual indicators."""
        if field_name not in self.validation_states:
            return is_valid

        field_data = self.validation_states[field_name]
        field_widget = field_data["field"]
        indicator = field_data["indicator"]

        # Update field styling
        if value:  # Only show validation state if field has content
            if is_valid:
                field_widget.setProperty("class", "valid")
                indicator.setText("✓")
                indicator.setStyleSheet("color: #28a745; background-color: #d4edda; border-radius: 10px;")
                indicator.setVisible(True)
            else:
                field_widget.setProperty("class", "invalid")
                indicator.setText("✗")
                indicator.setStyleSheet("color: #dc3545; background-color: #f8d7da; border-radius: 10px;")
                indicator.setVisible(True)
        else:
            # Field is empty - neutral state
            field_widget.setProperty("class", "")
            indicator.setVisible(False)

        # Refresh styling
        field_widget.style().unpolish(field_widget)
        field_widget.style().polish(field_widget)

        field_data["valid"] = is_valid
        return is_valid

    def validate_password_field(self, password):
        """Enhanced password validation with requirement indicators."""
        if not hasattr(self, 'password_requirements'):
            return len(password) >= 8

        # Check each requirement
        requirements_met = {
            "length": len(password) >= 8,
            "uppercase": any(c.isupper() for c in password),
            "lowercase": any(c.islower() for c in password),
            "number": any(c.isdigit() for c in password),
            "special": any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password),
            "common": True  # Simplified for now - could integrate with common password list
        }

        # Update requirement indicators
        for req_id, is_met in requirements_met.items():
            if req_id in self.password_requirements:
                req_data = self.password_requirements[req_id]
                req_data["met"] = is_met

                if password:  # Only show indicators if password field has content
                    if is_met:
                        req_data["indicator"].setText("✓")
                        req_data["indicator"].setStyleSheet("color: #28a745;")
                        req_data["label"].setStyleSheet("color: #28a745;")
                    else:
                        req_data["indicator"].setText("✗")
                        req_data["indicator"].setStyleSheet("color: #dc3545;")
                        req_data["label"].setStyleSheet("color: #dc3545;")
                else:
                    # Password field is empty - neutral state
                    req_data["indicator"].setText("○")
                    req_data["indicator"].setStyleSheet("color: #6c757d;")
                    req_data["label"].setStyleSheet("color: #6c757d;")

        # Update password field validation state
        all_requirements_met = all(requirements_met.values())
        return self.validate_field("password", password, all_requirements_met)

    def show_error(self, message):
        """Show error message to user."""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_label.setVisible(True)

        # Hide progress bar
        self.progress_bar.setVisible(False)

        # Re-enable buttons
        self.create_button.setEnabled(True)
        self.cancel_button.setEnabled(True)

    def show_success(self, message):
        """Show success message to user."""
        self.status_label.setText(f"✅ {message}")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        self.status_label.setVisible(True)

    def attempt_registration(self):
        """Attempt to register new user account."""
        # Get form data
        username = self.username_edit.text().strip()
        full_name = self.full_name_edit.text().strip()
        email = self.email_edit.text().strip()
        password = self.password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        role_name = self.role_combo.currentData()

        # Validate required fields
        if not username:
            self.show_error("Username is required.")
            self.username_edit.setFocus()
            return

        if not full_name:
            self.show_error("Full name is required.")
            self.full_name_edit.setFocus()
            return

        if not password:
            self.show_error("Password is required.")
            self.password_edit.setFocus()
            return

        if password != confirm_password:
            self.show_error("Passwords do not match.")
            self.confirm_password_edit.setFocus()
            return

        if not role_name:
            self.show_error("Please select a role.")
            self.role_combo.setFocus()
            return

        # Validate username format
        username_valid, username_error = AuthenticationService.validate_username(username)
        if not username_valid:
            self.show_error(username_error)
            self.username_edit.setFocus()
            return

        # Validate password strength
        password_valid, password_error = AuthenticationService.validate_password_strength(password)
        if not password_valid:
            self.show_error(password_error)
            self.password_edit.setFocus()
            return

        # Validate email if provided
        if email:
            email_valid, email_error = AuthenticationService.validate_email(email)
            if not email_valid:
                self.show_error(email_error)
                self.email_edit.setFocus()
                return

        # Check username availability
        if not AuthenticationService.is_username_available(username):
            self.show_error("Username is already taken. Please choose a different username.")
            self.username_edit.setFocus()
            return

        # Show progress and disable buttons
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.create_button.setEnabled(False)
        self.cancel_button.setEnabled(False)
        self.status_label.setVisible(False)

        # Get created_by_user_id
        created_by_user_id = None
        if self.current_user:
            created_by_user_id = self.current_user.user_id

        # Start registration in worker thread
        self.registration_worker = RegistrationWorker(
            username, password, full_name, email, role_name, created_by_user_id
        )
        self.registration_worker.registration_complete.connect(self.on_registration_complete)
        self.registration_worker.registration_error.connect(self.on_registration_error)
        self.registration_worker.start()

        logger.info(f"Registration attempt started for username: {username}")

    @pyqtSlot(bool, str, int)
    def on_registration_complete(self, success, message, user_id):
        """Handle registration completion."""
        self.progress_bar.setVisible(False)

        if success:
            self.show_success(message)
            logger.info(f"Registration successful for user ID: {user_id}")

            # Emit success signal
            self.registration_successful.emit(user_id)

            # Close dialog after short delay
            QTimer.singleShot(2000, self.accept)
        else:
            self.show_error(message)
            logger.warning(f"Registration failed: {message}")

            # Re-enable buttons
            self.create_button.setEnabled(True)
            self.cancel_button.setEnabled(True)

    @pyqtSlot(str)
    def on_registration_error(self, error_message):
        """Handle registration error."""
        self.show_error(f"Registration failed: {error_message}")
        logger.error(f"Registration error: {error_message}")

        # Re-enable buttons
        self.create_button.setEnabled(True)
        self.cancel_button.setEnabled(True)

    def get_registered_user_id(self):
        """Get the ID of the successfully registered user."""
        # This would be set by the registration completion handler
        return getattr(self, '_registered_user_id', None)
