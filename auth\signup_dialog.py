"""
Sign-Up Dialog for PROJECT-ALPHA
Provides secure user registration interface with validation and role-based access control.
"""

import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QComboBox, QFrame,
                             QMessageBox, QProgressBar, QGroupBox, QTextEdit,
                             QCheckBox, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon
from auth.authentication_service import AuthenticationService
from ui.window_utils import DPIScaler

logger = logging.getLogger(__name__)


class RegistrationWorker(QThread):
    """Worker thread for user registration to prevent UI blocking."""
    
    registration_complete = pyqtSignal(bool, str, int)  # success, message, user_id
    registration_error = pyqtSignal(str)
    
    def __init__(self, username, password, full_name, email, role_name, created_by_user_id):
        super().__init__()
        self.username = username
        self.password = password
        self.full_name = full_name
        self.email = email
        self.role_name = role_name
        self.created_by_user_id = created_by_user_id
    
    def run(self):
        """Perform user registration in background thread."""
        try:
            success, message, user_id = AuthenticationService.register_user(
                self.username, self.password, self.full_name, self.email,
                self.role_name, self.created_by_user_id
            )
            self.registration_complete.emit(success, message, user_id)
        except Exception as e:
            logger.error(f"Registration error: {e}")
            self.registration_error.emit(str(e))


class SignUpDialog(QDialog):
    """
    Comprehensive user registration dialog with validation and role-based access control.
    Supports both open registration and administrator-managed registration.
    """
    
    # Signals
    registration_successful = pyqtSignal(int)  # user_id
    registration_cancelled = pyqtSignal()
    
    def __init__(self, parent=None, current_user=None, is_admin_mode=False):
        """
        Initialize sign-up dialog.
        
        Args:
            parent: Parent widget
            current_user: Current authenticated user (None for open registration)
            is_admin_mode: True if called from admin user management interface
        """
        super().__init__(parent)
        self.current_user = current_user
        self.is_admin_mode = is_admin_mode
        self.registration_worker = None
        self.available_roles = []
        
        self.setWindowTitle("PROJECT-ALPHA - Create New Account")
        self.setModal(True)
        self.setFixedSize(DPIScaler.scale_size(500), DPIScaler.scale_size(600))
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)
        
        self.setup_ui()
        self.setup_connections()
        self.apply_styles()
        self.load_available_roles()
        
        # Focus on username field
        self.username_edit.setFocus()
        
        logger.info(f"SignUpDialog initialized - Admin mode: {is_admin_mode}")
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(DPIScaler.scale_size(15))
        layout.setContentsMargins(DPIScaler.scale_size(20), DPIScaler.scale_size(20),
                                  DPIScaler.scale_size(20), DPIScaler.scale_size(20))
        
        # Create header
        self.create_header(layout)
        
        # Create scroll area for form
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Create form widget
        form_widget = QFrame()
        form_layout = QVBoxLayout(form_widget)
        
        # Create registration form
        self.create_registration_form(form_layout)
        
        # Create password requirements info
        self.create_password_requirements(form_layout)
        
        scroll_area.setWidget(form_widget)
        layout.addWidget(scroll_area)
        
        # Create progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Create buttons
        self.create_buttons(layout)
        
        # Create status label
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)
    
    def create_header(self, layout):
        """Create header section with title."""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        
        # Title
        title_text = "Create New Account"
        if self.is_admin_mode:
            title_text = "Create User Account"
        
        title_label = QLabel(title_text)
        title_font = QFont()
        title_font.setPointSize(DPIScaler.scale_font_size(16))
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # Subtitle
        if self.is_admin_mode:
            subtitle_text = "Administrator User Management"
        else:
            subtitle_text = "Join PROJECT-ALPHA System"
        
        subtitle_label = QLabel(subtitle_text)
        subtitle_font = QFont()
        subtitle_font.setPointSize(DPIScaler.scale_font_size(10))
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)

    def create_registration_form(self, layout):
        """Create registration form with all required fields."""
        form_group = QGroupBox("Account Information")
        form_layout = QFormLayout(form_group)
        form_layout.setSpacing(DPIScaler.scale_size(10))

        # Username field
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Enter unique username")
        self.username_edit.setMinimumHeight(DPIScaler.scale_size(32))
        self.username_edit.setMaxLength(50)
        form_layout.addRow("Username *:", self.username_edit)

        # Full name field
        self.full_name_edit = QLineEdit()
        self.full_name_edit.setPlaceholderText("Enter full name")
        self.full_name_edit.setMinimumHeight(DPIScaler.scale_size(32))
        self.full_name_edit.setMaxLength(100)
        form_layout.addRow("Full Name *:", self.full_name_edit)

        # Email field (optional)
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Enter email address (optional)")
        self.email_edit.setMinimumHeight(DPIScaler.scale_size(32))
        self.email_edit.setMaxLength(255)
        form_layout.addRow("Email:", self.email_edit)

        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Enter secure password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(DPIScaler.scale_size(32))
        self.password_edit.setMaxLength(128)
        form_layout.addRow("Password *:", self.password_edit)

        # Confirm password field
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setPlaceholderText("Confirm password")
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setMinimumHeight(DPIScaler.scale_size(32))
        self.confirm_password_edit.setMaxLength(128)
        form_layout.addRow("Confirm Password *:", self.confirm_password_edit)

        # Role selection (admin mode or open registration with limited roles)
        self.role_combo = QComboBox()
        self.role_combo.setMinimumHeight(DPIScaler.scale_size(32))
        form_layout.addRow("Role *:", self.role_combo)

        # Show password checkbox
        self.show_password_checkbox = QCheckBox("Show passwords")
        form_layout.addRow("", self.show_password_checkbox)

        layout.addWidget(form_group)

    def create_password_requirements(self, layout):
        """Create password requirements information panel."""
        req_group = QGroupBox("Password Requirements")
        req_layout = QVBoxLayout(req_group)

        requirements_text = """Password must meet the following requirements:
• At least 8 characters long
• Contains at least one uppercase letter (A-Z)
• Contains at least one lowercase letter (a-z)
• Contains at least one number (0-9)
• Contains at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)
• Cannot be a common weak password"""

        req_label = QLabel(requirements_text)
        req_label.setWordWrap(True)
        req_label.setStyleSheet("color: #666; font-size: 10px;")
        req_layout.addWidget(req_label)

        layout.addWidget(req_group)

    def create_buttons(self, layout):
        """Create dialog buttons."""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(DPIScaler.scale_size(10))

        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.cancel_button.setMinimumWidth(DPIScaler.scale_size(100))
        button_layout.addWidget(self.cancel_button)

        button_layout.addStretch()

        # Create account button
        self.create_button = QPushButton("Create Account")
        self.create_button.setMinimumHeight(DPIScaler.scale_size(35))
        self.create_button.setMinimumWidth(DPIScaler.scale_size(120))
        self.create_button.setDefault(True)
        button_layout.addWidget(self.create_button)

        layout.addLayout(button_layout)

    def setup_connections(self):
        """Set up signal connections."""
        # Button connections
        self.create_button.clicked.connect(self.attempt_registration)
        self.cancel_button.clicked.connect(self.reject)

        # Field validation connections
        self.username_edit.textChanged.connect(self.validate_form)
        self.full_name_edit.textChanged.connect(self.validate_form)
        self.password_edit.textChanged.connect(self.validate_form)
        self.confirm_password_edit.textChanged.connect(self.validate_form)
        self.email_edit.textChanged.connect(self.validate_form)

        # Show password checkbox
        self.show_password_checkbox.toggled.connect(self.toggle_password_visibility)

        # Enter key handling
        self.username_edit.returnPressed.connect(self.attempt_registration)
        self.full_name_edit.returnPressed.connect(self.attempt_registration)
        self.email_edit.returnPressed.connect(self.attempt_registration)
        self.password_edit.returnPressed.connect(self.attempt_registration)
        self.confirm_password_edit.returnPressed.connect(self.attempt_registration)

    def apply_styles(self):
        """Apply consistent styling to the dialog."""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QPushButton#cancel_button {
                background-color: #f44336;
            }
            QPushButton#cancel_button:hover {
                background-color: #da190b;
            }
        """)

        # Set object names for styling
        self.cancel_button.setObjectName("cancel_button")

    def load_available_roles(self):
        """Load available roles based on current user permissions."""
        try:
            current_user_role = None
            if self.current_user:
                current_user_role = self.current_user.role_name

            self.available_roles = AuthenticationService.get_available_roles(current_user_role)

            self.role_combo.clear()
            for role in self.available_roles:
                display_text = f"{role['role_name']}"
                if role.get('role_description'):
                    display_text += f" - {role['role_description']}"
                self.role_combo.addItem(display_text, role['role_name'])

            # Set default selection
            if not self.is_admin_mode:
                # For open registration, default to Read-Only
                for i in range(self.role_combo.count()):
                    if self.role_combo.itemData(i) == 'Read-Only':
                        self.role_combo.setCurrentIndex(i)
                        break

            logger.info(f"Loaded {len(self.available_roles)} available roles")

        except Exception as e:
            logger.error(f"Error loading available roles: {e}")
            self.show_error("Failed to load available roles. Please try again.")

    def toggle_password_visibility(self, show):
        """Toggle password field visibility."""
        if show:
            self.password_edit.setEchoMode(QLineEdit.Normal)
            self.confirm_password_edit.setEchoMode(QLineEdit.Normal)
        else:
            self.password_edit.setEchoMode(QLineEdit.Password)
            self.confirm_password_edit.setEchoMode(QLineEdit.Password)

    def validate_form(self):
        """Validate form fields and enable/disable create button."""
        username = self.username_edit.text().strip()
        full_name = self.full_name_edit.text().strip()
        password = self.password_edit.text()
        confirm_password = self.confirm_password_edit.text()

        # Basic field validation
        is_valid = (
            len(username) >= 3 and
            len(full_name) >= 1 and
            len(password) >= 8 and
            password == confirm_password and
            self.role_combo.currentData() is not None
        )

        self.create_button.setEnabled(is_valid)

        # Clear status if form becomes valid
        if is_valid and self.status_label.isVisible():
            self.status_label.setVisible(False)

    def show_error(self, message):
        """Show error message to user."""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_label.setVisible(True)

        # Hide progress bar
        self.progress_bar.setVisible(False)

        # Re-enable buttons
        self.create_button.setEnabled(True)
        self.cancel_button.setEnabled(True)

    def show_success(self, message):
        """Show success message to user."""
        self.status_label.setText(f"✅ {message}")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        self.status_label.setVisible(True)

    def attempt_registration(self):
        """Attempt to register new user account."""
        # Get form data
        username = self.username_edit.text().strip()
        full_name = self.full_name_edit.text().strip()
        email = self.email_edit.text().strip()
        password = self.password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        role_name = self.role_combo.currentData()

        # Validate required fields
        if not username:
            self.show_error("Username is required.")
            self.username_edit.setFocus()
            return

        if not full_name:
            self.show_error("Full name is required.")
            self.full_name_edit.setFocus()
            return

        if not password:
            self.show_error("Password is required.")
            self.password_edit.setFocus()
            return

        if password != confirm_password:
            self.show_error("Passwords do not match.")
            self.confirm_password_edit.setFocus()
            return

        if not role_name:
            self.show_error("Please select a role.")
            self.role_combo.setFocus()
            return

        # Validate username format
        username_valid, username_error = AuthenticationService.validate_username(username)
        if not username_valid:
            self.show_error(username_error)
            self.username_edit.setFocus()
            return

        # Validate password strength
        password_valid, password_error = AuthenticationService.validate_password_strength(password)
        if not password_valid:
            self.show_error(password_error)
            self.password_edit.setFocus()
            return

        # Validate email if provided
        if email:
            email_valid, email_error = AuthenticationService.validate_email(email)
            if not email_valid:
                self.show_error(email_error)
                self.email_edit.setFocus()
                return

        # Check username availability
        if not AuthenticationService.is_username_available(username):
            self.show_error("Username is already taken. Please choose a different username.")
            self.username_edit.setFocus()
            return

        # Show progress and disable buttons
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.create_button.setEnabled(False)
        self.cancel_button.setEnabled(False)
        self.status_label.setVisible(False)

        # Get created_by_user_id
        created_by_user_id = None
        if self.current_user:
            created_by_user_id = self.current_user.user_id

        # Start registration in worker thread
        self.registration_worker = RegistrationWorker(
            username, password, full_name, email, role_name, created_by_user_id
        )
        self.registration_worker.registration_complete.connect(self.on_registration_complete)
        self.registration_worker.registration_error.connect(self.on_registration_error)
        self.registration_worker.start()

        logger.info(f"Registration attempt started for username: {username}")

    @pyqtSlot(bool, str, int)
    def on_registration_complete(self, success, message, user_id):
        """Handle registration completion."""
        self.progress_bar.setVisible(False)

        if success:
            self.show_success(message)
            logger.info(f"Registration successful for user ID: {user_id}")

            # Emit success signal
            self.registration_successful.emit(user_id)

            # Close dialog after short delay
            QTimer.singleShot(2000, self.accept)
        else:
            self.show_error(message)
            logger.warning(f"Registration failed: {message}")

            # Re-enable buttons
            self.create_button.setEnabled(True)
            self.cancel_button.setEnabled(True)

    @pyqtSlot(str)
    def on_registration_error(self, error_message):
        """Handle registration error."""
        self.show_error(f"Registration failed: {error_message}")
        logger.error(f"Registration error: {error_message}")

        # Re-enable buttons
        self.create_button.setEnabled(True)
        self.cancel_button.setEnabled(True)

    def get_registered_user_id(self):
        """Get the ID of the successfully registered user."""
        # This would be set by the registration completion handler
        return getattr(self, '_registered_user_id', None)
