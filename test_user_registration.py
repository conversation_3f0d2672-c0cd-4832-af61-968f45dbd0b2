"""
Comprehensive test suite for user registration functionality in PROJECT-ALPHA.
Tests all aspects of the user registration system including validation, database integration,
and UI components.
"""

import sys
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import test modules
from auth.authentication_service import AuthenticationService
from auth.signup_dialog import SignUpDialog
from auth.user_management_dialog import UserManagementDialog
from database import init_db


class TestUserRegistrationSystem(unittest.TestCase):
    """Test suite for user registration system."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test database."""
        # Create temporary database for testing
        cls.test_db_fd, cls.test_db_path = tempfile.mkstemp(suffix='.db')
        
        # Set database path for testing
        os.environ['DATABASE_PATH'] = cls.test_db_path
        
        # Initialize test database
        success, first_run = init_db()
        if not success:
            raise Exception("Failed to initialize test database")
        
        print(f"Test database created at: {cls.test_db_path}")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test database."""
        os.close(cls.test_db_fd)
        os.unlink(cls.test_db_path)
        print("Test database cleaned up")
    
    def test_password_validation(self):
        """Test password strength validation."""
        print("\n=== Testing Password Validation ===")
        
        # Test valid passwords
        valid_passwords = [
            "SecurePass123!",
            "MyP@ssw0rd",
            "Complex#Password1",
            "Test123$Password"
        ]
        
        for password in valid_passwords:
            is_valid, message = AuthenticationService.validate_password_strength(password)
            self.assertTrue(is_valid, f"Password '{password}' should be valid: {message}")
            print(f"✓ Valid password: {password}")
        
        # Test invalid passwords
        invalid_passwords = [
            ("short", "Password must be at least 8 characters long"),
            ("nouppercase123!", "Password must contain at least one uppercase letter"),
            ("NOLOWERCASE123!", "Password must contain at least one lowercase letter"),
            ("NoNumbers!", "Password must contain at least one number"),
            ("NoSpecialChars123", "Password must contain at least one special character"),
            ("password123!", "Password is too common")
        ]
        
        for password, expected_error in invalid_passwords:
            is_valid, message = AuthenticationService.validate_password_strength(password)
            self.assertFalse(is_valid, f"Password '{password}' should be invalid")
            self.assertIn(expected_error.split()[0], message, f"Error message should contain expected text")
            print(f"✓ Invalid password rejected: {password} - {message}")
    
    def test_username_validation(self):
        """Test username format validation."""
        print("\n=== Testing Username Validation ===")
        
        # Test valid usernames
        valid_usernames = [
            "testuser",
            "test_user",
            "test-user",
            "user123",
            "TestUser",
            "test_user_123"
        ]
        
        for username in valid_usernames:
            is_valid, message = AuthenticationService.validate_username(username)
            self.assertTrue(is_valid, f"Username '{username}' should be valid: {message}")
            print(f"✓ Valid username: {username}")
        
        # Test invalid usernames
        invalid_usernames = [
            ("", "Username is required"),
            ("ab", "Username must be at least 3 characters long"),
            ("123user", "Username cannot start with a number"),
            ("user@domain", "Username can only contain letters, numbers, underscores, and hyphens"),
            ("user space", "Username can only contain letters, numbers, underscores, and hyphens")
        ]
        
        for username, expected_error in invalid_usernames:
            is_valid, message = AuthenticationService.validate_username(username)
            self.assertFalse(is_valid, f"Username '{username}' should be invalid")
            print(f"✓ Invalid username rejected: {username} - {message}")
    
    def test_email_validation(self):
        """Test email format validation."""
        print("\n=== Testing Email Validation ===")
        
        # Test valid emails
        valid_emails = [
            "",  # Empty email should be valid (optional field)
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            is_valid, message = AuthenticationService.validate_email(email)
            self.assertTrue(is_valid, f"Email '{email}' should be valid: {message}")
            print(f"✓ Valid email: {email if email else '(empty)'}")
        
        # Test invalid emails
        invalid_emails = [
            "invalid-email",
            "@example.com",
            "user@",
            "user@.com",
            "<EMAIL>"
        ]
        
        for email in invalid_emails:
            is_valid, message = AuthenticationService.validate_email(email)
            self.assertFalse(is_valid, f"Email '{email}' should be invalid: got message '{message}'")
            print(f"✓ Invalid email rejected: {email} - {message}")
    
    def test_user_registration(self):
        """Test complete user registration process."""
        print("\n=== Testing User Registration ===")
        
        # Generate unique username for this test run
        import time
        unique_suffix = str(int(time.time() * 1000))[-6:]  # Last 6 digits of timestamp
        test_username = f"testuser{unique_suffix}"

        # Test successful registration
        success, message, user_id = AuthenticationService.register_user(
            username=test_username,
            password="TestPass123!",
            full_name="Test User One",
            email="<EMAIL>",
            role_name="Read-Only"
        )
        
        self.assertTrue(success, f"Registration should succeed: {message}")
        self.assertGreater(user_id, 0, "User ID should be positive")
        print(f"✓ User registered successfully: ID {user_id}")
        
        # Test duplicate username
        success, message, user_id = AuthenticationService.register_user(
            username=test_username,  # Same username as above
            password="TestPass123!",
            full_name="Test User Two",
            email="<EMAIL>",
            role_name="Read-Only"
        )
        
        self.assertFalse(success, "Registration with duplicate username should fail")
        self.assertIn("already taken", message, "Error message should mention username is taken")
        print(f"✓ Duplicate username rejected: {message}")
        
        # Test username availability check
        self.assertFalse(AuthenticationService.is_username_available(test_username))
        self.assertTrue(AuthenticationService.is_username_available(f"available{unique_suffix}"))
        print("✓ Username availability check working")
    
    def test_role_assignment(self):
        """Test role assignment during registration."""
        print("\n=== Testing Role Assignment ===")
        
        # Test available roles for different user types
        admin_roles = AuthenticationService.get_available_roles("Administrator")
        non_admin_roles = AuthenticationService.get_available_roles("Read-Write")
        open_roles = AuthenticationService.get_available_roles(None)
        
        # Admin should see all roles
        self.assertGreaterEqual(len(admin_roles), 3, "Administrator should see all roles")
        print(f"✓ Administrator can assign {len(admin_roles)} roles")
        
        # Non-admin should see limited roles
        self.assertEqual(len(non_admin_roles), 1, "Non-admin should see only Read-Only role")
        self.assertEqual(non_admin_roles[0]['role_name'], 'Read-Only')
        print(f"✓ Non-administrator can assign {len(non_admin_roles)} role(s)")
        
        # Open registration should see limited roles
        self.assertEqual(len(open_roles), 1, "Open registration should see only Read-Only role")
        print(f"✓ Open registration can assign {len(open_roles)} role(s)")
    
    def test_user_count(self):
        """Test user counting functionality."""
        print("\n=== Testing User Count ===")
        
        initial_count = AuthenticationService.count_total_users()
        print(f"Initial user count: {initial_count}")
        
        # Generate unique username for count test
        import time
        unique_suffix = str(int(time.time() * 1000))[-6:]
        count_test_username = f"counttest{unique_suffix}"

        # Register a new user
        success, message, user_id = AuthenticationService.register_user(
            username=count_test_username,
            password="TestPass123!",
            full_name="Count Test User",
            role_name="Read-Only"
        )
        
        self.assertTrue(success, "User registration should succeed")
        
        # Check count increased
        new_count = AuthenticationService.count_total_users()
        self.assertEqual(new_count, initial_count + 1, "User count should increase by 1")
        print(f"✓ User count increased to: {new_count}")
    
    def test_signup_dialog_creation(self):
        """Test SignUpDialog creation and initialization."""
        print("\n=== Testing SignUpDialog Creation ===")

        try:
            # Create QApplication for GUI testing
            from PyQt5.QtWidgets import QApplication
            import sys

            if not QApplication.instance():
                app = QApplication(sys.argv)

            # Test open registration mode
            dialog = SignUpDialog(parent=None, current_user=None, is_admin_mode=False)
            self.assertIsNotNone(dialog, "SignUpDialog should be created")
            self.assertFalse(dialog.is_admin_mode, "Should be in open registration mode")
            print("✓ SignUpDialog created successfully (open registration)")

            # Test admin mode
            mock_user = MagicMock()
            mock_user.role_name = "Administrator"
            mock_user.user_id = 1

            admin_dialog = SignUpDialog(parent=None, current_user=mock_user, is_admin_mode=True)
            self.assertIsNotNone(admin_dialog, "Admin SignUpDialog should be created")
            self.assertTrue(admin_dialog.is_admin_mode, "Should be in admin mode")
            print("✓ SignUpDialog created successfully (admin mode)")

        except Exception as e:
            print(f"⚠ SignUpDialog creation test skipped (requires GUI): {e}")

    def test_user_management_dialog_creation(self):
        """Test UserManagementDialog creation and initialization."""
        print("\n=== Testing UserManagementDialog Creation ===")

        try:
            # Create QApplication for GUI testing
            from PyQt5.QtWidgets import QApplication
            import sys

            if not QApplication.instance():
                app = QApplication(sys.argv)

            # Create mock admin user
            mock_user = MagicMock()
            mock_user.role_name = "Administrator"
            mock_user.user_id = 1
            mock_user.username = "admin"

            # Test dialog creation
            dialog = UserManagementDialog(parent=None, current_user=mock_user)
            self.assertIsNotNone(dialog, "UserManagementDialog should be created")
            print("✓ UserManagementDialog created successfully")

            # Test non-admin user rejection
            mock_non_admin = MagicMock()
            mock_non_admin.role_name = "Read-Only"

            with self.assertRaises(ValueError):
                UserManagementDialog(parent=None, current_user=mock_non_admin)
            print("✓ Non-admin user correctly rejected")

        except Exception as e:
            print(f"⚠ UserManagementDialog creation test skipped (requires GUI): {e}")


def run_registration_tests():
    """Run all user registration tests."""
    print("=" * 60)
    print("PROJECT-ALPHA User Registration System Test Suite")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestUserRegistrationSystem)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\nSuccess Rate: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print("🎉 ALL TESTS PASSED! User registration system is ready.")
    else:
        print("⚠ Some tests failed. Please review and fix issues.")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_registration_tests()
