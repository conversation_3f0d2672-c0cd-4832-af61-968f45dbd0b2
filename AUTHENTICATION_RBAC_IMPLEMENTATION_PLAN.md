# PROJECT-ALPHA Authentication & Role-Based Access Control Implementation Plan

**Date:** July 3, 2025  
**Status:** DESIGN PHASE  
**Integration Target:** UI Consistency Framework + Single-Window Desktop Architecture  

---

## 🎯 System Overview

### Core Requirements Analysis
- **Two Access Modes**: Read-Only and Read-Write permissions
- **Multi-User Support**: Concurrent users with unique credentials
- **Role-Based Control**: UI-level and data-level enforcement
- **Desktop Architecture**: Maintain single-window design with local SQLite
- **Framework Integration**: Leverage existing ButtonStateManager and UI consistency

### Design Principles
1. **Security First**: Secure authentication with proper session management
2. **Seamless Integration**: Work with existing UI consistency framework
3. **Minimal Disruption**: Preserve current application architecture
4. **User Experience**: Intuitive login and role management
5. **Performance**: No impact on existing application performance

---

## 🏗️ System Architecture

### Authentication Flow
```
Application Start → Login Dialog → Authentication Service → Session Manager → Main Window
                                      ↓
                              Role Verification → ButtonStateManager → UI Enforcement
```

### Component Structure
```
auth/
├── authentication_service.py    # Core authentication logic
├── session_manager.py          # Session handling and validation
├── role_manager.py             # Role-based permission management
├── user_manager.py             # User CRUD operations
└── login_dialog.py             # Login interface

ui/
├── button_state_manager.py     # ENHANCED: Role-aware button states
├── user_management_widget.py   # NEW: User administration interface
└── auth_decorators.py          # NEW: Permission decorators for methods
```

---

## 🗄️ Database Schema Design

### New Tables Required

#### 1. Users Table
```sql
CREATE TABLE users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    salt TEXT NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    role_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    last_login TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    created_by INTEGER,
    FOREIGN KEY (role_id) REFERENCES roles(role_id),
    FOREIGN KEY (created_by) REFERENCES users(user_id)
);
```

#### 2. Roles Table
```sql
CREATE TABLE roles (
    role_id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_name TEXT UNIQUE NOT NULL,
    role_description TEXT,
    permissions TEXT NOT NULL, -- JSON string of permissions
    is_active BOOLEAN DEFAULT 1,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);
```

#### 3. User Sessions Table
```sql
CREATE TABLE user_sessions (
    session_id TEXT PRIMARY KEY,
    user_id INTEGER NOT NULL,
    login_time TEXT DEFAULT (datetime('now')),
    last_activity TEXT DEFAULT (datetime('now')),
    ip_address TEXT,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### 4. Audit Log Table (Enhanced)
```sql
CREATE TABLE audit_log (
    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    session_id TEXT,
    action_type TEXT NOT NULL,
    table_name TEXT,
    record_id TEXT,
    old_values TEXT, -- JSON
    new_values TEXT, -- JSON
    timestamp TEXT DEFAULT (datetime('now')),
    ip_address TEXT,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

### Default Data Setup
```sql
-- Default Roles
INSERT INTO roles (role_name, role_description, permissions) VALUES
('Administrator', 'Full system access including user management', 
 '{"read": true, "write": true, "delete": true, "admin": true, "user_management": true}'),
('Read-Write', 'Full CRUD access to all data', 
 '{"read": true, "write": true, "delete": true, "admin": false, "user_management": false}'),
('Read-Only', 'View-only access to all data', 
 '{"read": true, "write": false, "delete": false, "admin": false, "user_management": false}');

-- Default Admin User (password: admin123 - MUST BE CHANGED)
INSERT INTO users (username, password_hash, salt, full_name, role_id) VALUES
('admin', '[SECURE_HASH]', '[RANDOM_SALT]', 'System Administrator', 1);
```

---

## 🔐 Authentication Service Design

### Core Authentication Features
```python
class AuthenticationService:
    """Core authentication and authorization service."""
    
    @staticmethod
    def authenticate_user(username: str, password: str) -> Optional[User]:
        """Authenticate user credentials."""
        
    @staticmethod
    def hash_password(password: str, salt: str = None) -> Tuple[str, str]:
        """Secure password hashing with salt."""
        
    @staticmethod
    def verify_password(password: str, hash: str, salt: str) -> bool:
        """Verify password against stored hash."""
        
    @staticmethod
    def create_session(user: User) -> str:
        """Create new user session."""
        
    @staticmethod
    def validate_session(session_id: str) -> Optional[User]:
        """Validate active session."""
```

### Session Management
```python
class SessionManager:
    """Manage user sessions and activity."""
    
    def __init__(self):
        self.current_user = None
        self.current_session = None
        
    def login(self, username: str, password: str) -> bool:
        """Login user and create session."""
        
    def logout(self):
        """Logout current user and cleanup session."""
        
    def is_authenticated(self) -> bool:
        """Check if user is authenticated."""
        
    def has_permission(self, permission: str) -> bool:
        """Check if current user has specific permission."""
```

---

## 🎛️ Role-Based Access Control Integration

### Enhanced ButtonStateManager
```python
class ButtonStateManager(QObject):
    """Enhanced with role-based access control."""
    
    def __init__(self, parent=None, session_manager=None):
        super().__init__(parent)
        self.session_manager = session_manager
        self.permission_rules = {}
        
    def register_button(self, name, button, button_type, style_type="default", 
                       required_permission=None):
        """Register button with optional permission requirement."""
        
    def set_context(self, context, **kwargs):
        """Set context with role-based permission checking."""
        
    def check_permission(self, permission: str) -> bool:
        """Check if current user has required permission."""
```

### Permission Decorators
```python
def requires_permission(permission: str):
    """Decorator to enforce permissions on methods."""
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            if not self.session_manager.has_permission(permission):
                QMessageBox.warning(self, "Access Denied", 
                                  f"You don't have permission to {permission}")
                return
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def read_only_mode():
    """Decorator to disable method in read-only mode."""
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            if not self.session_manager.has_permission('write'):
                QMessageBox.information(self, "Read-Only Mode", 
                                      "This action is not available in read-only mode")
                return
            return func(self, *args, **kwargs)
        return wrapper
    return decorator
```

---

## 🖥️ User Interface Integration

### Login Dialog Design
```python
class LoginDialog(QDialog):
    """Secure login dialog with modern UI."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("PROJECT-ALPHA Login")
        self.setModal(True)
        self.setup_ui()
        
    def setup_ui(self):
        """Create login interface with security features."""
        # Username field
        # Password field (masked)
        # Remember me checkbox
        # Login button
        # Cancel button
        # Forgot password link (future)
        
    def authenticate(self):
        """Handle login attempt with security measures."""
        # Rate limiting
        # Input validation
        # Secure authentication
        # Session creation
```

### User Management Widget
```python
class UserManagementWidget(QWidget):
    """Administrative interface for user management."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.session_manager = None
        self.setup_ui()
        
    def setup_ui(self):
        """Create user management interface."""
        # User list table
        # Add/Edit/Delete user buttons
        # Role assignment
        # Password reset
        # User activity log
        
    @requires_permission('user_management')
    def add_user(self):
        """Add new user (admin only)."""
        
    @requires_permission('user_management')
    def edit_user(self):
        """Edit existing user (admin only)."""
```

---

## 🔄 Application Flow Integration

### Modified Main Application Flow
```python
def main():
    """Enhanced main function with authentication."""
    app = QApplication(sys.argv)
    
    # Initialize database
    database.init_db()
    
    # Initialize authentication system
    auth_service = AuthenticationService()
    session_manager = SessionManager()
    
    # Show login dialog
    login_dialog = LoginDialog()
    if login_dialog.exec_() != QDialog.Accepted:
        return 0
        
    # Create main window with session context
    main_window = MainWindow(session_manager=session_manager)
    main_window.show()
    
    return app.exec_()
```

### Widget Integration Pattern
```python
class BaseWidget(QWidget):
    """Base widget with authentication support."""
    
    def __init__(self, parent=None, session_manager=None):
        super().__init__(parent)
        self.session_manager = session_manager
        self.button_manager = ButtonStateManager(self, session_manager)
        self.setup_ui()
        self.setup_permissions()
        
    def setup_permissions(self):
        """Configure role-based UI elements."""
        # Register buttons with required permissions
        # Set up context-sensitive access control
        # Apply role-based styling
```

---

## 📊 Security Considerations

### Password Security
- **Hashing**: bcrypt or Argon2 for password hashing
- **Salt**: Unique salt per password
- **Complexity**: Minimum password requirements
- **Expiration**: Optional password expiration policy

### Session Security
- **Timeout**: Configurable session timeout
- **Validation**: Regular session validation
- **Cleanup**: Automatic cleanup of expired sessions
- **Logging**: Comprehensive audit logging

### Data Protection
- **Encryption**: Sensitive data encryption at rest
- **Access Control**: Granular permission checking
- **Audit Trail**: Complete action logging
- **Backup Security**: Secure backup procedures

---

## 🚀 Implementation Phases

### Phase 1: Core Authentication (Week 1)
- [ ] Database schema implementation
- [ ] Authentication service development
- [ ] Session management system
- [ ] Basic login dialog

### Phase 2: Role Integration (Week 2)
- [ ] Enhanced ButtonStateManager
- [ ] Permission decorators
- [ ] Widget integration framework
- [ ] Role-based UI enforcement

### Phase 3: User Management (Week 3)
- [ ] User management interface
- [ ] Administrative functions
- [ ] Audit logging enhancement
- [ ] Security hardening

### Phase 4: Testing & Deployment (Week 4)
- [ ] Comprehensive testing
- [ ] Security testing
- [ ] Performance validation
- [ ] Documentation completion

---

## 📋 Success Criteria

### Functional Requirements ✅
- [ ] Two-mode access control (Read/Write)
- [ ] Multi-user concurrent support
- [ ] Role-based permission enforcement
- [ ] Seamless UI integration
- [ ] Administrative user management

### Technical Requirements ✅
- [ ] Secure authentication implementation
- [ ] Session management with timeout
- [ ] Database integration with existing schema
- [ ] Performance impact < 5%
- [ ] Backward compatibility maintained

### Security Requirements ✅
- [ ] Secure password storage
- [ ] Session security measures
- [ ] Comprehensive audit logging
- [ ] Access control enforcement
- [ ] Data protection compliance

---

## 🤔 Technical Questions & Answers

### Q1: Should user authentication be local (SQLite-based) or integrated with external systems?

**RECOMMENDATION: Local SQLite-based Authentication**

**Rationale:**
- **Military Environment**: Maintains air-gapped security requirements
- **Existing Architecture**: Aligns with current local SQLite database design
- **Deployment Simplicity**: No external dependencies or network requirements
- **Data Sovereignty**: All authentication data remains within military facility
- **Offline Operation**: Continues to work without network connectivity

**Implementation:**
- Store user credentials in local SQLite database with secure hashing
- Support for future integration with military LDAP/Active Directory if required
- Configurable authentication backends for flexibility

### Q2: How should user sessions be managed in the desktop application context?

**RECOMMENDATION: In-Memory Session Management with Database Persistence**

**Session Strategy:**
```python
class SessionManager:
    def __init__(self):
        self.current_session = None  # In-memory active session
        self.session_timeout = 8 * 60 * 60  # 8 hours default
        self.activity_timer = QTimer()  # Track user activity

    def create_session(self, user):
        """Create session with database persistence for audit."""
        session_id = str(uuid.uuid4())
        # Store in database for audit trail
        # Keep active session in memory for performance

    def validate_session(self):
        """Validate session timeout and activity."""
        # Check last activity time
        # Prompt for re-authentication if needed
        # Update activity timestamp
```

**Features:**
- **Automatic Timeout**: Configurable session timeout (default: 8 hours)
- **Activity Tracking**: Reset timeout on user interaction
- **Graceful Expiration**: Warning before session expires
- **Audit Trail**: All session events logged to database
- **Single Session**: One active session per application instance

### Q3: Should there be an administrator role for managing users and permissions?

**RECOMMENDATION: Three-Tier Role System**

**Role Hierarchy:**
1. **Administrator**: Full system access + user management
2. **Read-Write**: Full CRUD operations on data
3. **Read-Only**: View-only access to all data

**Administrator Capabilities:**
- Create, edit, disable user accounts
- Assign and modify user roles
- View audit logs and user activity
- Reset user passwords
- Configure system settings
- Export/import user data

**Implementation:**
```python
ROLE_PERMISSIONS = {
    'Administrator': {
        'read': True, 'write': True, 'delete': True,
        'user_management': True, 'system_config': True,
        'audit_access': True, 'export_users': True
    },
    'Read-Write': {
        'read': True, 'write': True, 'delete': True,
        'user_management': False, 'system_config': False,
        'audit_access': False, 'export_users': False
    },
    'Read-Only': {
        'read': True, 'write': False, 'delete': False,
        'user_management': False, 'system_config': False,
        'audit_access': False, 'export_users': False
    }
}
```

### Q4: How should this integrate with existing database corruption prevention measures?

**RECOMMENDATION: Enhanced Transaction Management with User Context**

**Integration Strategy:**
```python
class AuthenticatedDatabaseManager:
    def __init__(self, session_manager):
        self.session_manager = session_manager

    def execute_with_audit(self, query, params=None, operation_type='READ'):
        """Execute database operation with user context and audit."""
        user = self.session_manager.current_user

        # Check permissions
        if operation_type in ['INSERT', 'UPDATE', 'DELETE']:
            if not user.has_permission('write'):
                raise PermissionError("Write access denied")

        # Execute with transaction safety
        with get_db_connection() as conn:
            try:
                # Log operation start
                audit_log.log_operation_start(user.user_id, operation_type, query)

                # Execute operation
                result = conn.execute(query, params or [])

                # Log successful completion
                audit_log.log_operation_success(user.user_id, operation_type, result.rowcount)

                return result

            except Exception as e:
                # Log failure
                audit_log.log_operation_failure(user.user_id, operation_type, str(e))
                raise
```

**Enhanced Corruption Prevention:**
- **User-Aware Transactions**: All database operations include user context
- **Permission-Based Access**: Prevent unauthorized data modifications
- **Enhanced Audit Trail**: Track all operations with user attribution
- **Rollback Capabilities**: User-specific operation rollback
- **Concurrent Access Control**: Prevent conflicting operations between users

---

## 🔧 ButtonStateManager Integration Details

### Enhanced Button State Logic
```python
class ButtonStateContext:
    """Enhanced context with role awareness."""
    EMPTY = "empty"
    VIEWING = "viewing"
    EDITING = "editing"
    CREATING = "creating"
    MULTI_SELECT = "multi_select"
    READ_ONLY_MODE = "read_only_mode"  # NEW: For read-only users

class ButtonStateManager(QObject):
    def set_context(self, context, **kwargs):
        """Enhanced context setting with role checking."""
        user = self.session_manager.current_user if self.session_manager else None

        # Force read-only context for read-only users
        if user and not user.has_permission('write'):
            if context in [ButtonStateContext.EDITING, ButtonStateContext.CREATING]:
                context = ButtonStateContext.READ_ONLY_MODE

        # Apply context-specific button states
        for button_name, button_info in self.buttons.items():
            enabled = self._calculate_button_state(context, button_info, user)
            button_info['button'].setEnabled(enabled)

    def _calculate_button_state(self, context, button_info, user):
        """Calculate if button should be enabled based on context and permissions."""
        button_type = button_info['type']
        required_permission = button_info.get('required_permission')

        # Check user permissions first
        if user and required_permission:
            if not user.has_permission(required_permission):
                return False

        # Apply context rules
        return self._get_context_rules(context).get(button_type, False)
```

### Widget Integration Pattern
```python
class AuthenticatedWidget(QWidget):
    """Base class for widgets with authentication support."""

    def __init__(self, parent=None, session_manager=None):
        super().__init__(parent)
        self.session_manager = session_manager
        self.button_manager = ButtonStateManager(self, session_manager)
        self.setup_ui()
        self.setup_authentication()

    def setup_authentication(self):
        """Configure authentication-aware UI elements."""
        # Register buttons with permissions
        self.button_manager.register_button(
            "add", self.add_button, ButtonType.CREATE,
            required_permission='write'
        )
        self.button_manager.register_button(
            "edit", self.edit_button, ButtonType.EDIT,
            required_permission='write'
        )
        self.button_manager.register_button(
            "delete", self.delete_button, ButtonType.DELETE,
            required_permission='delete'
        )

        # Set initial context based on user role
        if self.session_manager.has_permission('write'):
            self.button_manager.set_context(ButtonStateContext.EMPTY)
        else:
            self.button_manager.set_context(ButtonStateContext.READ_ONLY_MODE)
```

---

## 📈 Performance Impact Analysis

### Expected Performance Metrics
- **Login Time**: < 2 seconds for authentication
- **Session Validation**: < 50ms per check
- **Permission Checking**: < 10ms per operation
- **Database Overhead**: < 5% increase in query time
- **Memory Usage**: < 10MB additional for session management

### Optimization Strategies
- **Permission Caching**: Cache user permissions in memory
- **Lazy Loading**: Load user details only when needed
- **Efficient Queries**: Optimized database queries for authentication
- **Session Pooling**: Reuse session objects where possible

---

**IMPLEMENTATION STATUS**: Design Complete - Ready for Development Phase
**NEXT STEP**: Begin implementation with database schema creation and core authentication service development.
