#!/usr/bin/env python3
"""
Database Query Optimization Test
Tests the N+1 query pattern fixes in fluids_widget.py and repairs_widget.py
"""

import sys
import os
import time
import re
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class QueryMonitor:
    """Monitor database queries to detect N+1 patterns."""
    
    def __init__(self):
        self.queries = []
        self.original_execute_query = None
        self.monitoring = False
    
    def start_monitoring(self):
        """Start monitoring database queries."""
        try:
            import database
            self.original_execute_query = database.execute_query
            
            def monitored_execute_query(query, params=None):
                if self.monitoring:
                    self.queries.append({
                        'query': query,
                        'params': params,
                        'timestamp': time.time()
                    })
                return self.original_execute_query(query, params)
            
            database.execute_query = monitored_execute_query
            self.monitoring = True
            print("✅ Database query monitoring started")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start query monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop monitoring and restore original function."""
        if self.original_execute_query:
            import database
            database.execute_query = self.original_execute_query
            self.monitoring = False
            print("✅ Database query monitoring stopped")
    
    def reset_queries(self):
        """Reset query log."""
        self.queries = []
    
    def get_query_count(self):
        """Get total number of queries executed."""
        return len(self.queries)
    
    def analyze_queries(self):
        """Analyze queries for patterns."""
        if not self.queries:
            return {
                'total_queries': 0,
                'equipment_queries': 0,
                'fluid_queries': 0,
                'overhaul_queries': 0,
                'n1_pattern_detected': False
            }
        
        equipment_queries = 0
        fluid_queries = 0
        overhaul_queries = 0
        
        for query_info in self.queries:
            query = query_info['query'].lower()
            
            if 'equipment' in query:
                equipment_queries += 1
            if 'fluid' in query:
                fluid_queries += 1
            if 'overhaul' in query:
                overhaul_queries += 1
        
        # Detect N+1 pattern (multiple equipment queries)
        n1_pattern = equipment_queries > 1
        
        return {
            'total_queries': len(self.queries),
            'equipment_queries': equipment_queries,
            'fluid_queries': fluid_queries,
            'overhaul_queries': overhaul_queries,
            'n1_pattern_detected': n1_pattern,
            'queries': self.queries
        }

def test_fluids_widget_optimization():
    """Test fluids widget query optimization."""
    print("\n🔬 Testing Fluids Widget Query Optimization")
    print("=" * 50)
    
    monitor = QueryMonitor()
    
    try:
        # Start monitoring
        if not monitor.start_monitoring():
            return False
        
        # Import and create fluids widget
        from ui.fluids_widget import FluidsWidget
        
        print("📊 Creating FluidsWidget and loading data...")
        
        # Reset query count
        monitor.reset_queries()
        
        # Create widget and load data
        fluids_widget = FluidsWidget()
        
        # Test the optimized load_data method
        start_time = time.time()
        fluids_widget.load_data()
        end_time = time.time()
        
        load_time = end_time - start_time
        
        # Analyze queries
        analysis = monitor.analyze_queries()
        
        print(f"⏱️  Data loading completed in {load_time:.3f} seconds")
        print(f"📊 Total database queries: {analysis['total_queries']}")
        print(f"🔍 Equipment queries: {analysis['equipment_queries']}")
        print(f"🔍 Fluid queries: {analysis['fluid_queries']}")
        
        # Expected: 1 equipment query (from Fluid.get_all() JOIN)
        # Before optimization: 2 queries (Fluid.get_all() + Equipment.get_active())
        
        expected_equipment_queries = 1
        optimization_successful = analysis['equipment_queries'] <= expected_equipment_queries
        
        print(f"\n📈 OPTIMIZATION ANALYSIS:")
        print("-" * 30)
        print(f"Expected Equipment Queries: ≤{expected_equipment_queries}")
        print(f"Actual Equipment Queries: {analysis['equipment_queries']}")
        print(f"N+1 Pattern Detected: {'❌ YES' if analysis['n1_pattern_detected'] else '✅ NO'}")
        print(f"Optimization Successful: {'✅ YES' if optimization_successful else '❌ NO'}")
        
        # Check for optimized method
        has_optimized_method = hasattr(fluids_widget, 'load_equipment_data_optimized')
        print(f"Optimized Method Present: {'✅ YES' if has_optimized_method else '❌ NO'}")
        
        # Query details
        if analysis['queries']:
            print(f"\n📋 QUERY DETAILS:")
            print("-" * 30)
            for i, query_info in enumerate(analysis['queries'], 1):
                query = query_info['query']
                # Truncate long queries
                if len(query) > 100:
                    query = query[:97] + "..."
                print(f"{i}. {query}")
        
        return optimization_successful and has_optimized_method
        
    except Exception as e:
        print(f"❌ Fluids widget test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        monitor.stop_monitoring()

def test_repairs_widget_optimization():
    """Test repairs widget query optimization."""
    print("\n🔬 Testing Repairs Widget Query Optimization")
    print("=" * 50)
    
    monitor = QueryMonitor()
    
    try:
        # Start monitoring
        if not monitor.start_monitoring():
            return False
        
        # Import repairs widget components
        from ui.repairs_widget import RepairsWidget
        
        print("📊 Creating RepairsWidget and testing overhaul sub-widget...")
        
        # Create repairs widget
        repairs_widget = RepairsWidget()
        
        # Get first overhaul sub-widget for testing
        if repairs_widget.tab_widget.count() > 0:
            sub_widget = repairs_widget.tab_widget.widget(0)
            
            # Reset query count
            monitor.reset_queries()
            
            # Test the optimized load_data method
            start_time = time.time()
            sub_widget.load_data()
            end_time = time.time()
            
            load_time = end_time - start_time
            
            # Analyze queries
            analysis = monitor.analyze_queries()
            
            print(f"⏱️  Data loading completed in {load_time:.3f} seconds")
            print(f"📊 Total database queries: {analysis['total_queries']}")
            print(f"🔍 Equipment queries: {analysis['equipment_queries']}")
            print(f"🔍 Overhaul queries: {analysis['overhaul_queries']}")
            
            # Expected: 1 overhaul query (includes equipment data via JOIN)
            # Before optimization: 2 queries (Overhaul.get_all() + Equipment.get_active())
            
            expected_equipment_queries = 0  # Should extract from overhaul data
            optimization_successful = analysis['equipment_queries'] <= expected_equipment_queries
            
            print(f"\n📈 OPTIMIZATION ANALYSIS:")
            print("-" * 30)
            print(f"Expected Equipment Queries: ≤{expected_equipment_queries}")
            print(f"Actual Equipment Queries: {analysis['equipment_queries']}")
            print(f"N+1 Pattern Detected: {'❌ YES' if analysis['n1_pattern_detected'] else '✅ NO'}")
            print(f"Optimization Successful: {'✅ YES' if optimization_successful else '❌ NO'}")
            
            # Query details
            if analysis['queries']:
                print(f"\n📋 QUERY DETAILS:")
                print("-" * 30)
                for i, query_info in enumerate(analysis['queries'], 1):
                    query = query_info['query']
                    # Truncate long queries
                    if len(query) > 100:
                        query = query[:97] + "..."
                    print(f"{i}. {query}")
            
            return optimization_successful
        else:
            print("❌ No overhaul sub-widgets found")
            return False
        
    except Exception as e:
        print(f"❌ Repairs widget test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        monitor.stop_monitoring()

def test_code_analysis():
    """Analyze code to verify N+1 optimizations."""
    print("\n🔍 Code Analysis - Verifying N+1 Query Optimizations")
    print("=" * 60)
    
    results = {}
    
    # Test fluids_widget.py
    try:
        with open('ui/fluids_widget.py', 'r', encoding='utf-8') as f:
            fluids_content = f.read()
        
        # Check for optimized method
        has_optimized_method = 'load_equipment_data_optimized' in fluids_content
        
        # Check for redundant Equipment.get_active() calls
        equipment_get_active_count = fluids_content.count('Equipment.get_active()')
        
        # Check for optimization comments
        has_optimization_comments = 'optimized' in fluids_content.lower()
        
        results['fluids_widget'] = {
            'optimized_method_present': has_optimized_method,
            'equipment_get_active_calls': equipment_get_active_count,
            'optimization_comments': has_optimization_comments,
            'optimized': has_optimized_method and equipment_get_active_count <= 1
        }
        
        print(f"📄 fluids_widget.py analysis:")
        print(f"  Optimized method present: {'✅' if has_optimized_method else '❌'}")
        print(f"  Equipment.get_active() calls: {equipment_get_active_count}")
        print(f"  Optimization comments: {'✅' if has_optimization_comments else '❌'}")
        
    except Exception as e:
        print(f"❌ Failed to analyze fluids_widget.py: {e}")
        results['fluids_widget'] = {'optimized': False}
    
    # Test repairs_widget.py
    try:
        with open('ui/repairs_widget.py', 'r', encoding='utf-8') as f:
            repairs_content = f.read()
        
        # Check for async loading implementation
        has_async_loading = 'load_other_tabs_async' in repairs_content
        
        # Check for AsyncDataLoader usage
        has_async_data_loader = 'AsyncDataLoader' in repairs_content
        
        # Check for equipment data extraction optimization
        has_equipment_extraction = 'equipment_ids_seen' in repairs_content
        
        # Check for Equipment.get_active() reduction
        equipment_get_active_count = repairs_content.count('Equipment.get_active()')
        
        results['repairs_widget'] = {
            'async_loading_present': has_async_loading,
            'async_data_loader_used': has_async_data_loader,
            'equipment_extraction_optimized': has_equipment_extraction,
            'equipment_get_active_calls': equipment_get_active_count,
            'optimized': has_async_loading and has_equipment_extraction
        }
        
        print(f"\n📄 repairs_widget.py analysis:")
        print(f"  Async loading present: {'✅' if has_async_loading else '❌'}")
        print(f"  AsyncDataLoader used: {'✅' if has_async_data_loader else '❌'}")
        print(f"  Equipment extraction optimized: {'✅' if has_equipment_extraction else '❌'}")
        print(f"  Equipment.get_active() calls: {equipment_get_active_count}")
        
    except Exception as e:
        print(f"❌ Failed to analyze repairs_widget.py: {e}")
        results['repairs_widget'] = {'optimized': False}
    
    # Overall assessment
    overall_optimized = (
        results.get('fluids_widget', {}).get('optimized', False) and
        results.get('repairs_widget', {}).get('optimized', False)
    )
    
    print(f"\n🎯 OVERALL CODE ANALYSIS:")
    print("-" * 30)
    print(f"Fluids Widget Optimized: {'✅' if results.get('fluids_widget', {}).get('optimized', False) else '❌'}")
    print(f"Repairs Widget Optimized: {'✅' if results.get('repairs_widget', {}).get('optimized', False) else '❌'}")
    print(f"Overall Optimization: {'✅ SUCCESS' if overall_optimized else '❌ INCOMPLETE'}")
    
    return overall_optimized

def main():
    """Run database optimization tests."""
    print("🚀 Database Query Optimization Test Suite")
    print("Testing Priority 1 Optimization: N+1 Query Pattern Fixes")
    print("=" * 70)
    
    # Test 1: Code Analysis
    print("1️⃣  CODE ANALYSIS")
    code_analysis_passed = test_code_analysis()
    
    # Test 2: Fluids Widget Optimization
    print("\n2️⃣  FLUIDS WIDGET TESTING")
    fluids_test_passed = test_fluids_widget_optimization()
    
    # Test 3: Repairs Widget Optimization
    print("\n3️⃣  REPAIRS WIDGET TESTING")
    repairs_test_passed = test_repairs_widget_optimization()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    print(f"Code Analysis: {'✅ PASS' if code_analysis_passed else '❌ FAIL'}")
    print(f"Fluids Widget Test: {'✅ PASS' if fluids_test_passed else '❌ FAIL'}")
    print(f"Repairs Widget Test: {'✅ PASS' if repairs_test_passed else '❌ FAIL'}")
    
    overall_success = code_analysis_passed and fluids_test_passed and repairs_test_passed
    print(f"\nOverall Result: {'🏆 SUCCESS' if overall_success else '⚠️  ISSUES DETECTED'}")
    
    if overall_success:
        print("\n🎉 Priority 1 Database Optimization Successfully Validated!")
        print("   • N+1 query patterns eliminated")
        print("   • 50%+ reduction in redundant database calls")
        print("   • Async loading implemented for UI responsiveness")
    else:
        print("\n⚠️  Issues detected - review optimization implementation")
    
    return overall_success

if __name__ == "__main__":
    main()
