#!/usr/bin/env python3
"""
PROJECT-ALPHA User Registration Integration Tests
Tests the complete registration workflow integration with the main application.
"""

import sys
import os
import tempfile
import shutil
import sqlite3
from unittest.mock import patch, MagicMock
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_test_environment():
    """Setup a clean test environment with temporary database."""
    print("🔧 Setting up test environment...")
    
    # Create temporary directory for test database
    test_dir = tempfile.mkdtemp(prefix="project_alpha_test_")
    test_db_path = os.path.join(test_dir, "test_inventory.db")
    
    # Set environment variable to use test database
    os.environ['DATABASE_PATH'] = test_db_path
    
    print(f"✅ Test database created at: {test_db_path}")
    return test_dir, test_db_path

def cleanup_test_environment(test_dir):
    """Clean up test environment."""
    try:
        shutil.rmtree(test_dir)
        if 'DATABASE_PATH' in os.environ:
            del os.environ['DATABASE_PATH']
        print("✅ Test environment cleaned up")
    except Exception as e:
        print(f"⚠️ Error cleaning up test environment: {e}")

def test_database_initialization():
    """Test that database initializes correctly with authentication tables."""
    print("\n🔍 Testing database initialization...")
    
    try:
        import database
        success, first_run = database.init_db()
        
        if not success:
            print("❌ Database initialization failed")
            return False
        
        print(f"✅ Database initialized successfully (first_run: {first_run})")
        
        # Test authentication service initialization
        from auth.authentication_service import AuthenticationService
        user_count = AuthenticationService.count_total_users()
        print(f"✅ Authentication service working - User count: {user_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database initialization error: {e}")
        return False

def test_open_registration_scenario():
    """Test open registration when no users exist."""
    print("\n🔍 Testing open registration scenario...")

    try:
        from auth.authentication_service import AuthenticationService
        from auth.login_dialog import LoginDialog
        from PyQt5.QtWidgets import QApplication

        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # Check current user count
        user_count = AuthenticationService.count_total_users()
        print(f"Current user count: {user_count}")

        # Test with current state (users exist - should be restricted)
        login_dialog = LoginDialog()
        signup_available = login_dialog.check_signup_availability()

        if user_count == 0:
            if not signup_available:
                print("❌ Signup should be available when no users exist")
                return False
            print("✅ Signup correctly available for open registration")
        else:
            if signup_available:
                print("❌ Signup should not be available when users exist")
                return False
            print("✅ Signup correctly restricted when users exist")

        return True

    except Exception as e:
        print(f"❌ Open registration test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_database_scenario():
    """Test open registration with completely empty database."""
    print("\n🔍 Testing empty database scenario...")

    try:
        # Clear all users from database to test open registration
        import database
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM users")
            conn.commit()

        from auth.authentication_service import AuthenticationService
        from auth.login_dialog import LoginDialog
        from PyQt5.QtWidgets import QApplication

        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # Check user count after clearing
        user_count = AuthenticationService.count_total_users()
        print(f"User count after clearing: {user_count}")

        if user_count != 0:
            print("❌ Failed to clear users from database")
            return False

        # Test signup availability with empty database
        login_dialog = LoginDialog()
        signup_available = login_dialog.check_signup_availability()

        if not signup_available:
            print("❌ Signup should be available when no users exist")
            return False

        print("✅ Signup correctly available for empty database (open registration)")

        # Test first user registration
        success, message, user_id = AuthenticationService.register_user(
            username="firstuser",
            password="FirstPass123!",
            full_name="First User",
            email="<EMAIL>",
            role_name="Administrator"  # First user should be admin
        )

        if not success:
            print(f"❌ First user registration failed: {message}")
            return False

        print(f"✅ First user registered successfully - ID: {user_id}")

        # Test that signup is now restricted
        login_dialog2 = LoginDialog()
        signup_available2 = login_dialog2.check_signup_availability()

        if signup_available2:
            print("❌ Signup should be restricted after first user is created")
            return False

        print("✅ Signup correctly restricted after first user creation")

        return True

    except Exception as e:
        print(f"❌ Empty database test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_registration_process():
    """Test the complete user registration process."""
    print("\n🔍 Testing user registration process...")
    
    try:
        from auth.authentication_service import AuthenticationService
        import time
        
        # Generate unique username
        unique_suffix = str(int(time.time() * 1000))[-6:]
        test_username = f"testuser{unique_suffix}"
        
        # Test user registration
        success, message, user_id = AuthenticationService.register_user(
            username=test_username,
            password="TestPass123!",
            full_name="Integration Test User",
            email="<EMAIL>",
            role_name="Read-Only"
        )
        
        if not success:
            print(f"❌ User registration failed: {message}")
            return False
        
        print(f"✅ User registered successfully - ID: {user_id}")
        
        # Test authentication of newly registered user
        user = AuthenticationService.authenticate_user(test_username, "TestPass123!")
        if not user:
            print("❌ Newly registered user cannot authenticate")
            return False
        
        print(f"✅ Newly registered user can authenticate: {user.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ User registration test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signup_dialog_functionality():
    """Test SignUpDialog creation and functionality."""
    print("\n🔍 Testing SignUpDialog functionality...")
    
    try:
        from auth.signup_dialog import SignUpDialog
        from PyQt5.QtWidgets import QApplication
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test open registration mode
        dialog = SignUpDialog(parent=None, current_user=None, is_admin_mode=False)
        if not dialog:
            print("❌ Failed to create SignUpDialog in open registration mode")
            return False
        
        print("✅ SignUpDialog created successfully (open registration mode)")
        
        # Test admin mode
        mock_user = MagicMock()
        mock_user.role_name = "Administrator"
        mock_user.user_id = 1
        
        admin_dialog = SignUpDialog(parent=None, current_user=mock_user, is_admin_mode=True)
        if not admin_dialog:
            print("❌ Failed to create SignUpDialog in admin mode")
            return False
        
        print("✅ SignUpDialog created successfully (admin mode)")
        
        return True
        
    except Exception as e:
        print(f"❌ SignUpDialog test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_management_dialog():
    """Test UserManagementDialog functionality."""
    print("\n🔍 Testing UserManagementDialog functionality...")
    
    try:
        from auth.user_management_dialog import UserManagementDialog
        from PyQt5.QtWidgets import QApplication
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create mock admin user
        mock_user = MagicMock()
        mock_user.role_name = "Administrator"
        mock_user.user_id = 1
        mock_user.username = "admin"
        
        # Test dialog creation
        dialog = UserManagementDialog(parent=None, current_user=mock_user)
        if not dialog:
            print("❌ Failed to create UserManagementDialog")
            return False
        
        print("✅ UserManagementDialog created successfully")
        
        # Test non-admin user rejection
        mock_non_admin = MagicMock()
        mock_non_admin.role_name = "Read-Only"
        
        try:
            UserManagementDialog(parent=None, current_user=mock_non_admin)
            print("❌ Non-admin user should be rejected")
            return False
        except ValueError:
            print("✅ Non-admin user correctly rejected")
        
        return True
        
    except Exception as e:
        print(f"❌ UserManagementDialog test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_integration_tests():
    """Run all integration tests."""
    print("🚀 Starting User Registration Integration Tests")
    print("=" * 60)
    
    test_dir = None
    try:
        # Setup test environment
        test_dir, test_db_path = setup_test_environment()
        
        # Run tests
        tests = [
            test_database_initialization,
            test_open_registration_scenario,
            test_empty_database_scenario,
            test_user_registration_process,
            test_signup_dialog_functionality,
            test_user_management_dialog
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                if test():
                    passed += 1
                    print("✅ PASSED")
                else:
                    failed += 1
                    print("❌ FAILED")
            except Exception as e:
                print(f"❌ Test {test.__name__} crashed: {e}")
                failed += 1
            print("-" * 40)
        
        print(f"\n📊 Integration Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All integration tests passed!")
            return True
        else:
            print("⚠️ Some tests failed. Check the output above for details.")
            return False
            
    finally:
        if test_dir:
            cleanup_test_environment(test_dir)

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
